{
	"folders": [
		{
			"name": "CRRC-TBOX-ROOT",
			"path": "."
		},
		{
			"name": "CRRC-TBOX-MCU_BOOT",
			"path": "mcu_boot"
		},
		{
			"name": "CRRC-TBOX-MCU_APP",
			"path": "mcu_code"
		},
		{
			"name": "CRRC-TBOX-ARM",
			"path": "tbox_platform-master"
		}
	],
	"settings": {
		"clangd.arguments": [
			// 在后台自动分析文件（基于 compile_commands）
			"--background-index",
			// 标记 compile_commands.json 文件的目录位置
			"--compile-commands-dir=${workspaceFolder}/build",
			// 同时开启的任务数量
			"-j=12",
			// 全局补全（会自动补充头文件）
			"--all-scopes-completion",
			// 更详细的补全内容
			"--completion-style=detailed",
			// Include what you use（根据使用情况插入头文件）
			"--header-insertion=iwyu",
			// PCH 优化的位置（memory 表示存储在内存中）
			"--pch-storage=memory",
			// clang-format 默认样式，当没有 .clang-format 文件时应用
			"--fallback-style=WebKit",
			// 美化 JSON 输出
			"--pretty",
			// 启用 clang-tidy 诊断
			"--clang-tidy",
			// 白名单允许执行的编译器路径，用于自动探测系统头文件路径，可以用逗号分隔多个
			"--query-driver=${env:ARM_TOOLCHAIN}/bin/arm-none-eabi-gcc"
		],
		"clangd.fallbackFlags": [
			"-std=c99",
		],
		"clangd.serverCompletionRanking": true,
		"editorjumper.selectedIDE": "CLion",
	}
}
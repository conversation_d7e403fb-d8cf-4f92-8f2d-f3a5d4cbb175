# 忽略编辑器和 IDE 产生的文件
*.swp
*.swo
.vscode/
.idea/
.vs/
*.sublime-workspace
.vscode-ctags

# 忽略日志文件
*.log

# 忽略临时文件
*.tmp
*.bak

# NOTE:
# 1.多个gitignore文件的作用机制
# 在一个仓库中可以有多个 .gitignore 文件。
# 每个 .gitignore 文件的作用范围是其所在目录及其子目录。
# Git 会从根目录开始，逐级向下应用 .gitignore 文件中的规则。
# 例如：
# 根目录的 .gitignore 文件会应用于整个仓库。
# 子目录中的 .gitignore 文件会覆盖或补充根目录中的规则，只在该子目录及其子目录中生效。
#
# 本文件中的规则会应用于整个仓库，只设置基本的忽略规则，
# 具体需要忽略的产物等规则应在各自的 .gitignore 文件中设置。

arm_code/config.h.in~
arm_code/config.status
arm_code/config.h.in
arm_code/stamp-h1
tbox_platform-master/GPATH
tbox_platform-master/GRTAGS
tbox_platform-master/GTAGS
tbox_platform-master/platform/DiagValidation/ParseSecurity - 副本.h
tbox_platform-master/.cache/
tbox_platform-master/tools/Fota/UserApp_src.bin
tbox_platform-master/tools/Fota/upgrade.zip
tbox_platform-master/platform/tsp/CrrcCanDecode0.xml
tbox_platform-master/.vscode-ctags.next
mcu_code/tags
tbox_platform-master/cmake_build.sh.wuling
tbox_platform-master/tools/Fota/mcu_app.bin
mcu_code/ARM_POWER_ON_FIX_README.md
tbox_platform-master/basic/upgrade/README.md
tbox_platform-master/platform/ipc.tar.gz
tbox_platform-master/basic/upgrade.tar.gz
tbox_platform-master/.cursor/rules/review-gate.mdc
mcu_boot/README.md
mcu_code/UserApp/app/ReadMe.md
tbox_platform-master/final_review_gate.py
tbox_platform-master/.cursor/rules/interactive.mdc
mcu_boot/.cursor/
mcu_code/.cursor/
tbox_platform-master/.cursor/

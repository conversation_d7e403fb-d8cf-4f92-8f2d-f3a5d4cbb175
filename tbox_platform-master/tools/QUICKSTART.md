# TBox升级包制作工具 - 快速开始

## 🚀 30秒快速上手

```bash
# 制作升级包（默认ARM+MCU组合，debug模式）
./tools/upgrade_builder.sh

# 仅制作ARM升级包（debug模式）
./tools/upgrade_builder.sh -t arm

# 仅制作MCU升级包（release模式）
./tools/upgrade_builder.sh -t mcu -m release

# 制作组合升级包（debug模式）
./tools/upgrade_builder.sh -t both -m debug
```

## 📋 前置条件

**ARM升级**: 先运行 `./cmake_build.sh -d` (debug) 或 `./cmake_build.sh -r` (release) 完成编译

**MCU升级**: 安装交叉编译器 `sudo apt-get install gcc-arm-none-eabi cmake ninja-build`

## 📦 输出文件

升级包会生成在 `build/upgrade_packages/` 目录，按时间戳和类型组织：

```text
build/upgrade_packages/
└── 20250716_1020/                # 时间戳目录
    ├── ARM/                      # ARM升级包
    │   ├── M42/upgrade.zip       # M42 ARM升级包
    │   └── M22/upgrade.zip       # M22 ARM升级包
    ├── MCU/upgrade.zip           # MCU升级包（仅MCU升级时）
    ├── ARM+MCU/                  # 组合升级包（组合升级时）
    │   ├── M42/upgrade.zip       # M42 ARM+MCU组合升级包
    │   └── M22/upgrade.zip       # M22 ARM+MCU组合升级包
    └── upgrade_summary_v1.0.0.txt # 升级信息摘要（包含编译模式等信息）
```

**统一命名**: 所有升级包都命名为 `upgrade.zip`，通过目录结构区分类型！

## 📚 完整文档

详细说明请查看: [README_upgrade_builder.md](./README_upgrade_builder.md)

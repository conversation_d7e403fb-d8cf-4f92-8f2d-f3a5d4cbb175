[upgradeType]
;type:0为TBOX升级，1为ECU升级
Type = 0;

;密码使能,1为加密，0为不加密
PasswordEnable = 0;

;用来解密升级文件,密码长度为6
Password = xxxxxx;

[tboxRef]
;ARM升级使能,1为升级，0为不升级
ArmEnable = 1;

;ARM升级文件个数
ArmFileCount = 1;

;ARM升级文件名和文件路径(/tbox/ota/tmp为压缩包解压路径，若压缩包内带路径应在后面加上)
ArmFileName_1 = upgrade.package;
ArmFilePath_1 = /tbox/ota/tmp/;

;ARM脚本升级使能,1为升级，0为不升级
ArmScript = 0;

;ARM脚本升级文件个数
ArmScriptCount = 0;

;ARM脚本升级文件名和目标路径
ArmScriptFile_1 = xxxx;
ArmScriptPath_1 = xxxx;

;MCU升级使能，1为升级，0为不升级
MCUEnable = 0;

;MCU升级文件名和文件路径(/tbox/ota/tmp为压缩包解压路径，若压缩包内带路径应在后面加上)
MCUFile = UserApp_src.bin;
MCUFilePath = /tbox/ota/tmp/;

;蓝牙升级使能，1为升级，0为不升级
BTEnable = 0;

;蓝牙升级文件名
BTFile = xxxx;

BTSTBEnable = 0;

[ECURef]
;ECU 类型
;0: AFS 1: BCM 2: APU 3: BMS 4: EDCM 5: EPB 6: ICM 7: MCS 8: SCM 9: VMS
ECUType = 0;

;ECU Driver升级使能，1为升级，0为不升级
ECUDriverEnable = 0;

;ECUDriver升级文件名
ECUDriverFile = xxxx;

;ECU APP升级使能，1为升级，0为不升级
ECUAPPEnable = 0
;ECUAPP升级文件名
ECUAPPFile = xxxx;

;等待时间(ms)
WaitTime = 1000;

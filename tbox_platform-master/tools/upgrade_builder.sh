#!/bin/bash

# TBox升级包制作工具
# 支持ARM、MCU单独升级或组合升级
# 简化配置，提供一键生成升级包功能

set -e

# 定义关键变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FOTA_TOOLS_DIR="${SCRIPT_DIR}/Fota"
PROJECT_ROOT="$(dirname "${SCRIPT_DIR}")"
MCU_APP_DIR="${PROJECT_ROOT}/../mcu_code"
MCU_BOOT_DIR="${PROJECT_ROOT}/../mcu_boot"
BUILD_DIR="${PROJECT_ROOT}/build"

# 默认配置
DEFAULT_VERSION="$(date +%Y%m%d%H%M)"
UPGRADE_TYPE="both" # arm, mcu, both
BUILD_MODE="debug" # debug, release
SYSTEM_UPGRADE=false
AUTO_BUILD=true
OUTPUT_DIR="${BUILD_DIR}/upgrade_packages"
TEMP_DIR="${BUILD_DIR}/upgrade_temp"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 获取文件大小
get_file_size() {
    du -h "$1" | cut -f1
}

# 获取文件MD5
get_file_md5() {
    md5sum "$1" | cut -d' ' -f1
}

# 显示帮助信息
show_help() {
    cat <<EOF
TBox升级包制作工具

用法: $0 [选项]

选项:
  -t, --type TYPE        升级类型: arm, mcu, both (默认: both)
  -v, --version VERSION  版本号 (默认: 当前时间戳)
  -m, --mode MODE        编译模式: debug, release (默认: debug)
  -s, --system          包含系统分区升级 (仅ARM升级时有效)
  -o, --output DIR      输出目录 (默认: ${BUILD_DIR}/upgrade_packages)
  --no-auto-build       禁用自动编译功能
  -h, --help            显示此帮助信息

升级类型说明:
  arm   - 仅制作ARM升级包 (缺少编译产物时会自动编译)
          生成 M42/upgrade.zip 和 M22/upgrade.zip
  mcu   - 仅制作MCU升级包 (自动编译MCU代码)
          生成 MCU/upgrade.zip
  both  - 制作ARM+MCU组合升级包 (自动编译所需组件)
          生成 ARM+MCU/M42/upgrade.zip 和 ARM+MCU/M22/upgrade.zip

示例:
  $0                                   # 制作ARM+MCU组合升级包 (debug模式)
  $0 -t arm -s                         # 制作包含系统的ARM升级包 (debug模式)
  $0 -t mcu -v v1.2.3 -m release      # 制作MCU升级包，指定版本号和release模式
  $0 -t both -v v2.0.0 -m debug       # 制作组合升级包到指定目录 (debug模式)
  $0 -m release --no-auto-build       # 禁用自动编译功能，使用release模式

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
        -t | --type)
            UPGRADE_TYPE="$2"
            shift 2
            ;;
        -v | --version)
            DEFAULT_VERSION="$2"
            shift 2
            ;;
        -m | --mode)
            BUILD_MODE="$2"
            shift 2
            ;;
        -s | --system)
            SYSTEM_UPGRADE=true
            shift
            ;;
        -o | --output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --no-auto-build)
            AUTO_BUILD=false
            shift
            ;;
        -h | --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        esac
    done

    # 验证升级类型
    if [[ ! "$UPGRADE_TYPE" =~ ^(arm|mcu|both)$ ]]; then
        log_error "无效的升级类型: $UPGRADE_TYPE"
        exit 1
    fi

    # 验证编译模式
    if [[ ! "$BUILD_MODE" =~ ^(debug|release)$ ]]; then
        log_error "无效的编译模式: $BUILD_MODE"
        exit 1
    fi
}

# 检查依赖工具
check_dependencies() {
    local missing_tools=()

    # 检查基本工具
    local tools=("zip" "md5sum" "sha256sum")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &>/dev/null; then
            missing_tools+=("$tool")
        fi
    done

    # 检查FOTA工具
    if [[ "$UPGRADE_TYPE" == "arm" || "$UPGRADE_TYPE" == "both" ]]; then
        if [[ ! -f "${FOTA_TOOLS_DIR}/diff_all.sh" ]]; then
            missing_tools+=("FOTA tools")
        fi
        if ! command -v python3 &>/dev/null; then
            missing_tools+=("python3")
        fi
    fi

    # 检查MCU编译工具
    if [[ "$UPGRADE_TYPE" == "mcu" || "$UPGRADE_TYPE" == "both" ]]; then
        if ! command -v arm-none-eabi-gcc &>/dev/null; then
            missing_tools+=("arm-none-eabi-gcc")
        fi
        if ! command -v cmake &>/dev/null; then
            missing_tools+=("cmake")
        fi
        if command -v srec_cat &>/dev/null; then
            log_debug "找到 srec_cat，将支持生成安全固件"
        fi
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少以下依赖工具："
        printf '%s\n' "${missing_tools[@]}"
        exit 1
    fi

    log_info "依赖检查通过"
}

# 自动编译ARM固件
auto_build_arm() {
    log_info "开始自动编译ARM固件..."

    # 检查主编译脚本是否存在
    local main_build_script="${PROJECT_ROOT}/cmake_build.sh"
    if [[ ! -f "$main_build_script" ]]; then
        log_error "找不到主编译脚本: $main_build_script"
        return 1
    fi

    # 执行编译命令
    log_info "执行ARM编译命令（${BUILD_MODE}模式）..."
    log_info "注意：ARM编译可能需要较长时间，请耐心等待..."
    cd "$PROJECT_ROOT" || return 1

    # 根据编译模式选择参数
    local build_arg
    if [[ "$BUILD_MODE" == "debug" ]]; then
        build_arg="-d"
        log_info "使用debug模式编译ARM固件"
    else
        build_arg="-r"
        log_info "使用release模式编译ARM固件"
    fi

    # 执行编译，但不完全依赖返回码
    ./cmake_build.sh "$build_arg"
    local build_exit_code=$?

    # 检查编译产物是否存在来判断编译是否成功
    local fota_bins_dir="${BUILD_DIR}/fota"
    if [[ -d "${fota_bins_dir}/M42bins" ]] && [[ -d "${fota_bins_dir}/M22bins" ]]; then
        local required_files=("uboot.bin" "ap_imagefs.img" "ap_rootfs.img" "ap_caprootfs.img" "cap_oem.img")
        local all_files_exist=true

        for file in "${required_files[@]}"; do
            if [[ ! -f "${fota_bins_dir}/M42bins/$file" ]] || [[ ! -f "${fota_bins_dir}/M22bins/$file" ]]; then
                all_files_exist=false
                break
            fi
        done

        if [[ "$all_files_exist" == "true" ]]; then
            log_info "ARM固件编译完成（通过检查编译产物确认）"
            return 0
        fi
    fi

    # 如果编译产物不完整，报告失败
    if [[ $build_exit_code -ne 0 ]]; then
        log_error "ARM固件编译失败（退出码: $build_exit_code）"
    else
        log_error "ARM固件编译可能成功，但编译产物不完整"
    fi
    return 1
}

# 检查ARM编译环境
check_arm_build_env() {
    local fota_bins_dir="${BUILD_DIR}/fota"

    if [[ ! -d "$fota_bins_dir" ]]; then
        log_warn "未找到ARM编译产物目录: $fota_bins_dir"

        if [[ "$AUTO_BUILD" == "true" ]]; then
            log_info "尝试自动编译ARM固件..."

            if auto_build_arm; then
                log_info "ARM固件自动编译完成，继续检查编译产物..."
            else
                log_error "ARM固件自动编译失败，请手动运行编译脚本"
                return 1
            fi
        else
            log_error "请先运行主编译脚本生成ARM固件: ./cmake_build.sh -r"
            log_error "或者移除 --no-auto-build 选项以启用自动编译"
            return 1
        fi
    fi

    local m42_dir="${fota_bins_dir}/M42bins"
    local m22_dir="${fota_bins_dir}/M22bins"

    if [[ ! -d "$m42_dir" ]] || [[ ! -d "$m22_dir" ]]; then
        log_error "ARM固件目录不完整，请重新编译"
        return 1
    fi

    # 检查必要的固件文件
    local required_files=("uboot.bin" "ap_imagefs.img" "ap_rootfs.img" "ap_caprootfs.img" "cap_oem.img")
    for file in "${required_files[@]}"; do
        if [[ ! -f "${m42_dir}/$file" ]]; then
            log_error "缺少ARM固件文件: $file"
            return 1
        fi
    done

    log_info "ARM编译环境检查通过"
    return 0
}

# 检查MCU编译环境
check_mcu_build_env() {
    if [[ ! -d "$MCU_APP_DIR" ]]; then
        log_error "MCU应用目录不存在: $MCU_APP_DIR"
        return 1
    fi

    if [[ ! -d "$MCU_BOOT_DIR" ]]; then
        log_error "MCU引导目录不存在: $MCU_BOOT_DIR"
        return 1
    fi

    log_info "MCU编译环境检查通过"
    return 0
}

# 编译MCU
build_mcu() {
    local build_dir=$1
    local component_name=$2

    log_info "开始编译 $component_name（${BUILD_MODE}模式）..."

    cd "$build_dir" || {
        log_error "无法进入目录: $build_dir"
        return 1
    }

    # 清理旧的构建目录
    rm -rf build
    mkdir -p build && cd build

    # 根据编译模式设置CMAKE_BUILD_TYPE
    local cmake_build_type
    if [[ "$BUILD_MODE" == "debug" ]]; then
        cmake_build_type="Debug"
    else
        cmake_build_type="Release"
    fi

    # 配置CMake
    log_info "配置 $component_name CMake（${cmake_build_type}）..."
    cmake -DCMAKE_BUILD_TYPE="$cmake_build_type" \
        -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
        -DCMAKE_TOOLCHAIN_FILE="../cmake/gcc.cmake" \
        -DARM_CPU="cortex-m33" \
        -G Ninja .. || {
        log_error "$component_name CMake配置失败"
        return 1
    }

    # 编译
    log_info "编译 $component_name..."
    cmake --build . || {
        log_error "$component_name 编译失败"
        return 1
    }

    log_info "$component_name 编译完成"
    return 0
}

# 制作MCU升级包（带时间戳参数）
create_mcu_package_with_timestamp() {
    local timestamp="$1"
    log_info "开始制作MCU升级包..."

    # 编译MCU应用
    if ! build_mcu "$MCU_APP_DIR" "MCU应用"; then
        return 1
    fi

    # 编译MCU引导
    if ! build_mcu "$MCU_BOOT_DIR" "MCU引导"; then
        return 1
    fi

    local mcu_app_bin="${MCU_APP_DIR}/build/mcu_app.bin"

    # 检查编译产物
    if [[ ! -f "$mcu_app_bin" ]]; then
        log_error "MCU应用二进制文件不存在: $mcu_app_bin"
        return 1
    fi

    local mcu_output_dir="${OUTPUT_DIR}/${timestamp}/MCU"
    mkdir -p "$mcu_output_dir"

    # 制作带MD5的升级文件
    log_info "生成MCU升级文件..."
    local temp_mcu_dir="${TEMP_DIR}/mcu"
    mkdir -p "$temp_mcu_dir"

    # 复制文件到临时目录并重命名
    cp "$mcu_app_bin" "${temp_mcu_dir}/TBox_mcu.bin"

    # 使用SHA256追加脚本处理MCU升级文件
    cd "$temp_mcu_dir"
    if [[ -f "${FOTA_TOOLS_DIR}/sha256append.sh" ]]; then
        # 使用sha256append.sh追加SHA256校验值
        if "${FOTA_TOOLS_DIR}/sha256append.sh" "TBox_mcu.bin" "UserApp_src.bin"; then
            log_info "MCU升级文件已生成（使用SHA256校验）"
        else
            log_error "MCU升级文件生成失败"
            return 1
        fi
    else
        log_error "找不到sha256append.sh脚本: ${FOTA_TOOLS_DIR}/sha256append.sh"
        return 1
    fi

    # 创建MCU升级的iot.ini配置
    cp "${FOTA_TOOLS_DIR}/iot.ini" "iot.ini"

    # 启用MCU升级，禁用ARM升级
    sed -i 's/MCUEnable = 0;/MCUEnable = 1;/g' "iot.ini"
    sed -i 's/ArmEnable = 1;/ArmEnable = 0;/g' "iot.ini"

    log_info "MCU升级配置已生成"

    # 创建upgrade.zip (只包含MCU文件和配置)
    if zip -r "${mcu_output_dir}/upgrade.zip" UserApp_src.bin iot.ini 2>/dev/null; then
        log_info "MCU升级包已生成: ${mcu_output_dir}/upgrade.zip"

        # 输出文件信息
        printf "  MCU/upgrade.zip ($(get_file_size "${mcu_output_dir}/upgrade.zip")) [MD5: $(get_file_md5 "${mcu_output_dir}/upgrade.zip")]\n"
    else
        log_error "MCU升级包创建失败"
        return 1
    fi

    cd "$SCRIPT_DIR"
    return 0
}

# 制作MCU升级包
create_mcu_package() {
    log_info "开始制作MCU升级包..."

    # 编译MCU应用
    if ! build_mcu "$MCU_APP_DIR" "MCU应用"; then
        return 1
    fi

    # 编译MCU引导
    if ! build_mcu "$MCU_BOOT_DIR" "MCU引导"; then
        return 1
    fi

    local mcu_app_bin="${MCU_APP_DIR}/build/mcu_app.bin"

    # 检查编译产物
    if [[ ! -f "$mcu_app_bin" ]]; then
        log_error "MCU应用二进制文件不存在: $mcu_app_bin"
        return 1
    fi

    # 创建MCU升级包目录结构
    local timestamp
    timestamp=$(date +"%Y%m%d_%H%M%S")
    local mcu_output_dir="${OUTPUT_DIR}/${timestamp}/MCU"
    mkdir -p "$mcu_output_dir"

    # 制作带MD5的升级文件
    log_info "生成MCU升级文件..."
    local temp_mcu_dir="${TEMP_DIR}/mcu"
    mkdir -p "$temp_mcu_dir"

    # 复制文件到临时目录并重命名
    cp "$mcu_app_bin" "${temp_mcu_dir}/TBox_mcu.bin"

    # 使用SHA256追加脚本处理MCU升级文件
    cd "$temp_mcu_dir"
    if [[ -f "${FOTA_TOOLS_DIR}/sha256append.sh" ]]; then
        # 使用sha256append.sh追加SHA256校验值
        if "${FOTA_TOOLS_DIR}/sha256append.sh" "TBox_mcu.bin" "UserApp_src.bin"; then
            log_info "MCU升级文件已生成（使用SHA256校验）"
        else
            log_error "MCU升级文件生成失败"
            return 1
        fi
    else
        log_error "找不到sha256append.sh脚本: ${FOTA_TOOLS_DIR}/sha256append.sh"
        return 1
    fi # 创建MCU升级的iot.ini配置
    cp "${FOTA_TOOLS_DIR}/iot.ini" "iot.ini"

    # 启用MCU升级，禁用ARM升级
    sed -i 's/MCUEnable = 0;/MCUEnable = 1;/g' "iot.ini"
    sed -i 's/ArmEnable = 1;/ArmEnable = 0;/g' "iot.ini"

    log_info "MCU升级配置已生成"

    # 创建upgrade.zip (只包含MCU文件和配置)
    if zip -r "${mcu_output_dir}/upgrade.zip" UserApp_src.bin iot.ini 2>/dev/null; then
        log_info "MCU升级包已生成: ${mcu_output_dir}/upgrade.zip"

        # 输出文件信息
        printf "  MCU/upgrade.zip ($(get_file_size "${mcu_output_dir}/upgrade.zip")) [MD5: $(get_file_md5 "${mcu_output_dir}/upgrade.zip")]\n"
    else
        log_error "MCU升级包创建失败"
        return 1
    fi

    cd "$SCRIPT_DIR"
    return 0
}

# 制作ARM升级包（带时间戳参数）
create_arm_package_with_timestamp() {
    local timestamp="$1"
    log_info "开始制作ARM升级包..."

    if ! check_arm_build_env; then
        return 1
    fi

    local fota_bins_dir="${BUILD_DIR}/fota"
    local m42_dir="${fota_bins_dir}/M42bins"
    local m22_dir="${fota_bins_dir}/M22bins"

    local arm_base_dir="${OUTPUT_DIR}/${timestamp}/ARM"

    # 为M42和M22分别制作升级包
    for variant in "M42" "M22"; do
        log_info "制作${variant}升级包..."

        local variant_dir
        if [[ "$variant" == "M42" ]]; then
            variant_dir="$m42_dir"
        else
            variant_dir="$m22_dir"
        fi

        local variant_output_dir="${arm_base_dir}/${variant}"
        mkdir -p "$variant_output_dir"

        local config_file="${TEMP_DIR}/${variant}_config.ini"
        mkdir -p "$(dirname "$config_file")"

        # 选择配置模板
        if [[ "$SYSTEM_UPGRADE" == "true" ]]; then
            cp "${FOTA_TOOLS_DIR}/config/7520V3SC_vehicle_dc_ov_all.ini" "$config_file"
            log_info "使用全系统升级配置"
        else
            cp "${FOTA_TOOLS_DIR}/config/7520V3SC_vehicle_dc_ov_oem.ini" "$config_file"
            log_info "使用OEM分区升级配置"
        fi

        # 更新配置文件中的路径和版本
        sed -i "s|{fota_dir}|${variant_dir}|g" "$config_file"
        sed -i "s|source_version=.*|source_version=${DEFAULT_VERSION}|g" "$config_file"
        sed -i "s|dest_version=.*|dest_version=${DEFAULT_VERSION}|g" "$config_file" # 生成升级包
        local fota_output_dir="${TEMP_DIR}/${variant}_output"
        cd "${FOTA_TOOLS_DIR}"
        if ./diff_all.sh "$config_file" "$fota_output_dir"; then
            # 添加SHA256校验
            if [[ -f "${fota_output_dir}/up/upgrade.package" ]]; then
                local temp_package="${TEMP_DIR}/${variant}_temp.package"
                ./sha256append.sh "${fota_output_dir}/up/upgrade.package" "$temp_package"

                # 创建upgrade.zip：将.package文件和配置文件打包成zip
                local zip_temp_dir="${TEMP_DIR}/${variant}_zip"
                mkdir -p "$zip_temp_dir"
                cp "$temp_package" "${zip_temp_dir}/upgrade.package"

                # 添加ARM升级的iot.ini配置
                cp "${FOTA_TOOLS_DIR}/iot.ini" "${zip_temp_dir}/iot.ini"

                # 禁用MCU升级，启用ARM升级 (ARM升级包默认就是ArmEnable=1)
                sed -i 's/MCUEnable = 1;/MCUEnable = 0;/g' "${zip_temp_dir}/iot.ini" 2>/dev/null || true

                cd "$zip_temp_dir"
                if zip -r "${variant_output_dir}/upgrade.zip" upgrade.package iot.ini; then
                    log_info "${variant}升级包已生成: ${variant_output_dir}/upgrade.zip"

                    # 输出文件信息
                    printf "  ${variant}/upgrade.zip ($(get_file_size "${variant_output_dir}/upgrade.zip")) [MD5: $(get_file_md5 "${variant_output_dir}/upgrade.zip")]\n"
                else
                    log_error "${variant}升级包打包失败"
                    return 1
                fi
            else
                log_error "${variant}升级包生成失败"
                return 1
            fi
        else
            log_error "${variant}升级包制作失败"
            return 1
        fi
    done

    return 0
}

# 制作ARM升级包
create_arm_package() {
    log_info "开始制作ARM升级包..."

    if ! check_arm_build_env; then
        return 1
    fi

    local fota_bins_dir="${BUILD_DIR}/fota"
    local m42_dir="${fota_bins_dir}/M42bins"
    local m22_dir="${fota_bins_dir}/M22bins"

    # 创建ARM升级包目录结构
    local timestamp
    timestamp=$(date +"%Y%m%d_%H%M%S")
    local arm_base_dir="${OUTPUT_DIR}/${timestamp}/ARM"

    # 为M42和M22分别制作升级包
    for variant in "M42" "M22"; do
        log_info "制作${variant}升级包..."

        local variant_dir
        if [[ "$variant" == "M42" ]]; then
            variant_dir="$m42_dir"
        else
            variant_dir="$m22_dir"
        fi

        local variant_output_dir="${arm_base_dir}/${variant}"
        mkdir -p "$variant_output_dir"

        local config_file="${TEMP_DIR}/${variant}_config.ini"
        mkdir -p "$(dirname "$config_file")"

        # 选择配置模板
        if [[ "$SYSTEM_UPGRADE" == "true" ]]; then
            cp "${FOTA_TOOLS_DIR}/config/7520V3SC_vehicle_dc_ov_all.ini" "$config_file"
            log_info "使用全系统升级配置"
        else
            cp "${FOTA_TOOLS_DIR}/config/7520V3SC_vehicle_dc_ov_oem.ini" "$config_file"
            log_info "使用OEM分区升级配置"
        fi

        # 更新配置文件中的路径和版本
        sed -i "s|{fota_dir}|${variant_dir}|g" "$config_file"
        sed -i "s|source_version=.*|source_version=${DEFAULT_VERSION}|g" "$config_file"
        sed -i "s|dest_version=.*|dest_version=${DEFAULT_VERSION}|g" "$config_file" # 生成升级包
        local fota_output_dir="${TEMP_DIR}/${variant}_output"
        cd "${FOTA_TOOLS_DIR}"
        if ./diff_all.sh "$config_file" "$fota_output_dir"; then
            # 添加SHA256校验
            if [[ -f "${fota_output_dir}/up/upgrade.package" ]]; then
                local temp_package="${TEMP_DIR}/${variant}_temp.package"
                ./sha256append.sh "${fota_output_dir}/up/upgrade.package" "$temp_package"

                # 创建upgrade.zip：将.package文件和配置文件打包成zip
                local zip_temp_dir="${TEMP_DIR}/${variant}_zip"
                mkdir -p "$zip_temp_dir"
                cp "$temp_package" "${zip_temp_dir}/upgrade.package" # 添加ARM升级的iot.ini配置
                cp "${FOTA_TOOLS_DIR}/iot.ini" "${zip_temp_dir}/iot.ini"

                # 禁用MCU升级，启用ARM升级 (ARM升级包默认就是ArmEnable=1)
                sed -i 's/MCUEnable = 1;/MCUEnable = 0;/g' "${zip_temp_dir}/iot.ini" 2>/dev/null || true

                cd "$zip_temp_dir"
                if zip -r "${variant_output_dir}/upgrade.zip" upgrade.package iot.ini; then
                    log_info "${variant}升级包已生成: ${variant_output_dir}/upgrade.zip"

                    # 输出文件信息
                    printf "  ${variant}/upgrade.zip ($(get_file_size "${variant_output_dir}/upgrade.zip")) [MD5: $(get_file_md5 "${variant_output_dir}/upgrade.zip")]\n"
                else
                    log_error "${variant}升级包打包失败"
                    return 1
                fi
            else
                log_error "${variant}升级包生成失败"
                return 1
            fi
        else
            log_error "${variant}升级包制作失败"
            return 1
        fi
    done

    return 0
}

# 制作组合升级包
create_combined_package() {
    log_info "开始制作组合升级包..."

    # 创建统一的时间戳，所有组件都使用这个时间戳
    local timestamp
    timestamp=$(date +"%Y%m%d_%H%M%S")

    # 先制作ARM和MCU升级包
    if ! create_arm_package_with_timestamp "$timestamp"; then
        return 1
    fi

    if ! create_mcu_package_with_timestamp "$timestamp"; then
        return 1
    fi

    # 从ARM升级包目录获取文件
    local arm_base_dir="${OUTPUT_DIR}/${timestamp}/ARM"
    local mcu_base_dir="${OUTPUT_DIR}/${timestamp}/MCU"

    # 检查ARM和MCU升级包是否存在
    if [[ ! -d "$arm_base_dir" ]] || [[ ! -d "$mcu_base_dir" ]]; then
        log_error "ARM或MCU升级包目录不存在"
        return 1
    fi

    local mcu_upgrade_file="${mcu_base_dir}/upgrade.zip"
    if [[ ! -f "$mcu_upgrade_file" ]]; then
        log_error "找不到MCU升级包: $mcu_upgrade_file"
        return 1
    fi

    # 为M42和M22分别制作组合升级包
    for variant in "M42" "M22"; do
        log_info "制作${variant} ARM+MCU组合升级包..."

        local combined_output_dir="${OUTPUT_DIR}/${timestamp}/ARM+MCU/${variant}"
        mkdir -p "$combined_output_dir"

        local combined_upgrade_dir="${TEMP_DIR}/combined_${variant}"
        mkdir -p "$combined_upgrade_dir"

        local arm_upgrade_file="${arm_base_dir}/${variant}/upgrade.zip"

        if [[ ! -f "$arm_upgrade_file" ]]; then
            log_error "找不到${variant} ARM升级包: $arm_upgrade_file"
            return 1
        fi

        # 解压ARM升级包到临时目录
        local arm_temp_dir="${TEMP_DIR}/arm_extract_${variant}"
        mkdir -p "$arm_temp_dir"
        cd "$arm_temp_dir"
        unzip -q "$arm_upgrade_file"

        # 解压MCU升级包到临时目录
        local mcu_temp_dir="${TEMP_DIR}/mcu_extract"
        mkdir -p "$mcu_temp_dir"
        cd "$mcu_temp_dir"
        unzip -q "$mcu_upgrade_file"

        # 将ARM升级包内容复制到组合目录
        cp -r "$arm_temp_dir"/* "$combined_upgrade_dir/"

        # 将MCU文件复制到组合目录，重命名为UserApp_src.bin
        if [[ -f "${mcu_temp_dir}/UserApp_src.bin" ]]; then
            cp "${mcu_temp_dir}/UserApp_src.bin" "${combined_upgrade_dir}/UserApp_src.bin"
        fi

        # 创建配置文件
        local iot_config="${combined_upgrade_dir}/iot.ini"
        cp "${FOTA_TOOLS_DIR}/iot.ini" "$iot_config"

        # 启用ARM和MCU升级
        sed -i 's/MCUEnable = 0;/MCUEnable = 1;/g' "$iot_config"
        sed -i 's/ArmEnable = 0;/ArmEnable = 1;/g' "$iot_config" 2>/dev/null || true

        # 打包成upgrade.zip
        cd "$combined_upgrade_dir"
        if zip -r "${combined_output_dir}/upgrade.zip" ./*; then
            log_info "${variant} ARM+MCU组合升级包已生成: ${combined_output_dir}/upgrade.zip"

            # 输出文件信息
            printf "  ARM+MCU/${variant}/upgrade.zip ($(get_file_size "${combined_output_dir}/upgrade.zip")) [MD5: $(get_file_md5 "${combined_output_dir}/upgrade.zip")]\n"
        else
            log_error "${variant} 组合升级包创建失败"
            return 1
        fi
    done

    return 0
}

# 生成升级包摘要
generate_summary() {
    log_info "生成升级包摘要..."

    # 查找最新的时间戳目录
    local latest_timestamp
    latest_timestamp=$(find "$OUTPUT_DIR" -maxdepth 1 -type d -name "????????_??????" 2>/dev/null | sort | tail -1)

    if [[ -n "$latest_timestamp" ]]; then
        latest_timestamp=$(basename "$latest_timestamp")
    fi

    if [[ -z "$latest_timestamp" ]]; then
        log_warn "未找到升级包目录，摘要文件将生成在根目录"
        latest_timestamp=""
    fi

    local summary_file
    if [[ -n "$latest_timestamp" ]]; then
        summary_file="${OUTPUT_DIR}/${latest_timestamp}/upgrade_summary_${DEFAULT_VERSION}.txt"
    else
        summary_file="${OUTPUT_DIR}/upgrade_summary_${DEFAULT_VERSION}.txt"
    fi

    # 确保摘要文件的目录存在
    mkdir -p "$(dirname "$summary_file")"

    cat >"$summary_file" <<EOF
TBox升级包制作摘要
==================

制作时间: $(date)
版本号: ${DEFAULT_VERSION}
升级类型: ${UPGRADE_TYPE}
编译模式: ${BUILD_MODE}
系统升级: ${SYSTEM_UPGRADE}

生成的文件:
EOF

    # 列出当前时间戳目录下的所有upgrade.zip文件
    if [[ -n "$latest_timestamp" ]]; then
        find "${OUTPUT_DIR}/${latest_timestamp}" -type f -name "upgrade.zip" | while read -r file; do
            local size
            local md5
            local relative_path
            size=$(du -h "$file" | cut -f1)
            md5=$(md5sum "$file" | cut -d' ' -f1)
            # 获取相对于OUTPUT_DIR的路径
            relative_path=${file#${OUTPUT_DIR}/}
            echo "  ${relative_path} (${size}) [MD5: ${md5}]" >>"$summary_file"
        done
    else
        # 如果没有找到时间戳目录，则不列出任何文件
        echo "  未找到升级包文件" >>"$summary_file"
    fi

    echo "" >>"$summary_file"
    echo "升级包位置: $OUTPUT_DIR" >>"$summary_file"

    log_info "摘要文件已生成: $summary_file"

    # 显示摘要
    cat "$summary_file"
}

# 清理临时文件
cleanup() {
    if [[ -d "$TEMP_DIR" ]]; then
        rm -rf "$TEMP_DIR"
        log_debug "已清理临时目录: $TEMP_DIR"
    fi
}

# 主函数
main() {
    # 设置错误时清理
    trap cleanup EXIT

    # 解析参数
    parse_args "$@"

    log_info "TBox升级包制作工具启动"
    log_info "版本: ${DEFAULT_VERSION}, 类型: ${UPGRADE_TYPE}, 模式: ${BUILD_MODE}"

    # 检查依赖
    check_dependencies

    # 创建输出目录
    mkdir -p "$OUTPUT_DIR" "$TEMP_DIR"

    # 根据升级类型执行相应操作
    case "$UPGRADE_TYPE" in
    "arm")
        if [[ "$SYSTEM_UPGRADE" == "true" ]]; then
            log_info "制作ARM系统升级包..."
        else
            log_info "制作ARM OEM升级包..."
        fi
        create_arm_package
        ;;
    "mcu")
        log_info "制作MCU升级包..."
        check_mcu_build_env
        create_mcu_package
        ;;
    "both")
        log_info "制作ARM+MCU组合升级包..."
        check_mcu_build_env
        create_combined_package
        ;;
    esac

    # 生成摘要
    generate_summary

    log_info "升级包制作完成！"
    log_info "输出目录: $OUTPUT_DIR"
}

# 运行主函数
main "$@"

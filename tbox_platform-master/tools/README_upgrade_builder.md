# TBox升级
- 🚀 **一键制作**: 简化配置，一条命令生成升级包
- 🔧 **灵活选择**: 支持ARM、MCU单独或组合升级
- 🛠️ **编译模式**: 支持debug/release两种编译模式
- 📦 **自动打包**: 自动生成标准格式的升级包
- ✅ **完整性校验**: 自动添加SHA256校验
- 📋 **详细摘要**: 生成升级包制作摘要报告
- 🤖 **智能编译**: 自动检测并编译缺失的固件组件说明

## 概述

这是一个简化的TBox升级包制作工具，支持ARM、MCU单独升级或组合升级，提供一键生成升级包的功能。

## 特性

- 🚀 **一键制作**: 简化配置，一条命令生成升级包
- 🔧 **灵活选择**: 支持ARM、MCU单独或组合升级
- �️ **编译模式**: 支持debug/release两种编译模式
- �📦 **自动打包**: 自动生成标准格式的升级包
- ✅ **完整性校验**: 自动添加SHA256校验
- 📋 **详细摘要**: 生成升级包制作摘要报告
- 🤖 **智能编译**: 自动检测并编译缺失的固件组件

## 目录结构

```text
tbox_platform-master/
├── tools/
│   ├── upgrade_builder.sh      # 主要的升级包制作脚本
│   └── Fota/                   # FOTA工具目录
│       ├── diff_all.sh         # ARM升级包制作脚本
│       ├── sha256append.sh     # SHA256校验追加脚本
│       ├── iot.ini             # 升级配置模板
│       └── config/             # 配置文件目录
└── build/
    ├── fota/                   # ARM编译产物目录
    └── upgrade_packages/       # 升级包输出目录
```

## 使用方法

### 基本语法

```bash
./tools/upgrade_builder.sh [选项]
```

### 选项说明

- `-t, --type TYPE`: 升级类型
  - `arm`: 仅制作ARM升级包
  - `mcu`: 仅制作MCU升级包  
  - `both`: 制作ARM+MCU组合升级包（默认）

- `-v, --version VERSION`: 版本号（默认：当前时间戳）

- `-m, --mode MODE`: 编译模式
  - `debug`: 调试模式编译（默认）
  - `release`: 发布模式编译

- `-s, --system`: 包含系统分区升级（仅ARM升级时有效）

- `-o, --output DIR`: 输出目录（默认：build/upgrade_packages）

- `--no-auto-build`: 禁用自动编译功能

- `-h, --help`: 显示帮助信息

### 使用示例

#### 1. 制作ARM+MCU组合升级包（默认）

```bash
./tools/upgrade_builder.sh
```

#### 2. 制作仅ARM升级包

```bash
./tools/upgrade_builder.sh -t arm
```

#### 3. 制作包含系统分区的ARM升级包

```bash
./tools/upgrade_builder.sh -t arm -s
```

#### 4. 制作仅MCU升级包（指定编译模式）

```bash
./tools/upgrade_builder.sh -t mcu -v v1.2.3 -m release
```

#### 5. 制作组合升级包到指定目录

```bash
./tools/upgrade_builder.sh -t both -v v2.0.0 -m debug -o /tmp/output
```

#### 6. 禁用自动编译功能

```bash
./tools/upgrade_builder.sh -m release --no-auto-build
```

## 前置条件

### ARM升级包制作前置条件

1. **完成ARM编译**: 需要先运行主编译脚本生成ARM固件

   ```bash
   # Debug模式编译（默认）
   ./cmake_build.sh -d
   
   # Release模式编译
   ./cmake_build.sh -r
   ```

2. **确保FOTA目录存在**: `build/fota/M42bins/` 和 `build/fota/M22bins/` 目录包含必要的固件文件：
   - `uboot.bin`
   - `ap_imagefs.img`
   - `ap_rootfs.img`
   - `ap_caprootfs.img`
   - `cap_oem.img`

### MCU升级包制作前置条件

1. **安装交叉编译工具链**:
   ```bash
   sudo apt-get install gcc-arm-none-eabi
   ```

2. **确保MCU代码目录存在**:
   - `../mcu_code/` (MCU应用代码)
   - `../mcu_boot/` (MCU引导代码)

3. **安装构建工具**:
   ```bash
   sudo apt-get install cmake ninja-build
   ```

## 输出结果

脚本执行完成后，会在输出目录以时间戳分类的目录结构生成升级包文件，所有升级包都统一命名为 `upgrade.zip`：

```text
build/upgrade_packages/
└── 20250716_1020/                    # 时间戳目录
    ├── ARM/                          # ARM升级包目录
    │   ├── M42/
    │   │   └── upgrade.zip           # M42 ARM升级包
    │   └── M22/
    │       └── upgrade.zip           # M22 ARM升级包
    ├── MCU/                          # MCU升级包目录（仅MCU升级时）
    │   └── upgrade.zip               # MCU升级包
    ├── ARM+MCU/                      # 组合升级包目录（组合升级时）
    │   ├── M42/
    │   │   └── upgrade.zip           # M42 ARM+MCU组合升级包
    │   └── M22/
    │       └── upgrade.zip           # M22 ARM+MCU组合升级包
    └── upgrade_summary_[version].txt # 升级包汇总信息
```

### 文件说明

#### ARM升级包（单独ARM升级）

- `时间/ARM/M42/upgrade.zip` - M42版本ARM升级包
- `时间/ARM/M22/upgrade.zip` - M22版本ARM升级包
- 包内容：`upgrade.package` (ARM固件升级包), `iot.ini` (升级配置文件)

#### MCU升级包（单独MCU升级）

- `时间/MCU/upgrade.zip` - MCU升级包
- 包内容：`UserApp_src.bin` (MCU应用程序带SHA256校验), `iot.ini` (升级配置文件)

#### 组合升级包（ARM+MCU升级）

- `时间/ARM+MCU/M42/upgrade.zip` - M42 ARM+MCU组合升级包
- `时间/ARM+MCU/M22/upgrade.zip` - M22 ARM+MCU组合升级包
- 包内容：`upgrade.package` (ARM固件), `UserApp_src.bin` (MCU应用程序), `iot.ini` (升级配置文件)

#### 摘要报告

- `时间/upgrade_summary_[版本].txt` - 包含所有生成文件的MD5校验值、编译模式等详细信息

## 升级包部署

### 手动升级

```bash
# 推送升级包到设备
adb push ./upgrade.zip /mnt/oemdata/tbox/ota/upgrade/temp/

# 重启设备触发升级
adb shell reboot
```

### 平台升级

将生成的升级包上传到平台，T-Box会自动检测并执行升级。

## 故障排除

### 常见问题

1. **找不到ARM固件文件**
   - 确保先运行了ARM编译脚本
   - 检查 `build/fota/` 目录是否存在且包含必要文件

2. **MCU编译失败**
   - 确保安装了 `gcc-arm-none-eabi` 工具链
   - 检查MCU代码目录路径是否正确

3. **依赖工具缺失**
   - 脚本会自动检查依赖并提示缺失的工具
   - 按提示安装缺失的依赖包

4. **权限问题**
   - 确保脚本有执行权限：`chmod +x upgrade_builder.sh`
   - 确保输出目录有写权限

5. **ARM编译时间很长**
   - ARM编译是正常的，首次编译可能需要30分钟到1小时
   - 请耐心等待编译完成

6. **自动编译失败但手动编译成功**
   - 这可能是编译脚本返回码的问题
   - 脚本会检查编译产物来确认编译是否真正成功

### 调试模式

如需调试，可以查看临时文件目录 `build/upgrade_temp/` 的内容（脚本执行完成前）。

## 配置定制

如需修改默认配置，可以编辑以下文件：

- `tools/Fota/iot.ini` - 升级配置模板
- `tools/Fota/config/` - FOTA配置文件模板

## 版本更新

建议使用语义化版本号，例如：

- `v1.0.0` - 主版本
- `v1.1.0` - 功能更新
- `v1.1.1` - 问题修复
- `20241215_1430` - 时间戳格式（默认）

#include <string.h>
#include <stdlib.h>
#include <pthread.h>
#include <errno.h>

#include "iot_ipcc.h"
#include "iot_dbus.h"
#include "iot_logc.h"
#include "IpcCommon.h"
#include "SystemApi.h"
#include "tbox_paraconfig.h"
#include "SystemCrc.h"

#define IPC_SEND_MSG_TIME_OUT 3000  //ms
#define IPC_HANDLE_CB_TIME_OUT 10 //ms


static int SendMessageByIpc(uint16_t msg_id, uint16_t msg_len, uint8_t *msg)
{
    int ret;
    const char dest[] = DBUS_SERVICE_IPC_NAME;
    const char module[] = DBUS_MODULE_IPC;
    const char action[] = DBUS_ACTION_IPC_SEND;
    DBusMsgParaInfo_s msgParaInfo;
    DBusMsgSendInfo_s msgSendInfo;
    DBusMessage *dbusMsg;
    DBusMessageIter iter;
    DBusMsgParaInfo_s returnInfo;

    msgSendInfo.isSignal = FALSE;
    msgSendInfo.dest = dest;
    msgSendInfo.module = module;
    msgSendInfo.action = action;

    msgParaInfo.type = TBOX_DBUS_TYPE_UINT16;
    msgParaInfo.typeInfo.u16 = msg_id;
    msgSendInfo.paraInfo = &msgParaInfo;

    ret = DBusSendMessageBegin(&msgSendInfo, &dbusMsg, &iter);
    TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_IPCC, ret, "Send msggage by ipc fail when add msg id", ret);

    if (NULL != msg)
    {
        msgParaInfo.type = TBOX_DBUS_TYPE_ARRAY;
        msgParaInfo.arrayType = TBOX_DBUS_TYPE_UINT8;
        msgParaInfo.arrayLen = msg_len;
        msgParaInfo.typeInfo.array = msg;

        ret = DBusSetMessage(&iter, &msgParaInfo);
        if (TBOX_OK != ret)
        {
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                             "Set message fail by ipc when add array message, ret=%d.", ret);
            DBusFreeMessage(dbusMsg);
            return ret;
        }
    }

    ret = DBusSendMessageEnd(FALSE, dbusMsg, &returnInfo, IPC_SEND_MSG_TIME_OUT);
    TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_IPCC, ret, "Send msggage by ipc fail when flush send", ret);

    DBUS_CHECK_WRONG_TYPE_MESSAGEC(TBOX_MODULE_IPCC, TBOX_DBUS_TYPE_INT32,
                                   (&returnInfo), "Send ipc message", DBUS_ERROR_REMOTE_WRONG_TYPE);

    return returnInfo.typeInfo.i32;
}

/*************************************************
函数名称: IpcSendHandShake
函数功能: 发送握手消息
输入参数: 无
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/03
*************************************************/
int IpcSendHandShake(void)
{
    int ret = TBOX_OK;

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Send shake hand in ipcc.");

    ret = SendMessageByIpc(MESSAGE_TX_HANDSHAKE, 0, NULL);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Send shake hand in ipcc failed, ret=%d.", ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcSendHeartBeat
函数功能: 发送心跳
输入参数: 无
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/03
*************************************************/
int IpcSendHeartBeat(void)
{
    int ret = TBOX_OK;

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "Send heart beat in ipcc.");

    ret = SendMessageByIpc(MESSAGE_TX_HEARTBEAT, 0, NULL);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Send heat beat in ipcc failed, ret=%d.", ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcReportArmDtc
函数功能: 向MCU汇报ARM端的故障码
输入参数: type - 故障发生模块，status - 故障状态
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/03
*************************************************/
int IpcReportArmDtc(uint8_t type, uint8_t status)
{
    int ret = TBOX_OK;
    uint8_t buf[2];

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_WARN, "Report arm dtc in ipcc, type=0x%x, status=%u.", type, status);

    buf[0] = type;
    buf[1] = status;
    ret = SendMessageByIpc(MESSAGE_TX_REPORT_ARM_DTC, sizeof(buf), buf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Report arm dtc failed for type=0x%x, status=%u, ret=%d.", type, status, ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcSetArmPowerStatus
函数功能: 设置ARM状态
输入参数: workType - 业务类型，workStatus - 工作状态 .workSource 调用来源，目前给网络管理使用，区分谁唤醒/关闭的
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/02
*************************************************/
int IpcSetArmPowerStatus(ArmWorkType workType, ArmWorkStatus workStatus,ArmWorkSource workSource)
{
    int ret = TBOX_OK;
    uint8_t buf[3];

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG,
                     "Set arm power status in ipcc, workType=%d, workStatus=%d.", workType, workStatus);

    buf[0] = workType;
    buf[1] = workStatus;
    buf[2] = workSource;
    ret = SendMessageByIpc(MESSAGE_TX_REPORT_ARM_STATUS, sizeof(buf), buf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Set arm work(%d) status(%d) failed, ret=%d.", workType, workStatus, ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcReuqestPowerManager
函数功能: 发起电源管理请求
输入参数: type - 0: 对ARM和4G模块下电和上电，1 : 浅度睡眠，2: 深度睡眠(当前仅支持0)
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/02
*************************************************/
int IpcReuqestPowerManager(uint8_t type)
{
    int ret = TBOX_OK;

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Request Power Manager in ipcc, PM type=%d.", type);

    ret = SendMessageByIpc(MESSAGE_TX_ARM_PM_REQUESET, 1, (uint8_t *)&type);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Request Power Manager(%d) failed, ret=%d.", type, ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcSendBtCmd
函数功能: 发送BT命令
输入参数: cmdBuf - BT命令
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/02
*************************************************/
int IpcSendBtCmd(char *cmdBuf)
{
    int ret = TBOX_OK;
    uint32_t cmdLen;
    uint8_t *pBuf;

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, cmdBuf, TBOX_ERROR_NULL_POINTER);
    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "Send bt command(%s) in ipcc.", cmdBuf);

    cmdLen = strlen(cmdBuf);
    pBuf = (uint8_t *)malloc(cmdLen + 1);
    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pBuf, TBOX_ERROR_MALLOC_FAIL);

    pBuf[0] = (uint8_t)BT_NORMAL_SEND_DATA;
    memcpy(&pBuf[1], cmdBuf, cmdLen);
    ret = SendMessageByIpc(MESSAGE_TX_BT_REQUESET, cmdLen + 1, pBuf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Send bt command(%s) failed, ret=%d.", cmdBuf, ret);
    }

    free(pBuf);
    return ret;
}

/*************************************************
函数名称: IpcSendBtUpgData
函数功能: 发送BT升级数据--用于升级
输入参数: cmdBuf - BT数据
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/02
*************************************************/
int IpcSendBtUpgData(uint32_t dataLen, uint8_t *dataBuf)
{
    int ret = TBOX_OK;
    uint8_t *pBuf;

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, dataBuf, TBOX_ERROR_NULL_POINTER);
    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "Send bt data(%s) in ipcc.", dataBuf);

    pBuf = (uint8_t *)malloc(dataLen + 1);
    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pBuf, TBOX_ERROR_MALLOC_FAIL);

    pBuf[0] = (uint8_t)BT_UPGRADE_SEND_DATA;
    memcpy(&pBuf[1], dataBuf, dataLen);
    ret = SendMessageByIpc(MESSAGE_TX_BT_REQUESET, dataLen + 1, pBuf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Send bt data(%s) failed, ret=%d.", dataBuf, ret);
    }

    free(pBuf);
    return ret;
}

/*************************************************
函数名称: IpcReportUpFileStatus
函数功能: 设置MCU往ARM上传文件时的状态
输入参数: fileInfo - 上传步骤，status - 通知状态
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/03
*************************************************/
int IpcReportUpFileStatus(TxFileInfo upStep, uint8_t status)
{
    int ret = TBOX_OK;
    uint8_t buf[2];

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG,
                     "Report upload file status in ipcc, upStep=%d, status=%u.", upStep, status);

    buf[0] = (uint8_t)upStep;
    buf[1] = status;
    ret = SendMessageByIpc(MESSAGE_TX_UPLOAD_FILE_STATUS, sizeof(buf), buf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Report upload file step(%d) status(%d) failed, ret=%d.", upStep, status, ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcRequestRtcTime
函数功能: 请求获取 rtc时间数据
输入参数: 无
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcRequestRtcTime(void)
{
    int ret = TBOX_OK;
    uint8_t buf[] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Request rtc time in ipcc.");

    ret = SendMessageByIpc(MESSAGE_TX_TIME_CALIBRATION, sizeof(buf), buf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Request rtc time failed, ret=%d.", ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcSetRtcTime
函数功能: 设置更新rtc时间
输入参数: pTime - 时间数据
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/03
*************************************************/
int IpcSetRtcTime(TimeInfo *pTime)
{
    int ret = TBOX_OK;
    uint8_t buf[7]; //timeZone不需要传输，所以只有7字节

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pTime, TBOX_ERROR_NULL_POINTER);

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Set rtc time in ipcc, w%u %u-%u-%u %u:%u:%u.",
                     pTime->week, pTime->year, pTime->month, pTime->day, pTime->hour, pTime->minute, pTime->second);

    buf[0] = pTime->second;
    buf[1] = pTime->minute;
    buf[2] = pTime->hour;
    buf[3] = pTime->day;
    buf[4] = pTime->week;
    buf[5] = pTime->month;
    buf[6] = pTime->year;

    ret = SendMessageByIpc(MESSAGE_TX_TIME_CALIBRATION, sizeof(buf), buf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Send set rtc time in ipcc fail, w%u %u-%u-%u %u:%u:%u.",
                         pTime->week, pTime->year, pTime->month, pTime->day, pTime->hour, pTime->minute, pTime->second);
    }

    return ret;
}

/*************************************************
函数名称: IpcRequestDidInfo
函数功能: 读取DID信息
输入参数: didCode - DID码
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/02
*************************************************/
int IpcRequestDidInfo(uint16_t didCode)
{
    int ret = TBOX_OK;
    uint8_t buf[3];
    DBusMsgParaInfo_s msgParaInfo;

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "Read arm did info in ipcc, did_code=0x%x.", didCode);

    buf[0] = DID_READ_TYPE;
    buf[1] = (uint8_t)(didCode >> 8);
    buf[2] = (uint8_t)didCode;
    ret = SendMessageByIpc(MESSAGE_TX_ARM_DID_INFO, sizeof(buf), buf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Read arm did info failed for did_code=0x%x, ret=%d.", didCode, ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcWriteDidInfo
函数功能: 配置DID信息
输入参数: didCode - DID码；didLen -  DID信息长度，didInfo - 要写入的DID信息
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/02
*************************************************/
int IpcWriteDidInfo(uint16_t didCode, uint16_t didLen, uint8_t *didInfo)
{
    int ret = TBOX_OK;
    uint8_t *pBuf;

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, didInfo, TBOX_ERROR_NULL_POINTER);

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG,
                     "Write arm did info in ipcc, did_code=0x%x, didLen=%u.", didCode, didLen);

    pBuf = malloc(didLen + 3);
    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pBuf, TBOX_ERROR_MALLOC_FAIL);

    pBuf[0] = DID_WRITE_TYPE;
    pBuf[1] = (uint8_t)(didCode >> 8);
    pBuf[2] = (uint8_t)didCode;
    memcpy(pBuf + 3, didInfo, didLen);
    ret = SendMessageByIpc(MESSAGE_TX_ARM_DID_INFO, didLen + 3, pBuf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Write arm did info failed for did_code=0x%x, didLen=%u, ret=%d.", didCode, didLen, ret);
    }

    free(pBuf);
    return ret;
}


/*************************************************
函数名称: IpcRequestTboxDtc
函数功能: 请求T-Box DTC
输入参数: type - 请求类型；maskValue - 请求DTC类型
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/02
*************************************************/
int IpcRequestTboxDtc(DTCReqType type, DtcFaultRequestMaskValue maskValue)
{
    int ret = TBOX_OK;
    uint8_t buf[3];

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "Request tbox dtc in ipcc, maskValue=0x%x.", maskValue);

    buf[0] = DTC_CLIENT_REQ;
    buf[1] = (uint8_t)type;
    buf[2] = (uint8_t)maskValue;
    ret = SendMessageByIpc(MESSAGE_TX_ARM_REQUEST_DTC, sizeof(buf), buf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Request tbox dtc failed for maskValue=0x%x, ret=%d.", maskValue, ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcSetMcuGpio
函数功能: 设置MCU特定的GPIO，主要用于控制LED状态
输入参数: gpioNum - 与MCU对应；lightOnTime - LED点亮时长，单位: 秒；lightOffTime - 熄灭时长，单位: 秒
输入参数: gpioMode - LED状态，0~3分别表示ON/OFF/BLINKED/FLASH；普通GPIO，0~1分别表示低和高
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcSetMcuGpio(uint8_t gpioNum, uint8_t gpioMode, uint8_t lightOnTime, uint8_t lightOffTime)
{
    int ret = TBOX_OK;
    uint8_t buf[4];
    DBusMsgParaInfo_s msgParaInfo;

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Set mcu gpio in ipcc, gpioNum=%u, gpioMode=%u, "
                     "lightOnTime=%u, lightOffTime=%u.", gpioNum, gpioMode, lightOnTime, lightOffTime);

    buf[0] = gpioNum;
    buf[1] = gpioMode;
    buf[2] = lightOnTime;
    buf[3] = lightOffTime;
    ret = SendMessageByIpc(MESSAGE_TX_ARM_SET_GPIO, sizeof(buf), buf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Set mcu gpio failed for gpio num=%u, ret=%d.", gpioNum, ret);
    }

    return ret;

}

/*************************************************
函数名称: IpcSendMcuUpgData
函数功能: 发送MCU升级数据
输入参数: frameCount - 发送的帧数，len - 本帧数据长度，frameData - 单帧升级数据
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcSendMcuUpgData(uint16_t frameCount, uint16_t len, uint8_t *frameData)
{
    int ret = TBOX_OK;
    uint8_t *pBuf = NULL;

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, frameData, TBOX_ERROR_NULL_POINTER);

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO,
                     "Send mcu upgrade data in ipcc, len=%u, frameCount=%u.", len, frameCount);

    pBuf = malloc(len + 5);
    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pBuf, TBOX_ERROR_MALLOC_FAIL);

    pBuf[0] = (uint8_t)len;
    pBuf[1] = (uint8_t)(len >> 8);
    pBuf[2] = (uint8_t)frameCount;
    pBuf[3] = (uint8_t)(frameCount >> 8);
    memcpy(pBuf + 4, frameData, len);
    pBuf[len + 4] = SystemCalCrc8(frameData, len); //添加crc在最后

    ret = SendMessageByIpc(MESSAGE_TX_UPGRADE_MCUFILE_DOWNLOAD, len + 5, pBuf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Send mcu upgrade data failed, frameCount=%u, len=%u, ret=%d.", frameCount, len, ret);
    }

    free(pBuf);
    return ret;
}

/*************************************************
函数名称: IpcSendClientStatus
函数功能: 发送ARM端客户进程运行状态
输入参数: status - 客户进程运行状态
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcSendClientStatus(uint8_t status)
{
    int ret = TBOX_OK;

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Send client status in ipcc, status=%u.", status);

    ret = SendMessageByIpc(MESSAGE_TX_CLIENT_STA, 1, &status);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Send client status failed for status=%u, ret=%d.", status, ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcSendRemoteCtrlCanMsgCmd
函数功能: 发送远程控制CAN消息命令
输入参数: rmtCmd - 远控命令数据，cmdLen - 命令数据长度
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: benyulong
编写日期 :2020/02/18
*************************************************/
int IpcSendRemoteCtrlCanMsgCmd(uint8_t *rmtCmd, uint32_t cmdLen)
{
    int ret = TBOX_OK;
    uint8_t buf[CAN_MSG_MAX_LEN + 10];

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, rmtCmd, TBOX_ERROR_NULL_POINTER);

    SystemPrintHexData(TBOX_MODULE_IPCC, TBOX_LOG_INFO, rmtCmd, cmdLen, "Send remote control command data: ");

    ret = SendMessageByIpc(MESSAGE_TX_REMOTE_CTRL_CAN_MSG_CMD, cmdLen, rmtCmd);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Send remote control can msg command failed, ret=%d.", ret);
    }

    return ret;
}
/*************************************************
函数名称: IpcSendCanPeriodConfig
函数功能: 发送CAN周期配置参数给mcu
输入参数: pCanPeriodConfig - Can周期配置参数
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcSendCanPeriodConfig(CanPeriodConfigInfo *pCanPeriodConfig)
{
    int ret = TBOX_OK;
    uint32_t count;
    uint8_t *pArray;
    uint8_t *pBuf;
    uint32_t bufLen;

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pCanPeriodConfig, TBOX_ERROR_NULL_POINTER);

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO,
                     "Send can period config in ipcc, total_num=%u.", pCanPeriodConfig->total);

    //can id只传输了2个byte，但CanPeriodInfo中定义的却是uint32_t，所以-2
    bufLen = (sizeof(CanPeriodInfo) - 2) * pCanPeriodConfig->total + 1;
    pBuf = (uint8_t *)malloc(bufLen);
    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pBuf, TBOX_ERROR_MALLOC_FAIL);

    pBuf[0] = pCanPeriodConfig->total;
    for (count = 0; count < pCanPeriodConfig->total; count++)
    {
        pArray = &pBuf[(sizeof(CanPeriodInfo) - 2) * count + 1];
        pArray[0] = pCanPeriodConfig->para[count].channel;
        pArray[1] = (uint8_t)(pCanPeriodConfig->para[count].period >> 8);
        pArray[2] = (uint8_t)pCanPeriodConfig->para[count].period;
        pArray[3] = (uint8_t)(pCanPeriodConfig->para[count].canId >> 8);
        pArray[4] = (uint8_t)pCanPeriodConfig->para[count].canId;
        pArray[5] = pCanPeriodConfig->para[count].rollingCountStartBit;
        pArray[6] = pCanPeriodConfig->para[count].checkSumByte;
        pArray[7] = pCanPeriodConfig->para[count].rollingCountMinValue;
        pArray[8] = pCanPeriodConfig->para[count].rollingCountMaxValue;
        pArray[9] = pCanPeriodConfig->para[count].rollingCountStepValue;
        pArray[10] = pCanPeriodConfig->para[count].data[0];
        pArray[11] = pCanPeriodConfig->para[count].data[1];
        pArray[12] = pCanPeriodConfig->para[count].data[2];
        pArray[13] = pCanPeriodConfig->para[count].data[3];
        pArray[14] = pCanPeriodConfig->para[count].data[4];
        pArray[15] = pCanPeriodConfig->para[count].data[5];
        pArray[16] = pCanPeriodConfig->para[count].data[6];
        pArray[17] = pCanPeriodConfig->para[count].data[7];
    }

    ret = SendMessageByIpc(MESSAGE_TX_PARA_CONFIG_CMD, bufLen, pBuf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Send can period config failed, ret=%d.", ret);
    }

    free(pBuf);
    return ret;
}

/*************************************************
函数名称: IpcReportDeviceStatus
函数功能: 发送ARM设备信息状态给mcu
输入参数: pArmDeviceSatus - 设备信息状态数据
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcReportDeviceStatus(ArmDeviceStatus *pArmDeviceSatus)
{
    int ret = TBOX_OK;
    //armMode、gnssStatus未传输，且每项只占一个byte，所以是10
    uint8_t buf[128] =  {0};
    uint8_t msgNum = 0;
    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pArmDeviceSatus, TBOX_ERROR_NULL_POINTER);

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Report device status in ipcc, status set: "
                     "arm:%u-%u,wifi:%u-%u,module:%u-%u,sim:%u,gnss:%u-%u-%u-%u-%u-%u.lat:%u,lon:%u,speed:%u",
                     pArmDeviceSatus->armStatus, pArmDeviceSatus->armWorkMode, pArmDeviceSatus->wifiStatus,
                     pArmDeviceSatus->wifiSignal, pArmDeviceSatus->moduleStatus, pArmDeviceSatus->moduleSignal,
                     pArmDeviceSatus->simCardStatus, pArmDeviceSatus->gnssStatus, pArmDeviceSatus->gpsStarCount,
                     pArmDeviceSatus->gpsDirectionH, pArmDeviceSatus->gpsDirectionL, pArmDeviceSatus->gpsFixStatus,
                     pArmDeviceSatus->gnssSignal,pArmDeviceSatus->latitude, pArmDeviceSatus->longitude,pArmDeviceSatus->speed);

    if(pArmDeviceSatus->extendedDataFlag == 0)
    {
        buf[0] = pArmDeviceSatus->moduleSignal;
        buf[1] = pArmDeviceSatus->gpsStarCount;
        buf[2] = pArmDeviceSatus->gpsDirectionH;
        buf[3] = pArmDeviceSatus->gpsDirectionL;
        buf[4] = pArmDeviceSatus->armStatus;
        buf[5] = pArmDeviceSatus->wifiSignal;
        buf[6] = pArmDeviceSatus->moduleStatus;
        buf[7] = pArmDeviceSatus->gpsFixStatus;
        buf[8] = pArmDeviceSatus->simCardStatus;
        buf[9] = pArmDeviceSatus->gnssSignal;
        buf[10] = pArmDeviceSatus->emmcStatus;
        // buf[11] = pArmDeviceSatus->ethStatus;
        buf[11] = 1; //中车直接设置为1
        buf[12] = (uint8_t)((pArmDeviceSatus->latitude >> 24) & 0xFF);
        buf[13] = (uint8_t)((pArmDeviceSatus->latitude >> 16) & 0xFF);
        buf[14] = (uint8_t)((pArmDeviceSatus->latitude >> 8) & 0xFF);
        buf[15] = (uint8_t)(pArmDeviceSatus->latitude & 0xFF);
        buf[16] = (uint8_t)((pArmDeviceSatus->longitude >> 24) & 0xFF);
        buf[17] = (uint8_t)((pArmDeviceSatus->longitude >> 16) & 0xFF);
        buf[18] = (uint8_t)((pArmDeviceSatus->longitude >> 8) & 0xFF);
        buf[19] = (uint8_t)(pArmDeviceSatus->longitude & 0xFF);
        buf[20] = (pArmDeviceSatus->speed >> 24) & 0xFF;
        buf[21] = (pArmDeviceSatus->speed >> 16) & 0xFF;
        buf[22] = (pArmDeviceSatus->speed >> 8) & 0xFF;
        buf[23] = pArmDeviceSatus->speed & 0xFF;
        buf[24] = (uint8_t)pArmDeviceSatus->tspConnectStatus;
        // buf[25] = pArmDeviceSatus->wifiStatus;
        buf[25] = 1; //中车直接设置为1
        msgNum = 26;
    }
    else
    {
        buf[0] = pArmDeviceSatus->netStatus;
        buf[1] = pArmDeviceSatus->canReportMode;
        buf[2] = pArmDeviceSatus->vehicleFault;
        msgNum = 3;
    }

    ret = SendMessageByIpc(MESSAGE_TX_PERIOD_VEHICLE_REPORT, msgNum, buf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Send report device status cmd failed, ret=%d.", ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcReqCheckMicFault
函数功能: 向mcu发起检测MIC故障请求
输入参数: 无
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcReqCheckMicFault(void)
{
    int ret = TBOX_OK;

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "Request check mic fault in ipcc.");

    ret = SendMessageByIpc(MESSAGE_TX_MCU_CHECK_MIC, 0, NULL);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Send request check mic fault cmd failed, ret=%d.", ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcSendRemoteDiagCmd
函数功能: 发送远程诊断命令
输入参数: canCh - can通道，canId - 远端ID， len - 命令或数据长度，cmdData - 命令数据
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcSendRemoteDiagCmd(uint8_t canCh, uint32_t canId, uint16_t len, uint8_t *cmdData)
{
    int ret = TBOX_OK;
    uint8_t *pBuf;

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, cmdData, TBOX_ERROR_NULL_POINTER);
    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG,
                     "Send remote diagnosis command in ipcc, canCh=%u, canId=%x, len=%u.", canCh, canId, len);

    pBuf = malloc(len + 7);
    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pBuf, TBOX_ERROR_MALLOC_FAIL);

    pBuf[0] = canCh;
    pBuf[1] = (uint8_t)(canId >> 24);
    pBuf[2] = (uint8_t)(canId >> 16);
    pBuf[3] = (uint8_t)(canId >> 8);
    pBuf[4] = (uint8_t)canId;
    pBuf[5] = (uint8_t)(len >> 8);
    pBuf[6] = (uint8_t)len;
    memcpy(pBuf + 7, cmdData, len);
    SystemPrintHexData(CLIENT_MODULE_DIAG, TBOX_LOG_INFO, pBuf, len + 7, "CanDiag:ipc Send Diag data:");
    ret = SendMessageByIpc(MESSAGE_TX_VEHICLE_REMOTE_DIAGNOSIS, len + 7, pBuf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Send remote diagnosis command failed, canCh=%u, canId=%u, len=%u, ret=%d.", canCh, canId, len, ret);
    }

    free(pBuf);
    return ret;
}

/*************************************************
函数名称: IpcsendUpgradeCmd
函数功能: 发送升级命令
输入参数: type - 升级目标(mcu/bt)，id - 升级ID， count - 要传送的包个数，status - 状态(开始/结束)
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcsendUpgradeCmd(uint8_t type, uint16_t id, uint16_t count, uint8_t status)
{
    int ret = TBOX_OK;
    uint8_t buf[6];

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO,
                     "Send upgrade command in ipcc, type=%u, id=%u, count=%u, status=%u.", type, id, count, status);

    buf[0] = type;
    buf[1] = (uint8_t)(count >> 0);
    buf[2] = (uint8_t)(count >> 8);
    buf[3] = (uint8_t)(id >> 0);
    buf[4] = (uint8_t)id >> 8;
    buf[5] = status;
    ret = SendMessageByIpc(MESSAGE_TX_REMOTE_UPGRADE_DOWNLOAD, sizeof(buf), buf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Send upgrade command failed, "
                         "type=%u, id=%u, count=%u, status=%u, ret=%d.", type, id, count, status, ret);
    }

    return ret;
}


/*************************************************
函数名称: IpcSetCanTransSwitch
函数功能: 设置Can传输开关
输入参数: switchOn - 0: 关闭CAN传输，1:打开CAN传输
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/03
*************************************************/
int IpcSetCanTransSwitch(boolean switchOn)
{
    int ret = TBOX_OK;

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Set can transfer switch in ipcc, status=%u.", switchOn);

    ret = SendMessageByIpc(MESSAGE_TX_CAN_LOG_CONTROL_COMMAND, 1, (uint8_t *)&switchOn);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Set can transfer switch(%u) failed, ret=%d.", switchOn, ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcSetRtcWakeTime
函数功能: 设置休眠后RTC唤醒时间
输入参数: type - 时间类型，time - 唤醒时间，与类型对应
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/03
*************************************************/
int IpcSetRtcWakeTime(RtcWakeupType type, uint8_t day, uint8_t hour, uint8_t min, uint8_t sec)
{
    int ret = TBOX_OK;
    uint8_t buf[5] = {0};

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Set rtc wakeup time in ipcc, type=%d, time=%u.", type, day);

    buf[0] = (uint8_t)type;
    buf[1] = day;
    buf[2] = hour;
    buf[3] = min;
    buf[4] = sec;
    ret = SendMessageByIpc(MESSAGE_TX_SET_WAKEUP_TIME, sizeof(buf), buf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Set rtc wakeup type=%d, time(%u) failed, ret=%d.", type, day, ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcSetRtcAlarmTime -- 与setRtcWakeupTime相似，废弃
函数功能: 设置休眠后RTC唤醒时间
输入参数: alarmTime，唤醒时间
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/03
*************************************************/
int IpcSetRtcAlarmTime(const RtcAlarmTime *alarmTime)
{
    int ret = TBOX_OK;
//
//    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Set alarm time in ipcc, w=%u, d=%u, h=%u, m=%u, s=%u.",
//                     alarmTime->week, alarmTime->day, alarmTime->hour, alarmTime->minute, alarmTime->second);
//
//    ret = SendMessageByIpc(MESSAGE_TX_SET_RTC_ALARM, sizeof(RtcAlarmTime), (uint8_t *)alarmTime);
//    if (TBOX_OK != ret)
//    {
//        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Send set alarm time command failed, ret=%d.", ret);
//    }

    return ret;
}

/*************************************************
函数名称: IpcSetMcuResetArm
函数功能: 通知MCU在ARM无响应时是否重启ARM
输入参数: resetArm，TRUE：重启，FALSE：不重启
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2022/06/30
*************************************************/
int IpcSetMcuResetArm(boolean resetArm)
{
    int ret = TBOX_OK;
    uint8_t resetFlag = 0;

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Notify mcu reset arm or not: %d.", resetArm);
    if(resetArm)
    {
        resetFlag = 1;
    }

    ret = SendMessageByIpc(MESSAGE_TX_MCU_RESET_ARM, sizeof(resetFlag), &resetFlag);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Notify mcu reset arm or not failed, ret=%d.", ret);
    }

    return ret;
}

/*************************************************
函数名称: IpcSendFtmCmd
函数功能: 发送装备命令
输入参数: index - xxx，cmdData - xxx，cmdLen - 命令长度
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcSendFtmCmd(uint8_t index, uint8_t cmdLen, uint8_t *cmdData)
{
    int ret = TBOX_OK;
    uint8_t *pBuf;

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, cmdData, TBOX_ERROR_NULL_POINTER);
    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "Send ftm command in ipcc, index=%u, len=%u.", index, cmdLen);

    pBuf = malloc(cmdLen + 1);
    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pBuf, TBOX_ERROR_MALLOC_FAIL);

    pBuf[0] = index;
    memcpy(pBuf + 1, cmdData, cmdLen);
    SystemPrintHexData(TBOX_MODULE_IPCC, TBOX_LOG_INFO, pBuf, cmdLen + 1, "send ftm ipc msg: ");
    ret = SendMessageByIpc(MESSAGE_TX_ARM_FTM_COMMAND, cmdLen + 1, pBuf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Send ftm command failed, index=%u, len=%u, ret=%d.", index, cmdLen, ret);
    }

    free(pBuf);
    return ret;
}

/*************************************************
函数名称: IpcSendPcFtmCmd
函数功能: 发送PC装备命令
输入参数: index - xxx，cmdData - xxx，cmdLen - 命令长度
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcSendPcFtmCmd(uint8_t index, uint8_t cmdLen, uint8_t *cmdData)
{
    int ret = TBOX_OK;
    uint8_t *pBuf;

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, cmdData, TBOX_ERROR_NULL_POINTER);
    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "Send PC ftm command in ipcc, index=%u, len=%u.", index, cmdLen);

    pBuf = malloc(cmdLen + 1);
    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pBuf, TBOX_ERROR_MALLOC_FAIL);

    pBuf[0] = index;
    memcpy(pBuf + 1, cmdData, cmdLen);
    ret = SendMessageByIpc(MESSAGE_TX_ARM_PC_COMMAND, cmdLen + 1, pBuf);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Send PC ftm command failed, index=%u, len=%u, ret=%d.", index, cmdLen, ret);
    }
    else
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                    "Send PC ftm command OK, index=%u, len=%u, ret=%d.", index, cmdLen, ret);
    }


    free(pBuf);
    return ret;
}

/*************************************************
函数名称: IpcSendDutFtmCmd
函数功能: 发送PC装备命令
输入参数: index - xxx，
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/06
*************************************************/
int IpcSendDutFtmCmd(uint8_t index)
{
    int ret = TBOX_OK;

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "Send dut ftm command in ipcc, index=%u.", index);

    ret = SendMessageByIpc(MESSAGE_TX_ARM_DUT_COMMAND, 1, &index);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Send dut ftm command failed, index=%u, ret=%d.", index, ret);
    }

    return ret;
}

/*************************************************
函数名称: SetIpcHandShakeStatus
函数功能: 设置与mcu握手的状态
输入参数: status - 握手状态
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/10
*************************************************/
int SetIpcHandShakeStatus(HandShakeStatus status)
{
    int ret = TBOX_OK;
    DBusMsgParaInfo_s paraInfo;
    const char *appName = NULL;

    appName = DbusGetConnectionName();
    if (NULL == appName)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Get app name failed.");
        return DBUS_ERROR_GET_NAME_FAIL;
    }
    if (0 != strcmp(appName, DBUS_APP_SYSCTRL_NAME))
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Only system control can set hand shake status.");
        return TBOX_ERROR_PERMISSION_DENIED;
    }

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Set hand shake status in ipcc, status=%u.", status);

    paraInfo.type = TBOX_DBUS_TYPE_UINT8;
    paraInfo.typeInfo.u8 = (uint8_t)status;
    ret = SetMessageByDBus(DBUS_SERVICE_IPC_NAME, DBUS_MODULE_IPC, DBUS_ACTION_SET_SHAKE_STATUS, &paraInfo, IPC_SEND_MSG_TIME_OUT);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Send hand shake status failed, status=%u, ret=%d.", status, ret);
    }

    return ret;
}

/*************************************************
函数名称: GetIpcHandShakeStatus
函数功能: 获取IPC握手状态
输入参数: 无
输出参数: 无
返回值：握手状态
编写者: zhengyong
编写日期 :2020/01/19
*************************************************/
HandShakeStatus GetIpcHandShakeStatus(void)
{
    int ret = TBOX_OK;
    DBusMsgParaInfo_s returnInfo = {0};

    ret = GetMessageByDBus(DBUS_SERVICE_IPC_NAME, DBUS_MODULE_IPC, DBUS_ACTION_GET_SHAKE_STATUS, &returnInfo, IPC_SEND_MSG_TIME_OUT);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Get ipc hand shake status failed, ret=%d.", ret);
        return HANDSHAKE_FAIL;
    }

    DBUS_CHECK_REMOTE_ERROR_MESSAGE(TBOX_MODULE_IPCC, (&returnInfo), "Get ipc hand shake status", HANDSHAKE_FAIL);
    DBUS_CHECK_WRONG_TYPE_MESSAGEC(TBOX_MODULE_IPCC, TBOX_DBUS_TYPE_UINT32,
                                   (&returnInfo), "Get ipc hand shake status", HANDSHAKE_FAIL);

    return returnInfo.typeInfo.u32;
}


/*************************************************
函数名称: SetIpcMcuWorkMode
函数功能: 设置MCU的工作模式，只是ipc内部使用，bootloader下部分消息不能发送
输入参数: mode - MCU当前工作模式
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/10
*************************************************/
int SetIpcMcuWorkMode(McuWorkMode mode)
{
    int ret = TBOX_OK;
    DBusMsgParaInfo_s paraInfo;
    const char *appName = NULL;

    appName = DbusGetConnectionName();
    if (NULL == appName)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Get app name failed.");
        return DBUS_ERROR_GET_NAME_FAIL;
    }
    if (0 != strcmp(appName, DBUS_APP_SYSCTRL_NAME))
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Only system control can set work mode.");
        return TBOX_ERROR_PERMISSION_DENIED;
    }

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Set mcu work mode in ipcc, mode=%u.", mode);

    paraInfo.type = TBOX_DBUS_TYPE_UINT8;
    paraInfo.typeInfo.u8 = (uint8_t)mode;
    ret = SetMessageByDBus(DBUS_SERVICE_IPC_NAME, DBUS_MODULE_IPC, DBUS_ACTION_SET_MCU_MODE, &paraInfo, IPC_SEND_MSG_TIME_OUT);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Send mcu work mode failed, mode=%u, ret=%d.", mode, ret);
    }

    return ret;
}

/*************************************************
函数名称: SetIpcTransferSwitch
函数功能: 设置IPC传输开关
输入参数: 0 - 关闭传输，1 - 打开传输
输出参数: 无
返回值：0 - 命令下发成功；其他，失败
编写者: zhengyong
编写日期 :2020/01/11
*************************************************/
int SetIpcTransferSwitch(boolean switchOn)
{
    int ret = TBOX_OK;
    DBusMsgParaInfo_s paraInfo;
    const char *appName = NULL;

    appName = DbusGetConnectionName();
    if (NULL == appName)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Get app name failed.");
        return DBUS_ERROR_GET_NAME_FAIL;
    }
    if (0 != strcmp(appName, DBUS_APP_SYSCTRL_NAME))
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Only system control can switch ipc transfer.");
        return TBOX_ERROR_PERMISSION_DENIED;
    }

    LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Set ipc transfer switch(%s) in ipcc.", switchOn ? "On" : "Off");

    paraInfo.type = TBOX_DBUS_TYPE_UINT8;
    paraInfo.typeInfo.u8 = switchOn ? 1 : 0;
    ret = SetMessageByDBus(DBUS_SERVICE_IPC_NAME, DBUS_MODULE_IPC, DBUS_ACTION_SET_IPC_SWITCH, &paraInfo, IPC_SEND_MSG_TIME_OUT);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Set ipc transfer switch(%s) failed, ret=%d.", switchOn ? "On" : "Off", ret);
    }

    return ret;
}

/*************************************************
函数名称: GetIpcTransferSwitch
函数功能: 获取IPC传输开关
输入参数: 无
输出参数: 无
返回值：0 - 关闭，1-打开
编写者: zhengyong
编写日期 :2020/01/11
*************************************************/
boolean GetIpcTransferSwitch(void)
{
    int ret = TBOX_OK;
    DBusMsgParaInfo_s returnInfo = {0};

    ret = GetMessageByDBus(DBUS_SERVICE_IPC_NAME, DBUS_MODULE_IPC, DBUS_ACTION_GET_IPC_SWITCH, &returnInfo, IPC_SEND_MSG_TIME_OUT);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Get ipc transfer switch failed, ret=%d.", ret);
        return FALSE;
    }

    DBUS_CHECK_REMOTE_ERROR_MESSAGE(TBOX_MODULE_IPCC, (&returnInfo), "Get ipc transfer switch", FALSE);
    DBUS_CHECK_WRONG_TYPE_MESSAGEC(TBOX_MODULE_IPCC, TBOX_DBUS_TYPE_UINT8,
                                   (&returnInfo), "Get ipc transfer switch", FALSE);

    return (boolean)returnInfo.typeInfo.u8;
}

static IpcRegCallback_s g_ipcCbs = {0};

/*************************************************
函数名称: HandleIpcSysMsg
函数功能: 广播系统类信号
输入参数: msgId - 消息ID，len - 数据长度，buf - 数据
输出参数: 无
返回值：无
编写者: zhengyong
编写日期 :2020/01/09
*************************************************/
static void HandleIpcSysMsg(uint16_t msgId, uint16_t len, uint8_t *buf)
{
    switch (msgId)
    {
        case MESSAGE_RX_WAKEUP_INFO:
            if (3 != len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for wakeup info.", len);
                break;
            }
            if (NULL != g_ipcCbs.pWakeup)
            {
                g_ipcCbs.pWakeup(buf[0], buf[1], buf[2]);
            }
            break;
        case MESSAGE_RX_TIME_CALIBRATION:
            if (sizeof(TimeInfo) - 1 != len) //timeZone未使用，所以-1
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for calibrate time.", len);
                break;
            }
            if (NULL != g_ipcCbs.pCaliTime)
            {
                TimeInfo timeInfo;
                timeInfo.second = buf[0];
                timeInfo.minute = buf[1];
                timeInfo.hour = buf[2];
                timeInfo.day = buf[3];
                timeInfo.week = buf[4];
                timeInfo.month = buf[5];
                timeInfo.year = buf[6];
                timeInfo.zoneTime = 0xff;
                g_ipcCbs.pCaliTime(&timeInfo);
            }
            break;
        case MESSAGE_RX_PM_REQUEST:
            if (1 != len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for pm report.", len);
                break;
            }
            if (NULL != g_ipcCbs.pStatusReport)
            {
                g_ipcCbs.pStatusReport(buf[0]);
            }
            break;
        case MESSAGE_RX_POWER_ON_REPORT:
            if (7 != len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for poweron report.", len);
                break;
            }
            if (NULL != g_ipcCbs.pPoweronReport)
            {
                g_ipcCbs.pPoweronReport(buf);
            }
            break;
        case MESSAGE_RX_TBOX_HARD_CONFIG:
            if (1 != len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for hard config.", len);
                break;
            }
            if (NULL != g_ipcCbs.pHardInfo)
            {
                g_ipcCbs.pHardInfo(buf[0]);
            }
            break;
        case MESSAGE_RX_HEART_BEAT_INFO:
            if (len == 0)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for peroid info.", len);
                break;
            }
            if (NULL != g_ipcCbs.pHeartBeatInfo)
            {
                g_ipcCbs.pHeartBeatInfo(len, buf);
            }
            break;
        default:
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Non supported signal for system, msgid:0x%x.", msgId);
            break;
    }
}

/*************************************************
函数名称: HandleIpcSafeMsg
函数功能: 广播安全类信号
输入参数: msgId - 消息ID，len - 数据长度，buf - 数据
输出参数: 无
返回值：无
编写者: zhengyong
编写日期 :2020/01/09
*************************************************/
static void HandleIpcSafeMsg(uint16_t msgId, uint16_t len, uint8_t *buf)
{
    switch (msgId)
    {
        case MESSAGE_RX_ECALL_REQUEST:
            if (1 != len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for ecall.", len);
                break;
            }
            if (NULL != g_ipcCbs.pECall)
            {
                g_ipcCbs.pECall(buf[0]);
            }
            break;
        default:
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Non supported signal for safe, msgid:0x%x.", msgId);
            break;
    }
}

/*************************************************
函数名称: HandleIpcBtMsg
函数功能: 广播蓝牙类信号
输入参数: msgId - 消息ID，len - 数据长度，buf - 数据
输出参数: 无
返回值：无
编写者: zhengyong
编写日期 :2020/01/09
*************************************************/
static void HandleIpcBtMsg(uint16_t msgId, uint16_t len, uint8_t *buf)
{
    uint8_t cmdType;

    switch (msgId)
    {
        case MESSAGE_RX_BT_RX_DATA:
            if (1 > len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for bt cmd.", len);
                break;
            }
            cmdType = buf[0];
            if (BT_RX_NORMAL_STATUS == cmdType && NULL != g_ipcCbs.pBtCmdRes)
            {
                g_ipcCbs.pBtCmdRes(len - 1, buf + 1);
            }
            else if (BT_RX_UPGRADE_STATUS == cmdType && NULL != g_ipcCbs.pBtUpgRes)
            {
                g_ipcCbs.pBtUpgRes(len - 1, buf + 1);
            }
            break;
        default:
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Non supported signal for bt cmd, msgid:0x%x.", msgId);
            break;
    }
}

/*************************************************
函数名称: HandleIpcUpgMsg
函数功能: 广播升级类信号
输入参数: msgId - 消息ID，len - 数据长度，buf - 数据
输出参数: 无
返回值：无
编写者: zhengyong
编写日期 :2020/01/09
*************************************************/
static void HandleIpcUpgMsg(uint16_t msgId, uint16_t len, uint8_t *buf)
{
    switch (msgId)
    {
        case MESSAGE_RX_MCU_UPLOAD_FILE:
            if (0 == len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for upload file.", len);
                break;
            }
            if (NULL != g_ipcCbs.pUploadFile)
            {
                g_ipcCbs.pUploadFile(len, buf);
            }
            break;
        case MESSAGE_RX_REMOTE_UPGRADE_RESPONSE:
            if (0 == len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for upgrade cmd res.", len);
                break;
            }
            if (NULL != g_ipcCbs.pRemoteUpgCmdRes)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "callback pRemoteUpgCmdRes......");
                g_ipcCbs.pRemoteUpgCmdRes(len, buf);
            }
            break;
        case MESSAGE_RX_UPDATE_CODE_RESPONSE:
            if (3 != len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for upgrade data res.", len);
                break;
            }
            if (NULL != g_ipcCbs.pRemoteUpgDataRes)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "callback pRemoteUpgDataRes......");
                g_ipcCbs.pRemoteUpgDataRes(buf[0], (uint16_t)buf[1] << 8 | buf[2]);
            }
            break;
        default:
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Non supported signal for upgrade, msgid:0x%x.", msgId);
            break;
    }
}


/*************************************************
函数名称: HandleIpcDidMsg
函数功能: 广播DID类信号
输入参数: msgId - 消息ID，len - 数据长度，buf - 数据
输出参数: 无
返回值：无
编写者: zhengyong
编写日期 :2020/01/09
*************************************************/
static void HandleIpcDidMsg(uint16_t msgId, uint16_t len, uint8_t *buf)
{
    switch (msgId)
    {
        case MESSAGE_RX_CAR_INFO:
            if (20 > len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for car info.", len);
                break;
            }

            if (NULL != g_ipcCbs.pCarInfo)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "car info len = %u.", len);
                g_ipcCbs.pCarInfo(buf);
            }
            break;
        case MESSAGE_RX_DID_INFO:
            if (4 > len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for did info.", len);
                break;
            }
            if (DID_WRITE_TYPE == buf[0] && NULL != g_ipcCbs.pWriteDidRes)
            {
                g_ipcCbs.pWriteDidRes((uint16_t)buf[2] << 8 | buf[3], buf[1]);
            }
            else if (DID_READ_TYPE == buf[0] && 4 <= len && NULL != g_ipcCbs.pDidInfo)
            {
                g_ipcCbs.pDidInfo((uint16_t)buf[2] << 8 | buf[3], len - 4, buf + 4);
            }
            break;
        default:
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Non supported signal for did info, msgid:0x%x.", msgId);
            break;
    }
}

/*************************************************
函数名称: HandleIpcDtcMsg
函数功能: 广播DTC类信号
输入参数: msgId - 消息ID，len - 数据长度，buf - 数据
输出参数: 无
返回值：无
编写者: zhengyong
编写日期 :2020/01/09
*************************************************/
static void HandleIpcDtcMsg(uint16_t msgId, uint16_t len, uint8_t *buf)
{
    uint16_t dtcNum = 0;

    switch (msgId)
    {
        case MESSAGE_RX_TBOX_DTC_RESPONSE:
            if (3 > len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for dtc info.", len);
                break;
            }
            if (DTC_SYSTEM_REQ == buf[0] && NULL != g_ipcCbs.pDtcInfo)
            {
                g_ipcCbs.pDtcInfo(&buf[1], len - 1);
            }
            else if (DTC_CLIENT_REQ == buf[0] && DTC_REQ_READ == buf[1] && NULL != g_ipcCbs.pRequestDtcRes)
            {
                if(buf[2] == DTC_REQ_SUCCESS_STATUS) //读取失败时，dtcNum为0
                {
                    dtcNum = (uint16_t)buf[5] << 8 | buf[6];
                }
                g_ipcCbs.pRequestDtcRes(dtcNum, buf[3], len - 7, buf + 7);
            }
            else if (DTC_CLIENT_REQ == buf[0] && DTC_REQ_CLEAR == buf[1] && NULL != g_ipcCbs.pRequestDtcRes)
            {
                g_ipcCbs.pRequestDtcRes(0, 0, 1, buf + 2);
            }
            break;
        case MESSAGE_RX_MCU_FAULT_INFO:
            if (1 > len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for mcu fault.", len);
                break;
            }
            if (NULL != g_ipcCbs.pMcuFaultInfo)
            {
                g_ipcCbs.pMcuFaultInfo(buf[0], len - 1, buf + 1);
            }
            break;
        default:
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Non supported signal for dtc, msgid:0x%x.", msgId);
            break;
    }
}

/*************************************************
函数名称: HandleIpcFtmMsg
函数功能: 广播装备类信号
输入参数: msgId - 消息ID，len - 数据长度，buf - 数据
输出参数: 无
返回值：无
编写者: zhengyong
编写日期 :2020/01/09
*************************************************/
static void HandleIpcFtmMsg(uint16_t msgId, uint16_t len, uint8_t *buf)
{
    switch (msgId)
    {
        case MESSAGE_RX_ARM_FTM_CMD:
            if (NULL != g_ipcCbs.pFtmInfo)
            {
                g_ipcCbs.pFtmInfo(len, buf);
            }
            break;
        default:
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Non supported signal for ftm, msgid:%u.", msgId);
            break;
    }
}

/*************************************************
函数名称: HandleIpcBusMsg
函数功能: 广播业务类信号
输入参数: msgId - 消息ID，len - 数据长度，buf - 数据
输出参数: 无
返回值：无
编写者: zhengyong
编写日期 :2020/01/09
*************************************************/
static void HandleIpcBusMsg(uint16_t msgId, uint16_t len, uint8_t *buf)
{
    switch (msgId)
    {
        case MESSAGE_RX_ARM_CAN_NET_NOTIFY:
            if (1 != len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for can status.", len);
                break;
            }
            if (NULL != g_ipcCbs.pCanStatus)
            {
                g_ipcCbs.pCanStatus(buf[1]);
            }
            break;
        case MESSAGE_RX_CAN_PARA_REPORT:
            if (NULL != g_ipcCbs.pCanReportStatus)
            {
                g_ipcCbs.pCanReportStatus(len, buf);
            }
            break;
        case MESSAGE_RX_REMOTE_CTRL_CAN_MSG_RES:
            if (1 > len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for remote control.", len);
                break;
            }
            if (NULL != g_ipcCbs.pRemoteCtrlCanMsgRes)
            {
                g_ipcCbs.pRemoteCtrlCanMsgRes(buf, len);
            }
            break;
        case MESSAGE_RX_VEHICLE_REMOTE_DIAGNOSIS:
            if (7 > len)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "length(%u) is error for remote diag.", len);
                break;
            }
            if (NULL != g_ipcCbs.pRemoteDiagRes)
            {
                uint32_t canId = (uint32_t)buf[1] << 24 | (uint32_t)buf[2] << 16 |
                                 (uint32_t)buf[3] << 8 | buf[4];
                uint16_t resLen = (uint16_t)buf[5] << 8 | buf[6];
                g_ipcCbs.pRemoteDiagRes(buf[0], canId, resLen, buf + 7);
            }
            break;
        default:
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Non supported signal for upgrade, msgid:0x%x.", msgId);
            break;
    }

}

void *HandleIpcCallback(void *arg)
{
    int ret;
    uint16_t msgId;
    DBusMessageIter iter;
    DBusMessage *msg;
    DBusMsgRecvInfo_s msgRecvInfo;
    DBusMsgParaInfo_s msgParaInfo;
    const char module[] = DBUS_MODULE_IPC_NOTIFY;
    uint32_t logCnt = 0;

    while (TRUE)
    {
        if(logCnt++ % 10000 == 0)
        {
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Ipcc call back running.");
        }
        msgRecvInfo.paraInfo.type = TBOX_DBUS_TYPE_INVALID;
        msgParaInfo.type = TBOX_DBUS_TYPE_INVALID;
        msgParaInfo.arrayLen = 0;
        msgParaInfo.typeInfo.array = NULL;
        msgRecvInfo.module = module;
        msgRecvInfo.action = NULL;

        //读取消息并获取第一个参数
        ret = DBusGetMessageBegin(&iter, &msg, &msgRecvInfo, 0);
        if (TBOX_OK != ret || NULL == msg)
        {
            msleep(IPC_HANDLE_CB_TIME_OUT);
            continue;
        }
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "get para1 ret=%d......", ret);
        if (TBOX_DBUS_TYPE_UINT16 != msgRecvInfo.paraInfo.type)
        {
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Got first message in ipc callback thread "
                             "but type(%c) of para is error.",  msgRecvInfo.paraInfo.type);
            DBusFreeMessage(msg);
            continue;
        }
        msgId = msgRecvInfo.paraInfo.typeInfo.u16;
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "get msgID = %d......", msgId);

        //如果有第二个参数，则读取
        if (DBusCheckNextMessage(&iter))
        {
            ret = DBusGetMessage(&iter, &msgParaInfo);
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "get para2 ret=%d......", ret);
            if (TBOX_OK != ret || TBOX_DBUS_TYPE_ARRAY != msgParaInfo.type)
            {
                LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Got second message in ipc callback thread"
                                 " but ret=%d or type(%c) of msg is error.", ret, msgRecvInfo.paraInfo.type);
                DBusFreeMessage(msg);
                continue;
            }
        }
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "start match msgID......");

        if (DBusCheckMessageMatch(msg, TRUE, DBUS_MODULE_IPC_NOTIFY, DBUS_ACTION_NOTIFY_SYS))
        {
            HandleIpcSysMsg(msgId, msgParaInfo.arrayLen, msgParaInfo.typeInfo.array);
        }
        else if (DBusCheckMessageMatch(msg, TRUE, DBUS_MODULE_IPC_NOTIFY, DBUS_ACTION_NOTIFY_SAFE))
        {
            HandleIpcSafeMsg(msgId, msgParaInfo.arrayLen, msgParaInfo.typeInfo.array);
        }
        else if (DBusCheckMessageMatch(msg, TRUE, DBUS_MODULE_IPC_NOTIFY, DBUS_ACTION_NOTIFY_BT))
        {
            HandleIpcBtMsg(msgId, msgParaInfo.arrayLen, msgParaInfo.typeInfo.array);
        }
        else if (DBusCheckMessageMatch(msg, TRUE, DBUS_MODULE_IPC_NOTIFY, DBUS_ACTION_NOTIFY_UPG))
        {
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_DEBUG, "match msgID DBUS_ACTION_NOTIFY_UPG......");
            HandleIpcUpgMsg(msgId, msgParaInfo.arrayLen, msgParaInfo.typeInfo.array);
        }
        else if (DBusCheckMessageMatch(msg, TRUE, DBUS_MODULE_IPC_NOTIFY, DBUS_ACTION_NOTIFY_DID))
        {
            HandleIpcDidMsg(msgId, msgParaInfo.arrayLen, msgParaInfo.typeInfo.array);
        }
        else if (DBusCheckMessageMatch(msg, TRUE, DBUS_MODULE_IPC_NOTIFY, DBUS_ACTION_NOTIFY_DTC))
        {
            HandleIpcDtcMsg(msgId, msgParaInfo.arrayLen, msgParaInfo.typeInfo.array);
        }
        else if (DBusCheckMessageMatch(msg, TRUE, DBUS_MODULE_IPC_NOTIFY, DBUS_ACTION_NOTIFY_FTM))
        {
            HandleIpcFtmMsg(msgId, msgParaInfo.arrayLen, msgParaInfo.typeInfo.array);
        }
        else if (DBusCheckMessageMatch(msg, TRUE, DBUS_MODULE_IPC_NOTIFY, DBUS_ACTION_NOTIFY_BUS))
        {
            HandleIpcBusMsg(msgId, msgParaInfo.arrayLen, msgParaInfo.typeInfo.array);
        }
        else
        {
            LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "Non supported signal, msgid:0x%x, action:%s.",
                             msgId, msgRecvInfo.action);
        }

        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "Got ipc notify, msgid=0x%x.", msgId);
        DBusFreeMessageInfo(&msgParaInfo);
        DBusGetMessageEnd(msg, NULL);
    }
}

/*************************************************
函数名称: SetIpcReceiveMatch
函数功能:设置DBUS接收信号过滤规则
输入参数: pIpcCallback-需要注册的函数指针，不需要收置为NULL
输出参数: hasCallbackFunc-是否有注册函数
返回值：0 - 成功，其他-失败
编写者: zhengyong
编写日期 :2020/01/11
*************************************************/
static int SetIpcReceiveMatch(IpcRegCallback_s *pIpcCallback, boolean *hasCallbackFunc)
{
    int ret;
    boolean temp;

    temp = (NULL != pIpcCallback->pStatusReport || NULL != pIpcCallback->pWakeup ||
            NULL != pIpcCallback->pCaliTime || NULL != pIpcCallback->pPoweronReport ||
            NULL != pIpcCallback->pHardInfo || NULL != pIpcCallback->pHeartBeatInfo);
    if (temp)
    {
        ret = DBusSetReceiveMatch(DBUS_FILTER_IPC_SYS);
        TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_IPCC, ret, "Set ipc system receive match failed", ret);
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "%s regist sys call back func to ipc:"
                         "StatusReport:%s, Wakeup:%s, CaliTime:%s, PoweronReport:%s, HardInfo:%s, HeartBeatInfo:%s.",
                         DbusGetConnectionName(), NULL != pIpcCallback->pStatusReport ? "yes" : "no",
                         NULL != pIpcCallback->pWakeup ? "yes" : "no", NULL != pIpcCallback->pCaliTime ? "yes" : "no",
                         NULL != pIpcCallback->pPoweronReport ? "yes" : "no", NULL != pIpcCallback->pHardInfo ? "yes" : "no",
                         NULL != pIpcCallback->pHeartBeatInfo ? "yes" : "no");
        *hasCallbackFunc = TRUE;
    }

    temp = (NULL != pIpcCallback->pBtCmdRes || NULL != pIpcCallback->pBtUpgRes);
    if (temp)
    {
        ret = DBusSetReceiveMatch(DBUS_FILTER_IPC_BT);
        TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_IPCC, ret, "Set ipc bt receive match failed", ret);
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "%s regist bt call back func to ipc:"
                         "BtCmdRes:%s, BtUpgRes:%s.", DbusGetConnectionName(),
                         NULL != pIpcCallback->pBtCmdRes ? "yes" : "no", NULL != pIpcCallback->pBtUpgRes ? "yes" : "no");
        *hasCallbackFunc = TRUE;
    }

    if (NULL != pIpcCallback->pECall)
    {
        ret = DBusSetReceiveMatch(DBUS_FILTER_IPC_SAFE);
        TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_IPCC, ret, "Set ipc safe receive match failed", ret);
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "%s regist safe call back func to ipc:"
                         "ECall:%s.", DbusGetConnectionName(), NULL != pIpcCallback->pECall ? "yes" : "no");
        *hasCallbackFunc = TRUE;
    }

    temp = (NULL != pIpcCallback->pUploadFile ||
            NULL != pIpcCallback->pRemoteUpgCmdRes ||
            NULL != pIpcCallback->pRemoteUpgDataRes);
    if (temp)
    {
        ret = DBusSetReceiveMatch(DBUS_FILTER_IPC_UPG);
        TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_IPCC, ret, "Set ipc upgrade receive match failed", ret);
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "%s regist upg call back func to ipc:"
                         "UploadFile:%s, RemoteUpgCmdRes:%s, RemoteUpgDataRes:%s.", DbusGetConnectionName(),
                         NULL != pIpcCallback->pUploadFile ? "yes" : "no", NULL != pIpcCallback->pRemoteUpgCmdRes ? "yes" : "no",
                         NULL != pIpcCallback->pRemoteUpgDataRes ? "yes" : "no");
        *hasCallbackFunc = TRUE;
    }

    temp = (NULL != pIpcCallback->pCarInfo ||
            NULL != pIpcCallback->pDidInfo ||
            NULL != pIpcCallback->pWriteDidRes);
    if (temp)
    {
        ret = DBusSetReceiveMatch(DBUS_FILTER_IPC_DID);
        TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_IPCC, ret, "Set ipc did receive match failed", ret);
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "%s regist did call back func to ipc:"
                         "CarInfo:%s, DidInfo:%s, WriteDidRes:%s.", DbusGetConnectionName(),
                         NULL != pIpcCallback->pCarInfo ? "yes" : "no", NULL != pIpcCallback->pDidInfo ? "yes" : "no",
                         NULL != pIpcCallback->pWriteDidRes ? "yes" : "no");
        *hasCallbackFunc = TRUE;
    }

    temp = (NULL != pIpcCallback->pDtcInfo ||
            NULL != pIpcCallback->pMcuFaultInfo ||
            NULL != pIpcCallback->pRequestDtcRes);
    if (temp)
    {
        ret = DBusSetReceiveMatch(DBUS_FILTER_IPC_DTC);
        TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_IPCC, ret, "Set ipc dtc receive match failed", ret);
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "%s regist dtc call back func to ipc:"
                         "DtcInfo:%s, McuFaultInfo:%s, RequestDtcRes:%s.", DbusGetConnectionName(),
                         NULL != pIpcCallback->pDtcInfo ? "yes" : "no", NULL != pIpcCallback->pMcuFaultInfo ? "yes" : "no",
                         NULL != pIpcCallback->pRequestDtcRes ? "yes" : "no");
        *hasCallbackFunc = TRUE;
    }

    if (NULL != pIpcCallback->pFtmInfo)
    {
        ret = DBusSetReceiveMatch(DBUS_FILTER_IPC_FTM);
        TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_IPCC, ret, "Set ipc ftm receive match failed", ret);
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "%s regist ftm call back func to ipc:"
                         "FtmInfo:%s.", DbusGetConnectionName(), NULL != pIpcCallback->pFtmInfo ? "yes" : "no");
        *hasCallbackFunc = TRUE;
    }

    temp = (NULL != pIpcCallback->pCanReportStatus || NULL != pIpcCallback->pCanStatus ||
            NULL != pIpcCallback->pRemoteDiagRes || NULL != pIpcCallback->pRemoteCtrlCanMsgRes);
    if (temp)
    {
        ret = DBusSetReceiveMatch(DBUS_FILTER_IPC_BUS);
        TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_IPCC, ret, "Set ipc business receive match failed", ret);
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_INFO, "%s regist business call back func to ipc:"
                         "CanReportStatus:%s, CanStatus:%s, RemoteDiagRes:%s, RemoteCtrlCanMsgRes:%s ",
                         DbusGetConnectionName(), NULL != pIpcCallback->pCanReportStatus ? "yes" : "no",
                         NULL != pIpcCallback->pCanStatus ? "yes" : "no", NULL != pIpcCallback->pRemoteDiagRes ? "yes" : "no",
                         NULL != pIpcCallback->pRemoteCtrlCanMsgRes ? "yes" : "no");
        *hasCallbackFunc = TRUE;
    }

    memcpy(&g_ipcCbs, pIpcCallback, sizeof(IpcRegCallback_s));
    return TBOX_OK;
}

/*************************************************
函数名称: RegistIpcCallback
函数功能:向ipc注册回调函数
输入参数: pIpcCallback-需要注册的函数指针，不需要收置为NULL
输出参数: 无
返回值：0 - 成功，其他-失败
编写者: zhengyong
编写日期 :2020/01/11
*************************************************/
int RegistIpcCallback(IpcRegCallback_s *pIpcCallback)
{
    boolean hasCallbackFunc = FALSE;
    int32_t ret;
    pthread_t tid;
    pthread_attr_t attr;

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_IPCC, pIpcCallback, TBOX_ERROR_NULL_POINTER);

    ret = SetIpcReceiveMatch(pIpcCallback, &hasCallbackFunc);
    TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_IPCC, ret, "Set ipc receive match failed", ret);

    if (!hasCallbackFunc)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR, "No callback function in RegistIpcCallback.");
        return IPC_ERROR_NO_CALLBACK_FUNC;
    }

    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);

    ret = pthread_create(&tid, &attr, HandleIpcCallback, NULL);
    if (ret < 0)
    {
        LogServiceBuffer(TBOX_MODULE_IPCC, TBOX_LOG_ERROR,
                         "Create thread for ipc callback function failed: %s.", strerror(errno));
        return TBOX_ERROR;
    }

    return TBOX_OK;
}




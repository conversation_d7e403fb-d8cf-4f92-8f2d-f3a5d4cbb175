/*
      tbox_typedef.h
描述：此文件主要是提供一些公共类型定义，包括enum,strut等
作者：zhengyong
时间：2020.01.10
*/

#ifndef __TBOX_TYPEDEF_H__
#define __TBOX_TYPEDEF_H__

#include <stdint.h>
#include "tbox_macro.h"
#include "tbox_paraconfig.h"
typedef char (*WhiteList)[SMS_WAKEUP_WL_UNIT_LEN];

#ifndef boolean
#define boolean uint8_t
#endif

/************************数据结构定义***************************/

typedef enum
{
    MCU_WAKEUP_BPLUS = 0,        // B+ 上电唤醒
    MCU_WAKEUP_ACC,              // ACC 唤醒
    MCU_WAKEUP_LPS,              // LPS 唤醒
    MCU_WAKEUP_RTC,              // RTC 唤醒
    MCU_WAKEUP_CAN1,             // CAN1 唤醒
    MCU_WAKEUP_CAN2,             // CAN2 唤醒
    MCU_WAKEUP_CAN3,             // CAN3 唤醒
    MCU_WAKEUP_CAN4,             // CAN4 唤醒
    MCU_WAKEUP_CAN5,             // CAN5 唤醒
    MCU_WAKEUP_MODULE,           // 模块唤醒
    MCU_WAKEUP_WATCHDOE,         // 看门狗唤醒
    MCU_WAKEUP_EXTERN_RESET,     // 复位管脚唤醒
    MCU_WAKEUP_SOFT_RESET,       // 软复位唤醒
    MCU_WAKEUP_UNKNOWN,          // 未知唤醒
    MCU_WAKEUP_SENSOR,           // SENSOR 唤醒
    MCU_WAKEUP_BLE,              // 蓝牙唤醒
    MCU_WAKEUP_ECALL,            //E-Call唤醒
    TBOX_WAKEUP_FCHG,             // 快充 唤醒
    TBOX_WAKEUP_SCHG,             // 慢充 唤醒
    MCU_WAKEUP_SMS = MCU_WAKEUP_MODULE + 10, //短信唤醒
    MCU_WAKEUP_PHONECALL = MCU_WAKEUP_MODULE + 11, //电话唤醒
    MCU_WAKEUP_TSP_PLATFORM = MCU_WAKEUP_MODULE + 12, //TSP 远程唤醒
} WakeupSource;

typedef enum
{
    TIME_FLAG_NOT_CALC = 0,
    TIME_FLAG_MCU_CALC,
    TIME_FLAG_GPS_CALC,
    TIME_FLAG_MODEM_CALC,
} TimeCalcFlag;

typedef enum
{
    ARM_TYPE_ECALL = 0,
    ARM_TYPE_TSP,
    ARM_TYPE_SVT,
    ARM_TYPE_NET,
    ARM_TYPE_DIAG,
    ARM_TYPE_DTC,
    ARM_TYPE_OTAMODE,
    ARM_TYPE_OTANETWORK,
} ArmWorkType;

typedef enum
{
    ARM_STATUS_INACTIVE = 0,
    ARM_STATUS_ACTIVE,
    ARM_STATUS_POWER_ON,
} ArmWorkStatus;

typedef enum
{
    ARM_NULL = 0,
    ARM_REMOTE_CTRL,
    ARM_REMOTE_DIAG,
    ARM_REMOTE_OTAMODE,
    ARM_REMOTE_OTANETWORK,
} ArmWorkSource;    //目前用于表示网络唤醒源，远控或远程诊断，以后有需求可覆盖到其他控制

typedef enum
{
    WORK_STATUS_UNKNOW = -1,
    WORK_STATUS_INACTIVE = 0,
    WORK_STATUS_ACTIVE,
} WorkStatus;

typedef enum
{
    ARM_CLINET_OFFLINE = 0,
    ARM_CLINET_ONLINE,
} ArmClinetStatus;

typedef enum
{
    BT_NORMAL_SEND_DATA = 1,
    BT_UPGRADE_SEND_DATA,
} BtCmdIndex;

typedef enum
{
    BT_RX_IDLE = 0,
    BT_RX_NORMAL_STATUS = 1,
    BT_RX_UPGRADE_STATUS,
    BT_RX_TEST,
} BtRxCmd;

typedef enum
{
    DID_WRITE_TYPE = 0x00,
    DID_READ_TYPE,
} DidRwType;


typedef enum
{
    DID_RW_RESULT_SUCCESS = 0x00,
    DID_RW_RESULT_FAIL,
} DidRwReult;

typedef enum
{
    /*无RTC唤醒请求*/
    RTC_WAKEUP_NONE = 0,
    /*设置RTC在指定minute时间后唤醒*/
    RTC_WAKEUP_MINUTE,
    /*设置RTC在指定hour时间后唤醒*/
    RTC_WAKEUP_HOUR,
    /*设置RTC在指定second时间后唤醒*/
    RTC_WAKEUP_SECOND,
    /*设置RTC在指定day时间后唤醒*/
    RTC_WAKEUP_DAY,
    /*设置RTC在指定hour,minute, second时间后唤醒*/
    RTC_WAKEUP_SPECIFIED,
} RtcWakeupType;

typedef enum
{
    AUDIO_CMD_START_SPEAKER = 0x01,
    AUDIO_CMD_STOP_SPEAKER,
    AUDIO_CMD_PHONE_VOICE,
    AUDIO_CMD_FTM_TEST,
    AUDIO_CMD_EMC_TEST,
    AUDIO_CMD_START_RECORD,
    AUDIO_CMD_STOP_RECORD,
} AudioCmdType;

typedef enum
{
    MCU_FAULT_TYPE_BPLUS_LOW = 0,   /*低电类型状态通知*/
    MCU_FAULT_BURGLAR_ALARM,        /*防盗报警类型通知*/
    MCU_VEHILCE_BLE_KEY_REQUEST,    /*蓝牙钥匙状态请求*/
    MCU_FAULT_AIRBAG,               /*安全气囊类型通知*/
    MCU_FAULT_IG_STATUS = 0x06,     /*车辆IG点火状态通知*/
} McuFaultType;

typedef enum
{
    MCU_PACKET_ACK_RX_SUCESS = 0,
    MCU_PACKET_ACK_LEN_ERROR,
    MCU_PACKET_ACK_REPEAT_FRAME,
    MCU_PACKET_ACK_FRAME_ERROR,
    MCU_PACKET_ACK_CRC_ERROR,
    MCU_PACKET_ACK_WAIT,
    MCU_PACKET_ACK_TIME_OUT,
    MCU_PACKET_ACK_RX_READY, //准备好接收数据
} McuPacketAck;

typedef enum
{
    TX_FILE_MCU_LOG = 0,                      //上传的文件类型是 MCU 日志文件
    TX_FILE_ARM_UPGRADE_FILE,                 //上传的文件类型是 ARM 的升级文件
} TxFileType;


typedef enum
{
    TX_FILE_PROPERTY_INFO = 0,                      //上传的文件信息，如大小，地址，长度
    TX_FILE_DATA_INFO,                              // 上传的文件内容信息
    TX_FILE_CRC_INFO,
    TX_FILE_RESET_INFO,
} TxFileInfo;

typedef enum
{
    DTC_FAULT_CURRENT_MASK = 0x01,     /*当前故障*/
    DTC_FAULT_CONFIRMED_MASK = 0x08,   /*历史故障*/
    ALL_FAULT_ALL_MASK = 0x09,         /*历史故障 +  当前故障*/
} DtcFaultRequestMaskValue;

typedef enum
{
    G4_GSM_NOT_REGISTERED = 0, /* 0x00: GSM没注网 */
    G4_GSM_REGISTERED,         /* 0x01: GSM注网*/
    G4_CONNECT_NETWORK,         /* 0x02: GSM注网，且ping通 */
} ModuleStatus;

typedef enum
{
    TBOX_WORK_NORMAL = 0x00,
    TBOX_WORK_FTM,
} ArmWorkMode;

typedef enum
{
    NETWORK_STATUS_NO_SIGNAL = 0,             //无信号
    NETWORK_STATUS_VERY_WEAK,                 //极弱
    NETWORK_STATUS_WEAK,                      //较弱
    NETWORK_STATUS_STRONG,                    //较强
    NETWORK_STATUS_VERY_STRONG,               //极强
} NWSigStrength;

typedef enum
{
    NAD_STATUS_NORAML = 0,                     //NAD 工作正常
    NAD_STATUS_ABNORAML,                       //NAD 工作异常
} NadWorkStatus;

typedef enum
{
    DTC_SYSTEM_REQ = 0x00,
    DTC_CLIENT_REQ,
}DtcReqSource;

/*这部分的值和RPC client.h 文件中的DTCReqType类型相同，只是client中的文件需要对客户开放*/
typedef enum
{
    DTC_REQ_RESTART_CHECK = 0x00,
    DTC_REQ_STOP_CHECK,
    DTC_REQ_CLEAR,
    DTC_REQ_READ,
    DTC_REQ_SYNC,
} DTCReqType;


typedef enum
{
    DTC_REQ_SUCCESS_STATUS = 0x00,
    DTC_REQ_FAIL_STATUS,
}DtcReqResultStatus;

typedef enum
{
    HANDSHAKE_FAIL   = 0,
    HANDSHAKE_SUCCESS,
    HANDSHAKE_WAIT,
} HandShakeStatus;

typedef enum
{
    MCU_UNINIT,
    MCU_BOOTLOADER,
    MCU_USERAPP,
} McuWorkMode;

typedef enum
{
    PM_STATUS_NORAML = 0,                          // MCU 正常工作模式
    PM_STATUS_LIGHT_SLEEP,                         // MCU 轻度睡眠模式
    PM_STATUS_DEEP_SLEEP,                          // MCU 深度睡眠模式
    PM_STATUS_BACKUP_NORAML,                       // MCU 备电正常模式
    PM_STATUS_BACKUP_SLEEP,                        // MCU 备电睡眠模式
    PM_STATUS_POWER_OFF,                           // MCU 关机模式
    PM_STATUS_NORAML_SLEEP_READY,                  // MCU 准备正常模式睡眠中
    PM_STATUS_BACKUP_SLEEP_READY,                  // MCU 准备备电模式睡眠中
    PM_STATUS_ENTER_NORAML_SLEEP,                  // MCU 进入正常模式睡眠中
    PM_STATUS_ENTER_BPLUS_LOW_SLEEP,               //mcu进入bPlus低电睡眠
    PM_STATUS_ENTER_BACKUP_SLEEP,                  // MCU 进入备电模式睡眠中
    PM_STATUS_WAIT_CONFIRM_SLEEP,                  //MCU等待ARM应答，只MCU使用
    PM_STATUS_MCU_SOFTREST = 0x55,                 //mcu  进入软复位模式， 此时ARM工作正常 ,用于升级中使用
} PmStatus;

typedef enum
{
    DTC_CAN_BUS_OFF_INDEX = 0x00,
    DTC_EXT_POWER_VOL_LOW_INDEX,
    DTC_EXT_POWER_VOL_HIGH_INDEX,
    DTC_LOST_COM_WITH_HECU_INDEX,
    DTC_LOST_COM_WITH_BCM_INDEX,
    DTC_LOST_COM_WITH_BMS_INDEX,
    DTC_LOST_COM_WITH_MCU_INDEX,
    DTC_LOST_COM_WITH_DCDC_INDEX,
    DTC_LOST_COM_WITH_ABS_INDEX,
    DTC_LOST_COM_WITH_IC_INDEX,
    DTC_LOST_COM_WITH_APU_INDEX,
    DTC_GNSS_MODULE_FAULT_INDEX,
    DTC_REMOTE_COM_FAULT_INDEX,
    DTC_SIM_CARD_FAULT_INDEX,
    DTC_NETWORK_MODULE_FAILURE_INDEX,
    DTC_GNSS_ANT_OPEN_INDEX,
    DTC_GNSS_ANT_SHORT_INDEX,
    DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX,
    DTC_EMMC_STORAGE_CHIP_FAULT_INDEX,
    DTC_EMMC_STORAGE_FULL_INDEX,
    DTC_NETWORK_MODULE_FLASH_FULL_INDEX,
    DTC_MAX_INDEX
} DtcIndex;
typedef enum
{
    DTC_STATUS_NO_FAULT = 0,
    DTC_STATUS_FAULT,
}DtcStatus;


typedef enum
{
    NORMAL_STATUS,    //正常工作状态
    STANDBY_STATUS,   //待机状态，短信、G-sensor等唤醒
    SLEEP_STATUS,     //睡眠模式，ARM实际上已关机
} SysPowerStatus_en;



typedef enum
{
    CAN_1,
    CAN_2,
    CAN_3,
    CAN_4,
    CAN_5,
    CAN_MAX,
} CanChannel_en;





typedef enum
{
    /*Devices Status Read fail*/
    DEVICES_UNAVILABLE = 0x00,
    /*Devices Status Read success*/
    DEVICES_AVILABLE,
}DevicesAvilableStatus;
    
//tbox升级状态枚举
typedef enum
{
    UPGRADE_STATUS_IDLE = 0,        //线程空转状态
    UPGRADE_STATUS_START,           //开始升级
    UPGRADE_STATUS_BT,              //bt正在升级
    UPGRADE_STATUS_BT_FINISH,       //bt升级完成
    UPGRADE_STATUS_MCU,             //mcu正在升级
    UPGRADE_STATUS_MCU_ROLLBACK,    //mcu正在回退版本
    UPGRADE_STATUS_MCU_FINISH,      //mcu升级完成
    UPGRADE_STATUS_ARM,             //arm正在升级
    UPGRADE_STATUS_ARM_FINISH,      //arm升级完成
    UPGRADE_STATUS_ARM_SCRIPT,             //arm脚本正在升级
    UPGRADE_STATUS_ARM_SCRIPT_FINISH,      //arm脚本升级完成
    UPGRADE_STATUS_WAITING,         //等待状态mcu重启arm
    UPGRADE_STATUS_RB_WAITING,      //等待状态mcu重启arm
    UPGRADE_STATUS_ALL_FINISH,      //所有元素升级完成
    UPGRADE_STATUS_MCU_DEBUG,       //MCU升级调试入口
} UpgradeStatus;
    
//tbox升级结果枚举
typedef enum
{
    UPGRADE_SUCCESS = 0,
    UPGRADE_BT_FAILED,
    UPGRADE_MCU_FAILED,
    UPGRADE_MCU_ROLLBACK_FAILED,
    UPGRADE_ARM_FAILED,
    UPGRADE_ARM_SCRIPT_FAILED,
    UPGRADE_READ_CONFIG_FILE_ERROR,  //读取配置文件错误
    UPGRADE_READ_STATUS_FILE_ERROR,  //读取状态文件错误
    UPGRADE_CHECK_FILE_ERROR,        //升级文件校验错误
} UpgradeResult;


typedef struct {

    uint8_t tboxId[TBOX_ID + 1];                              // 1612 TBOX 内部SN 五菱不使用

    uint8_t tboxSparePartNumber[TBOX_SPARE_PARTNUMBERMAX + 1]; // F187  客户零件号
    uint8_t vinCode[TBOX_VIN_NUM + 1];                        // F190  vin码
    uint8_t softUpdateDate[TBOX_SOFTWARE_UPDATE_DATE_LEN + 1]; // F199  软件更新日期7位BCD
    uint8_t vehicleOfflineConfig[VEHICLE_OFFLINE_CONFIG_LEN + 1]; // F110 车辆下线配置

    uint8_t tuid[TUID_LEN + 1];                               // 1600  TUID，T-Box SN(TUID 后20字节)，32位ASCII
    uint8_t tboxAuthStatus;                               // 1601 TBOX登签认证状态
    uint8_t simNumber[TBOX_SIM_LEN + 1];                   // 1602 SIM卡号
    uint8_t iccid[TBOX_ICCID_LEN + 1];                        // 1603 ICCID号
    uint8_t imei[TBOX_IMEI_LEN + 1];                          // 1604 imei

    uint32_t wakeupTimeInterval;                          // 1615 定时唤醒间隔，默认4h

    uint32_t AutoRechargeUnderVoltage;                    // 1618 智能补电欠压阈值
    uint8_t link1SSLEnable;                               // 1619 第一链路SSL使能
    uint8_t link2SSLEnable;                               // 1620 第二链路SSL使能
    uint8_t OfflineCheckFlag;                             // 1621 是否触发下线检测
    uint32_t acTimeoutMinutes;                            // 1622 空调运行超时关闭时间 (单位为分钟)
    uint8_t timedWakeupEnable;                            // 1623 定时唤醒使能
    uint8_t networkWakeupEnable;                          // 1624 网络唤醒使能
    uint8_t globalLogEnable;                              // 1625 全局日志输出使能
    uint8_t link1Enable;                                  // 1626 第一链路使能
    uint8_t link2Enable;                                  // 1627 第二链路使能
    uint8_t link3Enable;                                  // 1628 第三链路使能
    uint8_t thirdPartyLinkEnable;                         // 1629 第三方平台链路使能
    uint8_t certUpdateTime[CERT_UPDATE_TIME + 1];             // 1630 证书更新时间
    uint16_t dataResendTestTime;                          // 1631 数据补发测试时间
    uint16_t level3AlarmTestTime;                         // 1632 3级报警测试时间
    // uint8_t vehicleRestrictFlag;                          // 1633 限速或锁车标志
    uint8_t lockCarStatus;                                // 1636 锁车状态 (00:无命令, 01:进入锁车, 10:退出锁车)
    uint8_t speedLimitStatus;                             // 1637 限速状态 (00:无命令, 01:进入限速, 10:退出限速)
    uint8_t speedLimitValue;                              // 新增：限速值 0~255
} CarInfo;

// StaticDidInfo
typedef struct {
    uint8_t tboxCertificateID[TBOX_CERT_LEN + 1];             // 1605 TBOX 数字证书编号
    uint8_t link1Addr[TBOX_LINK_LEN + 1];                     // 1606 TBOX 第1链路地址
    uint8_t link1Port[TBOX_LINK_PORT_LEN + 1];                // 1607 TBOX 第1链路地址端口
    uint8_t link2Addr[TBOX_LINK_LEN + 1];                     // 1608 TBOX 第2链路地址
    uint8_t link2Port[TBOX_LINK_PORT_LEN + 1];                // 1609 TBOX 第2链路地址端口
    uint8_t link3Addr[TBOX_LINK_LEN + 1];                     // 1610 TBOX 第3链路地址
    uint8_t link3Port[TBOX_LINK_PORT_LEN + 1];                // 1611 TBOX 第3链路单项向认证端口
    uint8_t link3BiAuthPort[TBOX_LINK_PORT_LEN + 1];          // 1634 TBOX 第3链路双向认证端口
} StaticDidInfo;
typedef struct {
    uint8_t thirdPartyLinkAddr[TBOX_LINK_LEN + 1];            // 1613 第三方平台链路地址
    uint8_t thirdPartyLinkPort[TBOX_LINK_PORT_LEN + 1];       // 1614 第三方平台链路地址
    uint8_t link3Username[TBOX_LINK_USERNAME_LEN + 1];    // 1616 TBOX 第3链路登录名
    uint8_t link3Password[TBOX_LINK_PASSWORD_LEN + 1];    // 1617 TBOX 第3链路登录密码
} StaticDid2Info;

typedef struct {
    uint8_t tboxSoftwareId[TBOX_SOFTWARE_ID_LEN + 1];
    uint8_t tboxHardwareId[TBOX_HARDWARE_ID_LEN + 1];
    uint8_t tboxSystemSuppierIdentifier[TBOX_SYSTEM_SUPPIER_IDENTIFIER_LEN + 1];
    uint8_t tboxName[TBOX_NAME_LEN + 1];
    uint8_t tboxSoftVersion[TBOX_SOFT_VERSION_LEN + 1];
    uint8_t tboxHardVersion[TBOX_HARD_VERSION_LEN + 1];

    CarInfo carInfo;
    StaticDidInfo staticDidInfo;
    StaticDid2Info staticDid2Info;
}TotalDidInfo;

typedef struct {
    uint8_t ecuBatteryVoltage;       // 电池电压 (ECU供电电压), 1字节
    uint16_t vehicleSpeed;           // 车速, 2字节
    uint32_t odometer;               // 里程, 4字节
    uint8_t lowPowerMode;            // 低压电源模式, 1字节
    uint8_t dateTime[6];             // 日期和时间, 6字节
    uint8_t vehiclePowerMode;        // 整车动力模式, 1字节
    uint8_t gearPosition;            // 档位, 1字节
    uint8_t cellularSignalStrength;  // 移动网络信号强度, 1字节
    uint8_t gnssSignalStrength;      // GNSS信号强度, 1字节
    uint8_t wifiSignalStrength;      // WIFI信号强度, 1字节
    uint8_t link1TcpStatus;          // 第1链路TCP连接状态, 1字节
    uint8_t link2TcpStatus;          // 第2链路TCP连接状态, 1字节
    uint8_t link3TcpStatus;          // 第3链路TCP连接状态, 1字节
    uint8_t powerManagementMode;     // 电源管理工作模式, 1字节
    uint8_t mileageClearCount;       // 里程清零计数
    uint32_t tboxSaveMileageValues;  // TBOX保存里程值

    uint8_t rdsSoftwareIn[RDS_SOFTWARE_IN_LEN]; //OTAmaster、远程诊断软件版本号内部
    uint8_t rdsSoftwareOut[RDS_SOFTWARE_OUT_LEN]; //OTAmaster、远程诊断软件版本号外部
    uint8_t rdsSoftwareSupplierIdentifier[RDS_SOFTWARE_SUPPLIER_IDENTIFIER_LEN];
} DynamicDidInfo;

typedef struct
{
    int8_t   zoneTime;
    uint8_t  year;
    uint8_t  month;
    uint8_t  day;
    uint8_t  week;
    uint8_t  hour;
    uint8_t  minute;
    uint8_t  second;
} TimeInfo;

typedef struct rtcAlarmTime
{
    uint8_t second;
    uint8_t minute;
    uint8_t hour;
    uint8_t day;
    uint8_t week;
} RtcAlarmTime;

/*can period config*/
typedef struct canPeriodInfo
{
    uint8_t  channel;
    uint16_t period;
    uint32_t canId;
    uint8_t  rollingCountStartBit;
    uint8_t  checkSumByte;
    uint8_t  rollingCountMinValue;
    uint8_t  rollingCountMaxValue;
    uint8_t  rollingCountStepValue;
    uint8_t  data[CAN_FRAME_DATA_COUNT];
} CanPeriodInfo;

typedef struct canPeriodConfigInfo
{
    uint8_t        total;
    CanPeriodInfo  para[CAN_PERIOD_MSG_MAX_COUNT];
} CanPeriodConfigInfo;

typedef enum
{
    TSP_STATUS_RESISTER_UNKNOWN = 0, //未知状态
    TSP_STATUS_UNREGISTERED,         //未注册
    TSP_STATUS_REGISTERED,           //已注册
    TSP_STATUS_UNLOGINED,            //未登录
    TSP_STATUS_LOGINED,              //已登录
}ArmTspStatus;

typedef struct armDeviceStatus
{
    NadWorkStatus   armStatus;         /* ARM工作状态*/
    ArmWorkMode     armWorkMode;       /*arm工作模式，装备模式和正常业务模式*/
    ArmTspStatus    tspConnectStatus;  /* TSP注册/登录状态,需要将此状态同步到MCU用于外发报文*/
    uint8_t         wifiStatus;
    NWSigStrength   wifiSignal;        /* WIFI信号强度，0,1,2,3,4-无信号,,,,极强 */
    ModuleStatus    moduleStatus;
    uint8_t         moduleSignal;      /* 通讯模块信号强度 */
    uint8_t         simCardStatus;     /* SIM卡状态，0 - 识别SIM，1 - 未识别SIM */
    uint8_t         gnssStatus;        /* GPS模块状态  0 正常，1异常*/
    uint8_t         gpsStarCount;      /* GPS卫星个数 */
    uint8_t         gpsDirectionH;     /*GPS方向高位*/
    uint8_t         gpsDirectionL;     /*GPS方向低位*/
    uint8_t         gpsFixStatus;      /* GPS定位，0 - 无定位，1 - 已定位 */
    uint8_t         gnssSignal;        /* GNSS 信号强度值 */
    uint32_t        latitude;          /* 纬度 */
    uint32_t        longitude;         /* 经度 */
    uint32_t        speed;             /* 地面速度 */
    uint8_t         netStatus;         /* 网络状态0：未注册，1：注册失败，2：注册成功，3：拔号失败，
                                        4：拔号成功，5：连接中，6：连接失败，7：连接成功，8：登入失败，9：登入成功， */
    uint8_t         canReportMode;    /* 数据上传模式：0：5s模式，1：30s模式 */
    uint8_t         emmcStatus;       /* EMMC状态，0 - 正常，1 - 异常 */
    uint8_t         ethStatus;        /* 以太网状态，0 - 未连接，1 - 连接 */
    uint8_t         vehicleFault;    /* 0:无故障，1：有故障， */
    uint8_t         extendedDataFlag;    /* 0:常规数据，1：扩展数据 */
} ArmDeviceStatus;

typedef struct versionManageInfo
{
    uint16_t mcuHrVersion;
    uint16_t mcuBootVersion;
    uint32_t mcuSoftVersion;
} VersionManageInfo;

typedef struct systemStatus
{
    VersionManageInfo versionInfo;
    HandShakeStatus handShakeStatus;
    SysPowerStatus_en systemWorkStatus;
    McuWorkMode mcuWorkMode;
    WorkStatus accStatus;
} SystemStatus_s;

typedef struct {
    char domin[50];
    char *id;
} NtpServerInfo_s;

typedef struct timeConfigInfo
{
    uint8_t zoneTime;
    NtpServerInfo_s ntpServer[3]; //NTP服务器IP地址或域名
} TimeConfigInfo;

//定时器时间映射
typedef struct
{
    char taskName[TIMER_TASK_NAME_MAX + 1];
    TimeInfo dateTime;
    uint8_t paraData[TIMER_TASK_PARA_MAX];
    uint32_t paraLen;
}TimerInfo_s;

typedef enum
{
    NETWORK_STATUS_UNKNOW,
    NETWORK_STATUS_READY,
    NETWORK_STATUS_UNREADY
}NetWorkReady_en;

typedef enum {
    D1 = 1,  // 正常工作
    D2,  // 整车上电
    D3,  // 有业务请求
    D4,  // 被唤醒
    D5,  // 不工作
} TBoxState_en;
//各链路连接状态
typedef enum
{
    LINK_STATE_DISCONNECTED    = 0x00,
    LINK_STATE_CONNECT_ERROR   = 0x01,
    LINK_STATE_CONNECT_SUCCESS = 0x04,
} LinkConnectState_en;
typedef enum
{
    LINK1_UPGRADE_NO_TASK                     = 0x00, //无升级任务
    LINK1_UPGRADE_SUCCESS                     = 0x01, // 升级成功
    LINK1_PREPARE_DOWNLOAD                    = 0x02, // 升级前准备，下载文件
    LINK1_DOWNLOAD_COMPLETE_WAITING_FOR_FLASH = 0x03, // 下载升级文件完成，等待刷写
    LINK1_DOWNLOAD_FAILED                     = 0x04, // 下载升级文件失败
    LINK1_START_FLASH                         = 0x05, // 启动升级刷写
    LINK1_FLASHING                            = 0x06, // 正在升级，刷写中
    LINK1_FLASH_FAILED_ROLLBACK               = 0x07, // 升级刷写失败，回滚中
    LINK1_ROLLBACK_SUCCESS                    = 0x08, // 回滚成功
    LINK1_ROLLBACK_FAILED                     = 0x09, // 回滚失败
    LINK1_SECURE_ACCESS_FAILED                = 0x0A, // 安全访问失败
    LINK1_CONTROLLER_NEGATIVE_RESPONSE        = 0x0B, // 控制器否定响应
    LINK1_CONTROLLER_NO_RESPONSE_TIMEOUT      = 0x0C, // 控制器无应答（超时）
    LINK1_UPGRADE_CONDITION_SPEED_NOT_ZERO    = 0x0D, // 升级条件不满足 — 车速不为0
    LINK1_UPGRADE_CONDITION_VOLTAGE_RANGE    = 0x0E, // 升级条件不满足 — 电压低于12V或不在9-16V区间内
    LINK1_UPGRADE_CONDITION_GEAR_NOT_PARK    = 0x0F, // 升级条件不满足 — 档位未挂P档
    LINK1_UPGRADE_CONDITION_VEHICLE_CHARGING = 0x10, // 升级条件不满足 — 车辆充电中
    LINK1_UPGRADE_CONDITION_HIGH_VOLTAGE     = 0x11, // 升级条件不满足 — 高压状态中
    LINK1_UPGRADE_CONDITION_PARKING_BRAKE    = 0x12, // 升级条件不满足 — 手刹未拉起
    LINK1_FLASH_ERROR_INVALID_PARAMETER      = 0x13, // 刷写错误 — 无效的参数
    LINK1_FLASH_ERROR_UDS_TASK_ADD_FAILED    = 0x14, // 刷写错误 — UDS任务添加失败
    LINK1_FLASH_ERROR_UDS_PARSING            = 0x15, // 刷写错误 — UDS解析错误
    LINK1_FLASH_ERROR_UDS_SUBTASK_FAILED     = 0x16, // 刷写错误 — UDS子任务失败
    LINK1_FLASH_ERROR_UNDEFINED_SEND_FUNCTION = 0x17, // 刷写错误 — 未定义发送函数
    LINK1_FLASH_ERROR_BUFFER_OVERFLOW         = 0x18, // 刷写错误 — 缓冲区溢出
    LINK1_FLASH_ERROR_OTA_BLOCK_ERROR         = 0x19, // 刷写错误 — OTA刷写块出错
    LINK1_FLASH_ERROR_UDS_REQUEST_FAILED      = 0x1A, // 刷写错误 — UDS请求失败
    LINK1_FLASH_ERROR_MEMORY_ERROR            = 0x1B, // 刷写错误 — 内存出错
    LINK1_FLASH_ERROR_UDS_TIMEOUT             = 0x1C, // 刷写错误 — UDS超时
    LINK1_FLASH_ERROR_MEMORY_CHECK_FAILED     = 0x1D, // 刷写错误 — 内存校验错误
    LINK1_FLASH_ERROR_SERVICE_3E_LIMIT        = 0x1E, // 刷写错误 — 3E服务发送次数超过限制
    LINK1_FLASH_ERROR_INVALID_KEY             = 0x1F, // 刷写错误 — 无效key值
    LINK1_FLASH_ERROR_FILE_OPEN               = 0x20, // 刷写错误 — 文件打开错误
    LINK1_FLASH_ERROR_UDS_SM_CREATION_FAILED  = 0x21, // 刷写错误 — UDS和状态机创建失败
    LINK1_FLASH_ERROR_GENERAL_PROGRAMMING     = 0x22, // 刷写错误 — 一般性编程错误
    LINK1_FLASH_ERROR_UDS_SEND_EXCEPTION      = 0x23  // 刷写错误 — UDS报文外发异常|无法外发UDS报文
} Link1UpgradeStatus;

#endif


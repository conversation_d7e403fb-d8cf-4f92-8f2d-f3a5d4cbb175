/*
      tbox_paraconfig.h
描述：此文件主要是提供一些配置信息的宏定义
                  如关键字，默认值等
作者：zhengyong
时间：2019.12.12
*/


#ifndef  __TBOX_PARACONFIG_H__
#define  __TBOX_PARACONFIG_H__

#include "tbox_macro.h"
/***************TBOX 程序环境*********************/
#define TBOX_ENV_DEV              0
#define TBOX_ENV_UAT              1
#define TBOX_ENV_PRD              2
#define TBOX_ENV                  TBOX_ENV_DEV

/***************配置文件保存路径*********************/
#define FTM_TEST_CONFIG_FILE_PATH TBOX_CONFIG_RW_PATH "ftm.ini" //derek add for ftm
#define FTM_DAMEON_FILE_PATH      TBOX_CONFIG_RW_PATH "ftmdaemon_file.ini"
#define EXTEND_CAN_CACHE_DATA     TBOX_DATA_CACHE_PATH "extendcancache.dat"
#define FTM_PCB_KEY               "rw.ftm.pcbasn"

/***************Server端配置信息默认值*********************/

#define  LOG_LEVEL_KEY                           "rw.logconfig.loglevel"
#define  LOG_UART_SWITCH_KEY                     "rw.logconfig.loguartswitch"
#define  LOG_MODULE_TYPE_KEY                     "rw.logconfig.logmoduletype"
#define  LOG_ENCRYPT_TYPE_KEY                    "rw.logconfig.logencrypttype"
#define  LOG_ENCRYPT_CODE_KEY                    "rw.logconfig.logencryptcode"
#define  CAN_LOAD_LIB_NAME_KEY                   "mm.canlib.name"
#define  CAN_LOAD_LIB_COUNT_KEY                  "mm.canlib.count"   
#define  TBOX_SAVE_FLIE_TOTAL_KEY                "rw.logconfig.savefiletotal"
#define  TBOX_ARM_LOG_SIZE_KEY                   "rw.logconfig.armlogsize"




/***************Server端配置信息默认值*********************/
#define  LOG_LEVEL_DEFAULT_VAL                   1
#define  LOG_UART_SWITCH_DEFAULT_VAL             SWITCH_ON
#define  LOG_MODULE_TYPE_DEFAULT_VAL             255
#define  LOG_ENCRYPT_TYPE_DEFAULT_VAL            0
#define  LOG_ENCRYPT_CODE_DEFAULT_VAL            0xA5

//todo 原客户端错误码从-1010001开始 可能需要优化
//远程控制的错误码
#define RMT_ERROR_NET_TIME_OUT            -1010001
#define RMT_ERROR_LOW_POWER               -1010002
#define RMT_ERROR_NOT_PARKING_POS         -1010003
#define RMT_ERROR_PEPS_AUTH_FAIL          -1010004
#define RMT_ERROR_NOT_REMOTE_CTRL_MODE    -1010005
#define RMT_ERROR_VIN_REPEAT              -1010006
#define RMT_ERROR_INVALID_CMD             -1010007
#define RMT_ERROR_QUEUE_EMPTY             -1010008
#define RMT_ERROR_PENDING                 -1010009
#define RMT_ERROR_RESULT_CHECK            -1010010
#define RMT_ERROR_PEPS_TBOX_BCM_FAILD     -1010011     /* TBOX与BCM防盗认证失败*/
#define RMT_ERROR_PEPS_TBOX_VBU_FAILD     -1010012     /* TBOX与VBU防盗认证失败*/
#define RMT_ERROR_PEPS_BCM_VBU_FAILD      -1010013     /* BCM与VBU防盗认证失败*/
#define RMT_ERROR_DOOR_NOT_ALL_CLOSET     -1010014     /* 车门未全关*/
#define RMT_ERROR_DOOR_NOT_LOCK           -1010015     /* 车辆未上锁*/
#define RMT_ERROR_SPEED_NOT_ZERO          -1010016     /* 车速不为0*/
#define RMT_ERROR_CMD_TIME_OUT            -1010017     /* 指令执行超时*/
#define RMT_ERROR_LV_START_FAILD          -1010018     /* 低压上电失败（可用于空调控制）*/
#define RMT_ERROR_HV_START_FAILD          -1010019     /* 高压上电失败（可用于空调控制）*/
#define RMT_ERROR_OTA_UPDATING            -1010020     /* FOTA升级中 */
#define RMT_ERROR_PARAM_ERROR             -1010021     /* 参数错误（控制指令无效时） */
#define RMT_ERROR_ALREADY_ON              -1010022     /* 车辆已通电，无法执行此操作 */
#define RMT_ERROR_RMT_CMD_EXECUTING       -1010023     /* 远控指令执行中 */
#define RMT_ERROR_BATT_ERROR              -1010024     /* 蓄电池电压异常 */
#define RMT_ERROR_KEY_IN_CAR              -1010025     /* 有钥匙在车内 */
#define RMT_ERROR_FAN_SPEED_SET_FAILD     -1010026     /* 风量设定失败 */
#define RMT_ERROR_TEMP_SET_FAILD          -1010027     /* 稳定设定失败 */
#define RMT_ERROR_PEPS_FAIL_REASON        -1010028     /* peps fail reason */
#define RMT_ERROR_ENGINE_OVER_TIMES       -1010029     /* 远程上电超过次数 */
#define RMT_ERROR_ATWS_STATUS_NOT_ARMED   -1010030     /* 车辆未设防 */


//Buffer event错误码-1020000
#define BE_ERROR_CREATE_CTX_FAIL      -1020001
#define BE_ERROR_VERIFY_CTX_FAIL      -1020002
#define BE_ERROR_CONFIG_NOT_SET       -1020003
#define BE_ERROR_CREATE_CONNECT_FAIL  -1020004
#define BE_ERROR_NET_WORK_DISCONNECT  -1020005
#define BE_ERROR_WRITE_DATA_FAIL      -1020006
#define BE_ERROR_READ_DATA_FAIL       -1020007
#define BE_ERROR_REMOVE_DATA_FAIL     -1020008
#define BE_ERROR_NO_TCP_CONNECT       -1020009
#define BE_ERROR_CREATE_THREAD_FAIL   -1020010
#define BE_ERROR_EXIT_CONNECT_FAIL    -1020011
#define BE_ERROR_CREATE_SSL_FAIL      -1020012
#define BE_ERROR_TLS_VER_NOTSUPP      -1020013
#define BE_ERROR_GET_ADDR_FAIL        -1020014
#define BE_ERROR_RECONN_THREAD_EXIST  -1020015

//TSP client错误码-1030000
#define TSP_ERROR_CREATE_TCP_FAIL     -1030001
#define TSP_ERROR_CREATE_TRHEAD_FAIL  -1030002
#define TSP_ERROR_FRAME_DATA_LESS     -1030003
#define TSP_ERROR_TIMESTAMP_LONGERR   -1030004
#define TSP_ERROR_TRANS_ID_LONGERR    -1030005
#define TSP_ERROR_IMSI_LONGERR        -1030006
#define TSP_ERROR_FUNC_CODE_LONGERR   -1030007
#define TSP_ERROR_VERION_LONGERR      -1030008
#define TSP_ERROR_MSG_LENGTH_LONGERR  -1030009
#define TSP_ERROR_CRC_DATA_INVALID    -1030010
#define TSP_ERROR_DATA_CRC_FAIL       -1030011
#define TSP_ERROR_MSG_DATA_INVALID    -1030012
#define TSP_ERROR_ADD_MSG_DATA_FAIL   -1030013
#define TSP_ERROR_MSG_KEY_TOO_LONG    -1030014
#define TSP_ERROR_COMMAND_NOT_SUPPORT -1030015
#define TSP_ERROR_IMSI_NOT_MATCH      -1030016
#define TSP_ERROR_TIME_DIFF_TOOLONG   -1030017
#define TSP_ERROR_FRAME_DATA_INVALID  -1030018
#define TSP_ERROR_GET_TIME_FAILED     -1030019
#define TSP_ERROR_IMSI_NOT_READY      -1030020
#define TSP_ERROR_CMD_TIMED_OUT       -1030021
#define TSP_ERROR_NOT_LOGIN           -1030022
#define TSP_ERROR_PARA_INVALID        -1030023


//数据采集错误码-1040000
#define STATUS_ERROR_DATA_NOT_READY   -1040001
#define STATUS_ERROR_BUF_NOT_ENOUGH   -1040002

//FOTA错误码
#define FOTA_ERROR_NOT_DOWNLOAD       -1050001
#define FOTA_ERROR_NO_TASK            -1050002
#define FOTA_UPGRADE_FINISH           -1050003
#define FOTA_REVERT_FINISH            -1050004

#define FOTA_REVERT_SUCCESS           -1050005
#define FOTA_UPGRADE_ECU_FAIL         -1050006
#define FOTA_ERROR_PARSE_FAIL         -1050007
#define FOTA_ERROR_CRC_FAIL           -1050008
#define FOTA_FILE_EOF                 -1050009
#define FOTA_ERROR_READ_FAIL          -1050010
#define FOTA_UPGRADE_DCS              -1050011
#define FOTA_ERROR_NOT_SUPPORT        -1050012
#define FOTA_ERROR_UPGRADING          -1050013
#define FOTA_RETRY_ENOUGH             -1050014
#define FOTA_UPG_ECU_SUCCESS          -1050015
#define FOTA_ALREADY_START_DL         -1050016
#define FOTA_NOT_PAUSE_DL             -1050017
#define FOTA_DOWNLOAD_DCS             -1050018
#define FOTA_ECU_DOWNLOAD_SUCCESS     -1050019
#define FOTA_UPGRADE_ARM              -1050020
#define FOTA_UPGRADE_ARM_FAIL         -1050021
#define FOTA_UPG_ARM_SUCCESS          -1050022
#define FOTA_DOWNLOAD_ARM             -1050023
#define FOTA_UPGRADE_DCS_FAIL         -1050024

/***********************************以上可能不用***********************************************/

//电话号码
#define  PHONE_NUMBER_MAX_LEN                18
#define  I_CALL_PHONE_NUM_KEY                "rw.xcall.icallnum"
#define  B_CALL_PHONE_NUM_KEY                "rw.xcall.bcallnum"
#define  E_CALL_PHONE_NUM_KEY                "rw.xcall.ecallnum"
#define  E_CALL_BACK_NUM_KEY                 "rw.xcall.ecallbacknum"
#define  SMS_CENTER_PHONE_NUM_KEY            "rw.xcall.smscenternum"
#define  SYS_DELAY_SLEEP_TIME_KEY            "rw.sys.delaysleeptime"

//IPC相关属性
#define  IPC_RX_PRINTF_KEY                   "rw.ipc.rxprintf"
#define  IPC_TX_PRINTF_KEY                   "rw.ipc.txprintf"

#define  IPC_RX_PRINTF_MIN_VAL               SWITCH_OFF
#define  IPC_RX_PRINTF_MAX_VAL               SWITCH_ON

#define  IPC_TX_PRINTF_MIN_VAL               SWITCH_OFF
#define  IPC_TX_PRINTF_MAX_VAL               SWITCH_ON

#define  IPC_RX_PRINTF_DFT_VAL               SWITCH_OFF
#define  IPC_TX_PRINTF_DFT_VAL               SWITCH_OFF


//GPS相关属性
#define GPS_CONFIG_TYPE_KEY                  "rw.gps.type"
#define GPS_NMEA_LOG_FLAG                    "rw.gps.nmeaprintf"

#define DBUS_LOG_PRINTF_FLAG                 "rw.dbus.logprintf"

#define GPS_CONFIG_TYPE_MIN_VAL              0 //GPS_MODULE_UBLOX
#define GPS_CONFIG_TYPE_MAX_VAL              2 //GPS_MODULE_RIA

#define GPS_CONFIG_TYPE_DFT_VAL              1 //TODO 默认GPS_MODULE_INNER，后续考虑根据项目确认


//系统控制-TIME相关属性
#define  TIME_ZONE_TIME_KEY                  "ro.sysctrl.timezone"
#define  TIME_NTP_SERVER_KEY1                "ro.sysctrl.ntpserver1"
#define  TIME_NTP_SERVER_KEY2                "ro.sysctrl.ntpserver2"
#define  TIME_NTP_SERVER_KEY3                "ro.sysctrl.ntpserver3"

#define  TIME_ZONE_MIN_VAL                   (-12)
#define  TIME_ZONE_MAX_VAL                   14

#define TIME_ZONE_DFT_VAL        8
#define TIME_NTP_SERVER_ID_1     "TIME_NTP_SERVER_ID_1"
#define TIME_NTP_SERVER_DFT_VAL1 "pool.ntp.org"
#define TIME_NTP_SERVER_ID_2     "TIME_NTP_SERVER_ID_2"
#define TIME_NTP_SERVER_DFT_VAL2 "ntp.aliyun.com" //阿里云
#define TIME_NTP_SERVER_ID_3     "TIME_NTP_SERVER_ID_3"
#define TIME_NTP_SERVER_DFT_VAL3 "ntp.ntsc.ac.cn" //中国国家授时中心

#define TIME_ZONE_KEY                 "mm.time.zone"
#define DEFAULT_LOCAL_TIME_ZONE       (8)

//系统控制-can参数
#define  CAN_PERIOD_MSG_COUNT_KEY            "ro.sysctrl.messageTotal"
//如下一个key带有格式化标识符，使用时需要留意
#define  CAN_PERIOD_MSG_PARA_KEY             "ro.sysctrl.message_para_%d"

#define  CAN_PERIOD_MSG_MIN_COUNT            0
#define  CAN_PERIOD_MSG_MAX_COUNT            20

#define  CAN_PERIOD_MSG_COUNT_DFT_VAL        0


#define  CAR_ACC_STATUS_KEY                  "mm.car.accStatus"    //MCU硬线获取
#define  CAR_CAN_NET_STA_KEY                 "mm.car.canNetState"  //mcuCan网络状态 1：活跃  0：未活跃
#define  CAR_BPLUSLOW_KEY                    "mm.car.bplusLow"

//静态信息数据，更新需要重启系统 mcu传递过来
#define STATIC_DID_SYNC_FLAG                     "mm.static.syncFlag"         //表示上电后是否同步过
#define TBOX_ID_KEY                              "mm.static.tboxId"           //1612 TBOX 内部SN
#define CAR_SUPPLIER_CODE_KEY                    "mm.static.supplierCode"     //F18A 供应商代码
#define TBOX_SPAREPART_KEY                       "mm.static.sparePart"        //F187 零件号
#define CAR_VIN_CODE_KEY                         "mm.static.vinCode"          //F190 VIN码
#define TBOX_SOFTWARE_UPDATE_DATE_KEY            "mm.static.softwareDate"     //F199 软件更新日期
#define VEHICLE_OFFLINE_CONFIG_KEY               "mm.static.offlineConfig"    //F110 车辆下线配置
#define TUID_KEY                                 "mm.static.tuid"             //1600 TUID

#define TBOX_AUTH_STATUS_KEY                     "mm.static.authStatus"       //1601 TBOX 登签认证状态
#define TBOX_SIM_KEY                             "mm.static.sim"              //1602 SIM卡号
#define TBOX_ICCID_KEY                           "mm.static.iccid"            //1603 ICCID号
#define TBOX_IMEI_KEY                            "mm.static.imei"             //1604 IMEI号
#define WAKEUP_INTERVAL_KEY                      "mm.static.wakeupInterval"   //1615 定时唤醒间隔
#define AUTO_RECHARGE_UNDER_VOLTAGE_KEY          "mm.static.rechargeVoltage"  //1618 智能补电欠压阈值 (单位mV)
#define LINK1_SSL_ENABLE_KEY                     "mm.static.link1Ssl"         //1619 第一链路SSL使能
#define LINK2_SSL_ENABLE_KEY                     "mm.static.link2Ssl"         //1620 第二链路SSL使能
#define OFFLINE_CHECK_FLAG_KEY                   "mm.static.offlineCheck"     //1621 是否触发下线检测
#define AC_TIMEOUT_KEY                           "mm.static.acTimeout"        //1622 空调运行超时关闭时间 (单位秒)
#define TIMED_WAKEUP_ENABLE_KEY                  "mm.static.timedWakeup"      //1623 定时唤醒使能
#define NETWORK_WAKEUP_ENABLE_KEY                "mm.static.networkWakeup"    //1624 网络唤醒使能
#define GLOBAL_LOG_ENABLE_KEY                    "mm.static.globalLog"        //1625 全局日志输出使能
#define LINK1_ENABLE_KEY                         "mm.static.link1Enable"      //1626 第一链路使能
#define LINK2_ENABLE_KEY                         "mm.static.link2Enable"      //1627 第二链路使能
#define LINK3_ENABLE_KEY                         "mm.static.link3Enable"      //1628 第三链路使能
#define THIRD_PARTY_LINK_ENABLE_KEY              "mm.static.thirdLinkEnable"  //1629 第三方平台链路使能
#define CERT_UPDATE_TIME_KEY                     "mm.static.certTime"         //1630 证书更新时间
#define DATA_RESEND_TEST_TIME_KEY                "mm.static.resendTime"       //1631 数据补发测试时间 单位s
#define LEVEL3_ALARM_TEST_TIME_KEY               "mm.static.alarmTestTime"    //1632 3级报警测试时间
// #define VEHICLE_RESTRICT_FLAG_KEY                "mm.static.restrictFlag"     //1633 限速或锁车标志
#define VEHICLE_SPEED_LIMIT_KEY                  "mm.static.speedLimit"       //1635 限速值 0~255
#define VEHICLE_LOCK_STATUS_KEY                  "mm.static.lockCarStatus"    //1636 锁车状态 (00:无命令, 01:进入锁车, 02:退出锁车)
#define VEHICLE_SPEED_LIMIT_STATUS_KEY           "mm.static.speedLimitStatus" //1637 限速状态 (00:无命令, 01:进入限速, 02:退出限速)

#define TBOX_CERTIFICATE_ID_KEY                  "mm.static.certId"           //1605 TBOX 数字证书编号
#define LINK1_ADDR_KEY                           "mm.static.link1Addr"        //1606 第1链路地址
#define LINK1_PORT_KEY                           "mm.static.link1Port"        //1607 第1链路端口
#define LINK2_ADDR_KEY                           "mm.static.link2Addr"        //1608 第2链路地址
#define LINK2_PORT_KEY                           "mm.static.link2Port"        //1609 第2链路端口
#define LINK3_ADDR_KEY                           "mm.static.link3Addr"        //1610 第3链路地址
#define LINK3_PORT_KEY                           "mm.static.link3Port"        //1611 第3链路单项向认证端口
#define LINK3_BI_AUTH_PORT_KEY                   "mm.static.link3BiAuthPort"  //1634 第3链路双向认证端口

#define THIRD_PARTY_LINK_ADDR_KEY                "mm.static.thirdLinkAddr"    //1613 第三方平台链路地址
#define THIRD_PARTY_LINK_PORT_KEY                "mm.static.thirdLinkPort"    //1614 第三方平台链路端口
#define LINK3_USERNAME_KEY                       "mm.static.link3User"        //1616 第3链路登录名
#define LINK3_PASSWORD_KEY                       "mm.static.link3Pass"        //1617 第3链路登录密码

#define TBOX_SOFTWARE_ID_KEY                     "mm.static.softwareId"       //F188 TBOX 软件ID
#define TBOX_HARDWARE_ID_KEY                     "mm.static.hardwareId"       //F191 TBOX 硬件ID
#define TBOX_NAME_KEY                            "mm.static.tboxName"         //F197 TBOX 名称
#define TBOX_SOFT_VERSION_KEY                    "mm.static.softVersion"      //F190 TBOX 软件版本 （符合五菱的版本格式）
#define TBOX_HARD_VERSION_KEY                    "mm.static.hardVersion"      //F193 TBOX 硬件版本 （符合五菱的版本格式）与下面MCU版本有区别

//部分需要缓存的动态DID信息
#define VEHICLE_SPEED_KEY                        "mm.dynamic.vehicleSpeed"           // 车速
#define ODOMETER_KEY                             "mm.dynamic.odometer"               // 里程
#define LOW_POWER_MODE_KEY                       "mm.dynamic.lowPowerMode"           // 低压电源模式
#define VEHICLE_POWER_MODE_KEY                   "mm.dynamic.vehiclePowerMode"       // 整车动力模式
#define GEAR_POSITION_KEY                        "mm.dynamic.gearPosition"           // 档位
#define CELLULAR_SIGNAL_STRENGTH_KEY             "mm.dynamic.cellularSignal"         // 移动网络信号强度
#define GNSS_SIGNAL_STRENGTH_KEY                 "mm.dynamic.gnssSignalStrength"     // GNSS信号强度
#define WIFI_SIGNAL_STRENGTH_KEY                 "mm.dynamic.wifiSignalStrength"     // WIFI信号强度
#define LINK1_TCP_STATUS_KEY                     "mm.dynamic.link1TcpStatus"         // 第1链路TCP连接状态
#define LINK2_TCP_STATUS_KEY                     "mm.dynamic.link2TcpStatus"         // 第2链路TCP连接状态
#define LINK3_TCP_STATUS_KEY                     "mm.dynamic.link3TcpStatus"         // 第3链路TCP连接状态
#define POWER_MAG_MODE_KEY                       "mm.dynamic.powerMagMode"           // Tbox电源模式
#define MILEAGE_CLEAR_COUNT_KEY                  "rw.dynamic.mileageClearCount"      // 里程清零计数
#define SAVE_MILEAGE_VALUES_KEY                  "rw.dynamic.saveMileageValues"      // TBOX保存里程值

//DTC 状态
#define TBOX_DTC_STATUS_KEY                      "mm.dtc.allDtcStatus"         // dtc状态，存储为16进制字符串，读取后转换为uint8 字节数组访问

//OtaMaster百度进程相关属性
#define OTA_MASTER_VEHICLE_SPEED_KEY                "mm.otaMaster.vehicleSpeed"         // 车速
#define OTA_MASTER_HIGH_VOLT_SOC_KEY                "mm.otaMaster.highVoltSoc"          // 动力电池电量百分比(0-100)
#define OTA_MASTER_CHARGE_STATUS_KEY                "mm.otaMaster.changeStatus"         // 充电状态
#define OTA_MASTER_HIGH_VOLT_STATUS_KEY             "mm.otaMaster.highVoltStatus"       // 高压上电状态
#define OTA_MASTER_HANDBREAK_STATUS_KEY             "mm.otaMaster.handBreakStatus"      // 手刹状态
#define OTA_MASTER_ANTITHEFT_STATUS_KEY             "mm.otaMaster.antitheftStatus"      // 防盗状态
#define OTA_MASTER_DOOR_LOCK_STATUS_KEY             "mm.otaMaster.doorLockStatus"       // 主驾驶室门锁状态
#define OTA_MASTER_DIAG_UINT_STATUS_KEY             "mm.otaMaster.diagUnitStatus"       // 诊断仪状态状态
#define OTA_MASTER_VEHICLE_NETWORK_INFO_KEY         "rw.otaMaster.networkInfo"          // 唤醒整车网络信息
#define OTA_MASTER_OTA_MODE_INFO_KEY                "rw.otaMaster.otaModeInfo"          // 唤醒ota网络信息
#define OTA_MASTER_RECORD_INFO_KEY                  "rw.otaMaster.recordInfo"           // 录制信息
#define OTA_MASTER_RECORD_FOLDER_KEY                "rw.otaMaster.recordFolder"         // 录制目录信息
#define OTA_MASTER_RECORD_FILE_KEY                  "rw.otaMaster.recordFile"           // 录制文件信息

#define OTA_MASTER_RDS_SOFTWARE_IN                   "rw.otaMaster.softwareIn"          // 远程诊断内部版本号
#define OTA_MASTER_RDS_SUPPIER_IDENTIFIER            "rw.otaMaster.suppierId"           // 远程诊断供应商代号
#define OTA_MASTER_RDS_SOFTWARE_OUT                  "rw.otaMaster.softwareOut"         // 远程诊断软件版本号

#define OTA_MASTER_WEAKUP_STATUS                     "mm.static.weakupstatus"          // 模块被唤醒状态

//SIM相关属性，固定信息使用rw存储，更新需要重启系统
#define RIL_ICCID_INFO_KEY                   "rw.static.iccid"
#define RIL_SIMNUM_INFO_KEY                  "rw.static.simnum"
#define RIL_IMSI_INFO_KEY                    "rw.static.imsi"
#define RIL_IMEI_INFO_KEY                    "rw.static.imei"
#define RIL_SIM_STATUS_KEY                   "mm.static.simStatus"
//MCU版本信息
#define MCU_HW_VERSION_KEY                   "mm.static.mcuHWVers"
#define MCU_BOOT_VERSION_KEY                 "mm.static.mcuBootVers"
#define MCU_SW_VERSION_KEY                   "mm.static.mcuSWVers"
#define MCU_WORK_MODE_KEY                    "mm.status.mcuWorkMode"

#define IPC_HAND_SHAKE_KEY                   "mm.satus.ipcHandShake"

//升级状态
#define TBOX_UPGRADE_MODE_KEY                "rw.upgrade.modekey"
#define TBOX_UPGRADE_STATUS                  "rw.upgrade.status"
#define TBOX_UPGRADE_SUB_STA                 "rw.upgrade.subStatus"
#define TBOX_UPGRADE_RESULT                  "rw.upgrade.result"
#define TBOX_UPGRADE_MCU_FLAG                "rw.upgrade.mcuFlag"
#define TBOX_UPGRADE_DCS_FLAG                "rw.upgrade.dcsFlag"

#define TBOX_UPGRADE_ARM_STA                 "rw.upgrade.ArmUpgStatus"
#define TBOX_UPGRADE_SCRPIT_STA              "rw.upgrade.ScrpitUpgStatus"
#define TBOX_UPGRADE_MCU_STA                 "rw.upgrade.McuUpgStatus"
#define TBOX_UPGRADE_BT_STA                  "rw.upgrade.BtUpgStatus"

//定时任务
#define TIMER_TASK_NUM_KEY                   "rw.timer.taskNum"
#define TIMER_TASK_INFO_PRE_KEY              "rw.timer.taskInfo" //仅前缀，后面加数字

//客户端版本号，由客户端任意进程启动后写入
#define CLIENT_VERSION_KEY                   "rw.client.version"

//唤醒源
#define WAKEUP_SOURCE                        "mm.static.wakeupsource"

//KL30电压
#define SYS_KL30_VOLTAGE                    "mm.static.kl30Voltage"  //单位 mv

//快充电抢状态
#define SYS_FCHG_STA_KEY                 "mm.static.fchgStatus"
//慢充电抢状态
#define SYS_SCHG_STA_KEY                 "mm.static.schgStatus"

//备用电池电压
#define SYS_BK_BAT_VOLTAGE                  "mm.static.bkBatVoltage"

#define FIRST_LINK_SYNC_TIME_FINISHED       "mm.static.1thlinkSyncTime"   //国标 08 校时标志位

//当前网络状态
#define SYS_NETWORK_STA                     "mm.static.networkState"
#define SYS_NETWORK_RESET_STA               "rw.static.networkResetState"  //表示当前网络复位策略 0~2：重新拨号 3：开关飞行模式 4：重启模组 5：开关飞行模式

//CAN 接收状态
#define CAN_ACTIVE_KEY                  "mm.static.canActive"
//CAN 接收报文数量
#define CAN_RECEIVE_CNT_KEY                 "mm.static.canRecvCnt"

//第一链路终端升级相关
#define TERMINAL_UPGRADE_STATUS_KEY            "rw.static.terminalUpgradeStatus" //终端升级状态
#define TERMINAL_UPGRADE_ENABLE_START_TIME_KEY "rw.static.terUpgEnStartTime"     //终端升级使能开始时间
#define TERMINAL_UPGRADE_ENABLE_END_TIME_KEY   "rw.static.terUpgEnEndTime"       //终端升级使能结束时间
#define TERMINAL_UPGRADE_RTC_HOUR_KEY          "rw.static.terUpgRtcHour"         //终端升级RTC时间 小时
#define TERMINAL_UPGRADE_RTC_MIN_KEY           "rw.static.terUpgRtcMin"         //终端升级RTC时间 分钟
#define TERMINAL_UPGRADE_FTP_GET_INFO_KEY      "rw.static.terUpgFtpGetInfo"      //终端升级FTP获取信息
#define TERMINAL_UPGRADE_FTP_USER               "rw.static.terUpgFtpUser"         //终端升级FTP用户名
#define TERMINAL_UPGRADE_FTP_PASSWORD           "rw.static.terUpgFtpPassword"     //终端升级FTP密码
#define TERMINAL_UPGRADE_FTP_STORAGE_PATH_KEY "rw.static.terUpgFtpStoragePath"  //终端升级FTP存储路径
#define TERMINAL_UPGRADE_RTC_HOUR_DFT          3           //默认3.30
#define TERMINAL_UPGRADE_RTC_MIN_DFT           30          //默认3.30
#define TERMINAL_UPGRADE_ENABLE_START_TIME_DFT (3 * 60 * 60)                     //默认3点
#define TERMINAL_UPGRADE_ENABLE_END_TIME_DFT   (4 * 60 * 60)                     //默认3点
#define TERMINAL_UPGRADE_RTC_TASK_NAME         "terUpgRtcTimer"                  //终端升级RTC任务名

//第一链路相关属性
#if TBOX_ENV == 0
#define FIRST_LINK_SERVER_DOMIN                    "47.112.118.163"
#define FIRST_LINK_GB_SERVER_PORT                  51001 //不加密端口
#define FIRST_LINK_GB_SERVER_PORT_SSL              51005 //加密端口
#elif  TBOX_ENV == 1
#define FIRST_LINK_SERVER_DOMIN                    "182.90.255.107"
#define FIRST_LINK_GB_SERVER_PORT                  61001 //不加密端口
#define FIRST_LINK_GB_SERVER_PORT_SSL              61005 //加密端口
#else
//生成环境仅支持加密端口
#define FIRST_LINK_SERVER_DOMIN                    "182.90.255.107"
#define FIRST_LINK_GB_SERVER_PORT                  21005 //不加密端口
#define FIRST_LINK_GB_SERVER_PORT_SSL              21005 //加密端口
#endif
#define FIRST_LINK_GB_CACHE_FILE_NAME              "1thl_cache"
#define FIRST_LINK_PROP_FILE_NAME                  TBOX_CONFIG_RW_PATH  "rw.1thl" //属性文件名
#define FIRST_LINK_LOCAL_SOTRE_PERIOD_KEY          "rw.1thl.localstoreperiod"     // "localStorePeriod";
#define FIRST_LINK_NORMAL_REPORT_PERIOD_KEY        "rw.1thl.normalreportperiod"   //"norReportPeriod";
#define FIRST_LINK_ALARM_REPORT_PERIOD_KEY         "rw.1thl.alarmreportperiod"    //"alarmReportPeriod";
#define FIRST_LINK_REMOTE_SERVER_DOMAIN_KEY        "rw.1thl.remoteSDomain"        //"remoteServerDomain";
#define FIRST_LINK_REMOTE_SERVER_DOMAIN_PORT_KEY   "rw.1thl.remoteSDomainP"       //"remoteServerDomainPort"
#define FIRST_LINK_REMOTE_SERVER_DOMIN_CHANGE_FLAG "rw.1thl.remoteSDomainChg"     //"remoteServerDomainChangeFlag";
#define FIRST_LINK_HEART_BEAT_PERIOD_KEY           "rw.1thl.heartbeatPeriod"
#define FIRST_LINK_TBOX_ACK_TIMEOUT_KEY            "rw.1thl.tboxAckTimeout"
#define FIRST_LINK_SERVER_ACK_TIMEOUT_KEY          "rw.1thl.severAckTimeout"
#define FIRST_LINK_NEXT_LOGIN_PERIOD_KEY           "rw.1thl.nextLoginPeriod"
#define FIRST_LINK_COMMON_SERVER_DOMAIN_KEY        "rw.1thl.commonSDomain"   //"commonServerIP";
#define FIRST_LINK_COMMON_SERVER_DOMAIN_PORT_KEY   "rw.1thl.commonSDomaPort" //"commonServerDomainPort";
#define FIRST_LINK_COMMON_SERVER_ACTIVE_FLAG       "rw.1thl.commonSActFlag"  //"commonServerActiveFlag";
#define FIRST_LINK_LOCAL_STORE_LOGOUT_PERIOD_KEY   "rw.1thl.localloutPeriod" // "localStorelogoutPeriod";
#define FIRST_LINK_LOGINSN_KEY_NAME                "rw.1thl.loginSn"
#define FIRST_LINK_LOGINDAY_KEY_NAME               "rw.1thl.loginDay"
#define FIRST_LINK_ALERT_TEST_LEVEL_KEY            "mm.1thl.alertTestLevel" //终端命令开启报警标标志位
#define FIRST_LINK_AES_ENCRYPT_KEY                 "rw.1thl.encrypt" //第一链路数据存储加密开关 1-开 0关
#define FIRST_LINK_CA_FILE_PATH                    TBOX_CONFIG_RW_PATH "1thl_ca.pem"
#define FIRST_LINK_CERT_FILE_PATH                  TBOX_CONFIG_RW_PATH "1thl_cert.pem"
#define FIRST_LINK_KEY_FILE_PATH                   TBOX_CONFIG_RW_PATH "1thl_key.pem"
#define LINK1_GB_LOGIN_REQ_KEY                     "rw.static.gbLoginReq"  //国标登录应答标志位
#define LINK1_GB_LOGIN_BMS_ID_KEY                  "rw.static.gbLoginBmsId"  //国标登录电池编号
#define FIRST_LINK_LOGIN_STATUS_KEY                "mm.1thl.loginStatus" //第一链路登录状态 0-未登录 1-已登录
//第二链路相关属性
#if TBOX_ENV == 0
#define SECLINK_SERVER_DOMIN                    "47.112.118.163"
#define SECLINK_SERVER_PORT                     51011 //不加密端口
#define SECLINK_SERVER_PORT_SSL                 51013 //加密端口
#elif  TBOX_ENV == 1
#define SECLINK_SERVER_DOMIN                    "182.90.255.107"
#define SECLINK_SERVER_PORT                     61011 //不加密端口
#define SECLINK_SERVER_PORT_SSL                 61013 //加密端口
#else
//生成环境只有加密通道
#define SECLINK_SERVER_DOMIN                    "182.90.255.107"
#define SECLINK_SERVER_PORT                     21011 //不加密端口
#define SECLINK_SERVER_PORT_SSL                 21011 //加密端口
#endif
#define SECLINK_VENDOR_IDENTIFICATION           0x04  //供应商标识
#define SECLINK_CAR_MODEL_CODE                  0x044 //车型代号
#define SECLINK_CACHE_FILE_NAME                 "2thl_cache"
#define SECLINK_PROP_FILE_NAME                  TBOX_CONFIG_RW_PATH "rw.2thl"
#define SECLINK_LOCAL_SOTRE_PERIOD_KEY          "rw.2thl.localstoreperiod"   // "localStorePeriod";
#define SECLINK_NORMAL_REPORT_PERIOD_KEY        "rw.2thl.normalreportperiod" //"norReportPeriod";
#define SECLINK_ALARM_REPORT_PERIOD_KEY         "rw.2thl.alarmreportperiod"  //"alarmReportPeriod";
#define SECLINK_REMOTE_SERVER_DOMIN_CHANGE_FLAG "rw.2thl.remoteSDomainChg"   //"remoteServerDomainChangeFlag";
#define SECLINK_OLD_SERVER_DOMIN                "rw.2thl.oldRemoteSDomain"   //"old remoteServerDomain";
#define SECLINK_OLD_SERVER_PORT                 "rw.2thl.oldRemoteSPort"     //"old remoteServerPort";
#define SECLINK_HEART_BEAT_PERIOD_KEY           "rw.2thl.heartbeatPeriod"
#define SECLINK_TBOX_ACK_TIMEOUT_KEY            "rw.2thl.tboxAckTimeout"
#define SECLINK_SERVER_ACK_TIMEOUT_KEY          "rw.2thl.severAckTimeout"
#define SECLINK_NEXT_LOGIN_PERIOD_KEY           "rw.2thl.nextLoginPeriod"
#define SECLINK_LOCAL_STORE_LOGOUT_PERIOD_KEY   "rw.2thl.localloutPeriod" // "localStorelogoutPeriod";
#define SECLINK_LOGINSN_KEY_NAME                "rw.2thl.loginSn"
#define SECLINK_LOGINDAY_KEY_NAME               "rw.2thl.loginDay"
#define SECLINK_GB_EXTEND_CODE                  "rw.2thl.gbExtendCode"    //国标扩展字段配置信息代号
#define SECLINK_QB_CONFIG_CODE                  "rw.2thl.qbConfigCode"    //企标配置信息代号
#define SECLINK_TERMINAL_ALARM_FLAG             "rw.2thl.alarmFlag"       //终端报警信息记录
#define SECLINK_ALERT_TEST_LEVEL_KEY            "mm.2thl.alertTestLevel"  //终端命令开启报警标标志位
#define SECLINK_FAULT_SHOT_DURTION              "rw.2thl.faultShotDur"    //故障产生应保存CAN报文前后时间
#define SECLINK_FAULT_SHOT_UP_METHOD            "rw.2thl.faultShotUpMeth" //故障CAN报文上传方式
#define SECLINK_FAULT_SHOT_UP_URL               "rw.2thl.faultShotUpUrl"  //CAN报文上传地址
#define ACC_ON_TIME_KEY                         "mm.2thl.accOnTime"       //acc上电时间
#define SECLINK_SLEEP_HEART_PERIOD_KEY          "rw.2thl.sleepHeartBeatPeriod" //第二链路休眠状态下心跳间隔周期 测试时修改使用
#define SECLINK_SMART_CHARGE_CHECK_INTERVAL_KEY "rw.2thl.smartChargeCkInterval" //智能补电判断周期 测试时修改使用

#define SECLINK_CA_FILE_PATH                    TBOX_CONFIG_RW_PATH "2thl_ca.pem"
#define SECLINK_CERT_FILE_PATH                  TBOX_CONFIG_RW_PATH "2thl_cert.pem"
#define SECLINK_KEY_FILE_PATH                   TBOX_CONFIG_RW_PATH "2thl_key.pem"
#define SECLINK_BUSY_FLAG_KEY                   "mm.2thl.busyFlag" //终端忙碌标志位
#define SECLINK_BUSY_RMTCTRL                    0xff & (0x01 << 0) //远控忙
#define SECLINK_BUSY_CAR_STATUS_QUERY           0xff & (0x01 << 1) //车辆状态查询忙
#define SECLINK_BUSY_SHOT_REPORT                0xff & (0x01 << 2) //故障快照上报忙
#define SECLINK_BUSY_FILE_QUERY                 0xff & (0x01 << 3) //文件查询忙
#define SECLINK_BUSY_FILE_UPLOAD                0xff & (0x01 << 4) //文件上传忙
#define SECLINK_BUSY_ECTEND_DATA                0xff & (0x01 << 5) //扩展配置文件忙
#define SECLINK_BUSY_HEATH_QUERY                0xff & (0x01 << 6) //健康查询忙
#define SECLINK_BUSY_UPGRADE                    0xff & (0x01 << 7) //终端升级上报忙

//远控相关的配置
#define BOOK_CHARGE_PROCESS_STATE    "rw.rmtctrl.BkChrgState"
#define BOOK_CHARGE_SOC              "rw.rmtctrl.BkChrgSOC"
#define BOOK_CHARGE_CURRENT          "rw.rmtctrl.BkChrgCurrent"
#define BOOK_CHARGE_TIME             "rw.rmtctrl.BkChrgTime"
#define RMT_CTRL_AUTH_MASK           "rw.rmtctrl.AuthMask"
#define SMART_CHARGE_STATE           "rw.rmtctrl.SmartChrgState"
#define SMART_CHARGE_START_FLAG_KEY  "mm.SmartChargeState" //智能补电开启状态 0 未补电 1 补电开启

//TBox金融锁车异常处理配置
#define GPS_FAULT_NUM_KEY               "mm.GpsFaultNum"      //gps故障天数
#define TSP_COMM_FAULT_NUM_KEY          "mm. TspCommFaultNum" //tsp通信故障天数
#define SAVE_ODOMETER_KEY               "rw.SaveOdometer"     // 存储里程
//第三链路相关属性
#if TBOX_ENV == 0
#define THIRD_LINK_DEFAULT_DOMIN    "47.112.118.163"
#define THIRD_LINK_DEFAULT_PORT     443
#define THIRD_LINK_SSL_PORT         8443
#elif  TBOX_ENV == 1
#define THIRD_LINK_DEFAULT_DOMIN    "182.90.255.107"
#define THIRD_LINK_DEFAULT_PORT     18080
#define THIRD_LINK_SSL_PORT         18443
#else
#define THIRD_LINK_DEFAULT_DOMIN    "182.90.255.106"
#define THIRD_LINK_DEFAULT_PORT     443
#define THIRD_LINK_SSL_PORT         8443
#endif


//CAN数据采集上报
#define CAN_REPORT_INFO_KEY "rw.static.canReport"
#define CAN_REPORT_FLAG_KEY "rw.static.canReportFlag"
#define CAN_TARGET_VEHICLE_LIM_STATE_KEY "rw.static.tarLimt"
#define  TBOX_ARM_LOG_BACKUP_INDEX_PRE_KEY       "rw.logconfig.I"


#define CA_CERT_FILE_MAX_LEN 4096 //ca证书长度
#define KEY_FILE_MAX_LEN     2048 //客户端密钥长度
#define CERT_FILE_MAX_LEN    2048 //客户端证书长度
#endif

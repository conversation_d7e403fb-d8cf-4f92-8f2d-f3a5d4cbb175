<?xml version="1.0" encoding="utf-8"?>
<ISOFT Name="FullCanCollect" Desc="Full CAN Collect">
	<!-- 最新数据通知周期，0表示不通知，单位：秒  -->
	<NotifyInterval>10</NotifyInterval>
	<!-- 最新数据查询后是否清除 -->
	<ClearAfterQuery>1</ClearAfterQuery> <!-- 保持为0，与测试断言相符 -->
	<!-- 全量CAN文件存储周期，单位：秒 -->
	<FileStorageInterval>10</FileStorageInterval> <!-- 修改为60 -->
	<!-- 全量CAN文件存储个数，不能超过 MAX_FULL_CAN_FILE_COUNT -->
	<TotalFileCount>10</TotalFileCount> <!-- 修改为10 -->
	<!-- 可能的最大 CAN 数量，当 SpecifiedCanIdCount 为 0 时有效，不能超过CAN_CACHE_MAX_SIZE(300) -->
	<MaxCanIdCount>150</MaxCanIdCount> <!-- 修改为100 -->
	<!-- 指定 CAN ID 数量，0 表示全量 -->
	<SpecifiedCanIdCount>0</SpecifiedCanIdCount> <!-- 修改为2 -->
	<!-- 指定 CAN ID 列表，当 SpecifiedCanIdCount 非 0 时有效 -->
	<SpecifiedCanIdList></SpecifiedCanIdList>
    <!-- 触发接收列表数量 -->
    <TriggerOnReceiveCount>4</TriggerOnReceiveCount>
    <!-- 触发接收列表，当 TriggerOnReceiveCount 非 0 时有效 -->
    <TriggerOnReceiveList>
        <TriggerInfo>
            <CanId>0x1822d0f3</CanId>
            <CanCh>1</CanCh>
        </TriggerInfo>
        <TriggerInfo>
            <CanId>0x181ed0f3</CanId>
            <CanCh>1</CanCh>
        </TriggerInfo>
        <TriggerInfo>
            <CanId>0xc04a1a7</CanId>
            <CanCh>1</CanCh>
        </TriggerInfo>
        <TriggerInfo>
            <CanId>0xc0ca1a7</CanId>
            <CanCh>1</CanCh>
        </TriggerInfo>
    </TriggerOnReceiveList>
</ISOFT>
/*
      TspMain.h
描述：此文件用于描述TSP数据定义
作者：liuyuanqiang
时间：2020.12.18
*/

#ifndef _TSPMAIN_H_
#define _TSPMAIN_H_

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "typedefs.h"
#include "GBMainHandle.h"
#include "GBProtoHandle.h"
#include "SystemListApi.h"
#include "SpecialDataProcess.h"
#include "pthread.h"
#include "tbox_macro.h"

#define TSP_AUTO_NOTIFY_TASK_NAME         "tsp_auto_notify_task_name"
#define TSP_HANDLE_TASK_NAME              "tsp_handle_task_name"
#define TSP_SEND_TASK_NAME                "tsp_send_task_name"
#define TSP_COLLECT_DATA_TASK_NAME        "tsp_collect_data_task_name"
#define TSP_STACK_SIZE                    12288 //8192
#define TSP_HANDLE_STACK_SIZE             20480
//#define DEFAULT_VEHICLE_VIN              "ONEIOTWORLDTESTSP"
//#define DEFAULT_VEHICLE_VIN              "LTEYANMINGONETEST"
#define DEFAULT_VEHICLE_VIN              "00000000000000000"

#define        TBOX_MAX_CONFIG_PARA_LEN                128
// #ifndef TSP_DOMAIN_CONFIG_PARA_LEN
// #define        TSP_DOMAIN_CONFIG_PARA_LEN              128
// #endif
// #ifndef TSP_VERSION_CONFIG_PARA_LEN
// #define        TSP_VERSION_CONFIG_PARA_LEN             10
// #endif
#define        TSP_VIN_MAX_LEN                        18
#define        TSP_ICCID_LENGTH                    23
#define        TSP_IMEI_LENGTH                    16
#define        TSP_SN_LENGTH                      7
#define        TSP_ACTIVE_LEN_MAX                     50
#define        TSP_NET_RECONECT_COUNT_MAX              10

#define        TSP_CONNECTED_SLEEP_TIME           20
#define        TSP_DISCONNECTED_SLEEP_TIME        10
#define        TSP_HANDLE_TIME                    180
#define        TSP_SLEEP_DISAGREE_FALG            0x01
#define        TSP_SLEEP_AGREE_FALG               0x00
#define        TSP_CONTROL_FALG                   0x01
#define        TSP_DIAGNOSE_FALG                  0x02
#define        TSP_SLEEP_PERIOD                   1017 //MS，补偿系统调度延迟，确保60次循环正好是60秒
#define        TSP_WAIT_DATA_TIMEOUT              1   //秒
#define        TSP_CONNECT_EXIT_MAX_CNT           3    //连续3次快速退出视为连接异常

#define        TSP_MAX_RECONNECT_COUNT            10

#define TSP_DEFAULT_LOCAL_STORE_PERIOD         1000
#define TSP_DEFAULT_NORMAL_REPORT_PERIOD       10
#define TSP_DEFAULT_ALARM_REPORT_PERIOD        1000
#define TSP_DEFAULT_REMOTE_SERVER_PORT         9014
#define TSP_DEFAULT_REMOTE_SERVER_ACTIVE       0
#define TSP_DEFAULT_HEARTBEAT_PERIOD           60
#define TSP_DEFAULT_TBOX_ACK_TIMEOUT           30
#define TSP_DEFAULT_SEVER_ACK_TIMEOUT          300
#define TSP_DEFAULT_NEXT_LOGIN_PERIOD          30
#define TSP_DEFAULT_COMMON_SERVER_ACTIVE       1
#define TSP_DEFAULT_REMOTE_SERVER_DOMAIN       TSP_NET_SERVER_DOMAIN
#define TSP_DEFAULT_COMMON_SERVER_DOMAIN       TSP_NET_SERVER_DOMAIN  // 宏中引用另一个定义
#define TSP_DEFAULT_REMOTE_SERVER_DOMAIN_LEN   0

#if 1
#define TspLogI(fmt,...) \
do \
{ \
    LogServiceBuffer(CLIENT_MODULE_TSP,TBOX_LOG_INFO, "Tsp:"fmt,##__VA_ARGS__); \
}while(0)
#else
#define FOTALogI(fmt,...)
#endif


#if 1
#define TspLogE(fmt,...) \
do \
{ \
    LogServiceBuffer(CLIENT_MODULE_TSP,TBOX_LOG_ERROR, "Tsp:"fmt,##__VA_ARGS__); \
}while(0)
#else
#define FOTALogE(fmt,...)
#endif

#if 1
#define TspLogD(fmt,...) \
do \
{ \
    LogServiceBuffer(CLIENT_MODULE_TSP,TBOX_LOG_DEBUG, "Tsp:"fmt,##__VA_ARGS__); \
}while(0)
#else
#define CanDiagLogD(fmt,...)
#endif

#define SET_RESTORE_PARAM_AND_LOG(key, val) \
    do { \
        g_tspRamData.configInfo.key = val; \
        ret = SetIntProperty(c_##key, g_tspRamData.configInfo.key); \
        if (ret != 0) { \
            TspLogE("TSP:SetIntProperty " #key " failed, ret=%d\n\n", ret); \
        } \
    } while(0)

#define SET_INT_CONFIG(KEY, VALUE) do {                      \
    int __ret = SetIntProperty((KEY), (VALUE));                     \
    if (__ret != 0) {                                               \
        LogServiceBuffer(CLIENT_MODULE_TSP, TBOX_LOG_INFO,          \
            "TSP:SetIntProperty %s failed, ret=%d\n\n", #KEY, __ret); \
        ret = __ret;                                                \
    }                                                               \
} while (0)

#define SET_INT_PARAM_AND_LOG(KEY, VALUE) do {                          \
    int __ret = SetIntProperty((KEY), (VALUE));                         \
    if (__ret != 0) {                                                   \
        LogServiceBuffer(CLIENT_MODULE_TSP, TBOX_LOG_INFO,              \
            "TSP:SetIntProperty %s failed, ret=%d\n\n", #KEY, __ret);   \
        ret = __ret;                                                    \
    }                                                                   \
} while (0)

#define SET_STRING_PARAM_AND_LOG(KEY, STRPTR) do {                  \
    int __ret = SetStringProperty((KEY), (STRPTR));                 \
    if (__ret != 0) {                                               \
        LogServiceBuffer(CLIENT_MODULE_TSP, TBOX_LOG_INFO,          \
            "TSP:SetStringProperty %s failed, ret=%d\n\n", #KEY, __ret); \
        ret = __ret;                                                \
    }                                                               \
} while (0)

typedef enum
{
  TSP_CUSTOM_IP_TYPE_OR_DNS_NONE = -1,
  TSP_CUSTOM_IP_TYPE_OR_DNS_IPV4 = 0,
  TSP_CUSTOM_IP_TYPE_OR_DNS_IPV6 = 1,
  TSP_CUSTOM_IP_TYPE_OR_DNS_DNS = 2
}TspIpTypeOrDns_e;

typedef enum
{
    TSP_PROTOCOL_GB = 0,
    TSP_PROTOCOL_BQ,
    /*…*/
    TSP_MAX_PROTOCOL,
} TspProtocolType_e;

typedef enum
{
    NET_LED_RED_FLICK,
    NET_LED_GREEN_FLICK,
    NET_LED_GREE_FLICK_SLOW,
}NetLedState_en;


typedef struct tspConfigInfo
{
    uint16_t  localStorePeriod;
    uint16_t  normalReportPeriod;
    uint16_t  alarmReportPeriod;
    uint16_t  LogoutReportPeriod;
    uint8_t   remoteServerDomainLen;
    uint8_t   remoteServerDomain[TSP_DOMAIN_CONFIG_PARA_LEN];
    uint32_t  remoteServerDomainIP;
    uint16_t  remoteServerDomainPort;
    uint8_t   remoteServerActiveFlag;
    uint8_t   remoteDomainChangeFlag;
    uint8_t   needReconnectFlag;      //重连标志位
    uint8_t   hrVersion[TSP_VERSION_CONFIG_PARA_LEN];
    uint8_t   swVersion[TSP_VERSION_CONFIG_PARA_LEN];
    uint8_t   heartbeatPeriod;
    uint16_t  tboxAckTimeout;
    uint16_t  severAckTimeout;
    uint8_t   nextLoginPeriod;
    uint8_t   commonServerDomainLen;
    uint8_t   commonServerDomain[TSP_DOMAIN_CONFIG_PARA_LEN];
    uint32_t  commonServerDomainIP;
    uint16_t  commonServerDomainPort;
    uint8_t   commonServerActiveFlag;
    
    uint8_t   newServerDomain[TSP_DOMAIN_CONFIG_PARA_LEN];  //当实现域名切换，且登入平台时将配置写入文件
    uint8_t   newServerDomainLen;
    uint16_t  newServerDomainPort;
    uint8_t   newServerActiveFlag;                      //0 无效 1配置 2成功登入


    uint8_t   encryptType;
    char      vin[TSP_VIN_MAX_LEN];
    uint8_t   vinFlag;
    uint8_t   rxPrintf;
    uint8_t   txPrintf;
    char      iccid[TSP_ICCID_LENGTH];
    char      imei[TSP_IMEI_LENGTH];
    char      serialNo[TSP_SN_LENGTH];
} TspConfigInfo_s;


typedef struct tspStatusInfo
{
    //uint32_t         ip;                 /*IP地址*/
    uint8_t          carStartFlag;       /*汽车启动标志  0：未启动  1 已启动*/
    uint8_t          connectFlag;        /*通讯连接标志  0：未连接  1 已连接*/
    uint8_t          connectCount;       /*通讯连接次数*/
    uint8_t          logInFlag;          /*国标登入标志  0：未登入或登入失败  1 已登入*/
    uint8_t          logOutFlag;         /*国标登出标志  0：未登出或登出失败  1 已登出*/
    uint8_t          loginFristFlag;
    uint8_t          loginFristCount;
    uint8_t          nextloginCount;
    uint8_t          period;
    uint8_t          statusRecord;
    uint8_t          synctimeFlag;
    //uint8_t          ip_flag;            //新域名的ip地址有效标志 0 无效 1有效
    uint8_t          beatHeartCount;
    uint8_t          logoutCount;
    uint8_t          ledState;
    TimeInfo       time;
    pthread_mutex_t    lock;               /*状态信息互斥量*/
    uint8_t          getVehicleDescriptorFlag;
    uint8_t          sleepFlag;
    uint8_t          sleepDisagreeFlag;
    int64_t          sleepDisagreeTime;
    uint8_t          sleepWakeUpSource;
    uint8_t          tspBusyFlag;
    int64_t          loginTime;
    int64_t          currentTime;
    int64_t          wakeupTime;
} TspStatusInfo_s;

typedef struct
{
    int16_t          socketFd;
    // mbedtls_net_context server_fd;
    // mbedtls_entropy_context entropy;
    // mbedtls_ctr_drbg_context ctr_drbg;
    // mbedtls_ssl_context ssl;
    // mbedtls_ssl_config conf;
    // mbedtls_x509_crt cacert;
    //uint32_t         ip; 
    pthread_mutex_t  lock;  
}TspSocketInfo_s;

typedef struct
{
    TspProtocolType_e protocolType;
    
    //pthread_mutex_t  lock;  
}TspProtocolInfo_s;


typedef struct
{
    TspProtocolType_e protocolType;
    pthread_t tspAutoNotifyThreadId;
    pthread_t tspHandleThreadId;
    pthread_t tspSendThreadId;
    pthread_t tspfileMonitThreadId;
    pthread_t tspCollectDataThreadId;
    TspStatusInfo_s statusInfo;
    TspConfigInfo_s configInfo;    //与配置参数相关
    TspSocketInfo_s socketInfo;    //与网络连接相关
    //TspProtocolInfo_s protocolInfo;
    int systemSleep;
}TspRamData_s;

typedef struct
{
    int64_t sendTime;

}TspRamDataTime_s;


extern TspRamData_s g_tspRamData;
extern TspRamDataTime_s g_tspRamDataTime;

typedef void (*pTspParamInitCallBack)(void);
typedef void (*pTspInfoNotifyCallBack)(void);
typedef void (*pTspMsgHandleCallBack)(char *buf, int len);

#define TASK_SLEEP_PERIOD        1000 //ms
typedef struct
{
    TspProtocolType_e       protocolType;
    pTspParamInitCallBack   paramInit;
    pTspInfoNotifyCallBack  infoNotify;
    pTspMsgHandleCallBack   msgHandle;
}TspProtoHandle_s;

int TspInit(void);
void TspSocketDestroy(void);
int TspSocketClose(TspRamData_s *ramData);
int TspSocketCloseNow(TspRamData_s *ramData);

TspProtoHandle_s *TspGetProtoclHandle();
int TspGetConfigInfo(TspConfigInfo_s *configInfo);
int TspConfigInfoFlashGet(TspConfigInfo_s *configInfo);
int TspConfigInfoFlashSet(TspConfigInfo_s *configInfo);

uint8_t GetNomalReportPeriod(void);
uint8_t GetAlarmLocalSavePeriod(void);
uint8_t GetAlarmReportPeriod(void);
int TspGetVehicleDescriptor(void);
bool GetTspSleepStatus(void);
void SetSleepFlag(bool val);
bool GetSleepFlag(void);
void InitSleepFlagStatus(void);
#endif


#include "TspMain.h"
#include <arpa/inet.h>
#include <errno.h>
#include <netdb.h>
#include <netinet/in.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <unistd.h>
#include "CanPartialParser.h"
#include "CanServiceApi.h"
#include "QbAlarm.h"
#include "SystemApi.h"
#include "SystemControlc.h"
#include "TspNotify.h"
#include "TspSend.h"
#include "gb.h"
#include "qb.h"
#include "iot_dbus.h"
#include "iot_logc.h"
#include "iot_rilc.h"
#include "property_api.h"
#include "pthread.h"
#include "signal.h"
#include "tbox_paraconfig.h"


#define PROTOCOL_GB
#include "CanApi.h"
#include "GBMainHandle.h"

#if defined(PROTOCOL_BQ)
#define TSP_PROTOCOL_TYPE TSP_PROTOCOL_BQ
#else
#define TSP_PROTOCOL_TYPE TSP_PROTOCOL_GB
#endif

#define PROCESS_CIRCLE_MS 10

TspRamData_s g_tspRamData;
TspRamDataTime_s g_tspRamDataTime;

TspProtoHandle_s g_tspProtoHandle[TSP_MAX_PROTOCOL] = 
{
#if defined(PROTOCOL_GB)
    {TSP_PROTOCOL_GB, GBParamInit, GBNotifyTask, GBMsgHandle},
#endif
#if defined(PROTOCOL_BQ)
    {TSP_PROTOCOL_BQ, BQParamInit, BQNotifyTask, BQMsgHandle},
#endif
};

bool GetTspSleepStatus(void) { return GetSleepFlag(); }

const char c_localStorePeriod[] = "rw.tsp.localstoreperiod";                        // "localStorePeriod";  
const char c_normalReportPeriod[] = "rw.tsp.normalreportperiod";                   //"norReportPeriod";
const char c_alarmReportPeriod[] = "rw.tsp.alarmreportperiod";                  //"alarmReportPeriod";
const char c_remoteServerDomainLen[] = "rw.tsp.remoteserverdomainlen";          //"remoteServerDomainLen"
const char c_remoteServerDomain[] = "rw.tsp.remoteSDomain";                            //"remoteServerDomain";
const char c_remoteServerDomainIP[] = "rw.tsp.remoteSDomainIP";                        //"remoteServerDomainIP"
const char c_remoteServerDomainPort[] = "rw.tsp.remoteSDomainP";                       //"remoteServerDomainPort"
const char c_remoteServerActiveFlag[] = "rw.tsp.remSerActFlag";
const char c_hrVersion[] = "rw.tsp.hrVersion";
const char c_swVersion[] = "rw.tsp.swVersion";
const char c_heartbeatPeriod[] = "rw.tsp.heartbeatPeriod";
const char c_tboxAckTimeout[] = "rw.tsp.tboxAckTimeout";
const char c_severAckTimeout[] = "rw.tsp.severAckTimeout";
const char c_nextLoginPeriod[] = "rw.tsp.nextLoginPeriod";
const char c_commonServerDomainLen[] = "rw.tsp.commonSDomaLen";            //"commonServerDomainLen";
const char c_commonServerDomain[] = "rw.tsp.commonSDomain";                //"commonServerDomain";
const char c_commonServerDomainIP[] = "rw.tsp.commonSeDomaIP";             //"commonServerDomainIP";
const char c_commonServerDomainPort[] = "rw.tsp.commonSDomaPort";          //"commonServerDomainPort";
const char c_commonServerActiveFlag[] = "rw.tsp.commonSActFlag";           //"commonServerActiveFlag";
const char c_newServerDomain[] = "rw.tsp.newServerDomain";
const char c_newServerDomainLen[] = "rw.tsp.newSDomainLen";                //"newServerDomainLen";
const char c_newServerDomainPort[] = "rw.tsp.newSDomainPort";              //"newServerDomainPort";
const char c_newServerActiveFlag[] = "rw.tsp.newSActiveFlag";              //"newServerActiveFlag";
const char c_localStorelogoutPeriod[] = "rw.tsp.localloutPeriod";          // "localStorelogoutPeriod"; 
const char c_vin[] = "rw.tsp.vin";
const char c_id[] = "rw.tsp.id";

void TspSocketDestroy(void);
int SetPlatformSecondServerConfig(const char *ip, uint16_t port);
void TspCopyDomain(TspRamData_s *ramData);

void  ResetLogoutPeriod(void)
{
    //使循环时间加长，把此函数出现的地方变为第一次logout发起的时间
    g_tspRamData.statusInfo.period = 1;
}


uint8_t GetNomalReportPeriod(void)
{
    uint8_t nomalPeriod = 0;

    nomalPeriod = GetIntProperty(c_normalReportPeriod,10);;
    TspLogD("The Normal Report Period is %u",nomalPeriod);
    return nomalPeriod;
}

uint8_t GetAlarmReportPeriod(void)
{
    uint8_t alarmPeriod = 0;

    alarmPeriod = (GetIntProperty(c_alarmReportPeriod,1000))/1000;
    TspLogD("The Alarm Report Period is %u",alarmPeriod);
    return alarmPeriod;
}

uint8_t GetAlarmLocalSavePeriod(void)
{
    uint8_t alarmSavePeriod = 0;

    alarmSavePeriod = (GetIntProperty(c_localStorePeriod,1000))/1000;
    TspLogD("The Alarm Local Save Period is %u",alarmSavePeriod);
    return alarmSavePeriod ;
}



/*************************************************
函数名称: SystemCreatePriApn
函数功能: 创建私有APN，只在SIM卡存在时创建
输入参数: 无
输出参数: 无
函数返回类型值：TBOX_OK - 创建成功或SIM不存在，其他 - 创建失败
编写者: liuyuanqiang
编写日期：2021/03/17
*************************************************/
int TspCreatePriApn(void)
{
    int ret = TBOX_OK;

//    ret = AddAManualApn(APN_PROFILE_MANUAL_3, APP_TECH_AUTO, PRIVATE_APN_NAME);
    //无卡时不创建APN
    if(TBOX_OK != ret)
    {
        return ret;
    }

    return TBOX_OK;
}
void TimeCalibrateCb(TimeInfo *pTimeInfo, TimeCalcFlag type)
{

}
/*************************************************
函数名称: TspPMStatusReportCb
函数功能: 电源管理的回调函数
输入参数: powerMode - 休眠或唤醒
输出参数: 无
函数返回类型值：无
编写者: 郑勇
编写日期：2021/02/05
*************************************************/
void PMSleepEventCb(PmStatus status)
{
    LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "pm sleep event callback:sleepevelt:[%d]",status);
    if(PM_STATUS_NORAML_SLEEP_READY == status)
    {
        LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO,"PM_STATUS_NORAML_SLEEP_READY");
        if(1 == g_tspRamData.statusInfo.logInFlag)
        {
            SetSleepFlag(true);
            SetTotalOdometerToFlash();  //当要休眠时，将数据写入到flash
            GBLogout();
            LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO,"logout then wait 1s");
            sleep(1);
        }
        g_tspRamData.statusInfo.carStartFlag = 0;
        g_tspRamData.statusInfo.sleepDisagreeFlag = 0;
    //    g_tspRamData.statusInfo.sleepFlag = 1;
        
    }
}

/*************************************************
函数名称: TspGetProtoclHandle
函数功能: tsp获取当前协议回调函数
输入参数:  
输出参数: 无
函数返回类型值：TspProtoHandle_s
编写者: liuyuanqiang
编写日期：2021/1/14
*************************************************/
TspProtoHandle_s *TspGetProtoclHandle()
{
    return &g_tspProtoHandle[TSP_PROTOCOL_TYPE];
}


/*************************************************
函数名称: TspConfigInfoFlashGet
函数功能: tsp configInfo flash数据读取
输入参数: configInfo 
输出参数: 无
函数返回类型值：TBOX_OK - 初始化成功，其他 - 失败
编写者: liuyuanqiang
编写日期：2020/12/24
*************************************************/
int TspConfigInfoFlashGet(TspConfigInfo_s *configInfo)
{
    int ret = TBOX_OK;
    uint32_t len;
    char long_buf[TSP_DOMAIN_CONFIG_PARA_LEN] = {0};

    configInfo->localStorePeriod = GetIntProperty(c_localStorePeriod, TSP_DEFAULT_LOCAL_STORE_PERIOD);  //
    TspLogI("TSP:GetIntProperty c_localStorePeriod [0x%x]\n\n", configInfo->localStorePeriod);  
    
    configInfo->normalReportPeriod = GetIntProperty(c_normalReportPeriod, TSP_DEFAULT_NORMAL_REPORT_PERIOD);//10s
    TspLogI("TSP:GetIntProperty c_normalReportPeriod [0x%x]\n\n", configInfo->normalReportPeriod);

    configInfo->alarmReportPeriod = GetIntProperty(c_alarmReportPeriod, TSP_DEFAULT_ALARM_REPORT_PERIOD);
    TspLogI("TSP:GetIntProperty c_alarmReportPeriod [0x%x]\n\n", configInfo->alarmReportPeriod);

    configInfo->remoteServerDomainLen = GetIntProperty(c_remoteServerDomainLen, TSP_DEFAULT_REMOTE_SERVER_DOMAIN_LEN); 
    TspLogI("TSP:GetIntProperty c_remoteServerDomainLen [%d]\n\n", configInfo->remoteServerDomainLen);

    memset(long_buf, 0, TSP_DOMAIN_CONFIG_PARA_LEN);
    len = TSP_DOMAIN_CONFIG_PARA_LEN;
    ret = GetStringProperty(c_remoteServerDomain, long_buf, len, "null");
    if(ret != 0)
    {
        memset(configInfo->remoteServerDomain, 0x00, TSP_DOMAIN_CONFIG_PARA_LEN);
    }
    else
    {
        //将数据重缓存中拷贝到结构体中
        memset(configInfo->remoteServerDomain, 0, TSP_DOMAIN_CONFIG_PARA_LEN);
        memcpy(configInfo->remoteServerDomain, long_buf, configInfo->remoteServerDomainLen);
        TspLogI("TSP:GetStringProperty c_remoteServerDomain[%s]\n\n", configInfo->remoteServerDomain);
    }

    configInfo->remoteServerDomainLen = strlen((char *) configInfo->remoteServerDomain);
    configInfo->remoteServerDomainPort = GetIntProperty(c_remoteServerDomainPort, 0); 
    TspLogI("TSP:GetIntProperty c_remoteServerDomainPort [0x%x]\n\n", configInfo->remoteServerDomainPort);

    configInfo->remoteServerActiveFlag = GetIntProperty(c_remoteServerActiveFlag, TSP_DEFAULT_REMOTE_SERVER_ACTIVE); 
    TspLogI("TSP:GetIntProperty c_remoteServerActiveFlag [%d]\n\n", configInfo->remoteServerActiveFlag);
    
    memset(configInfo->hrVersion, 0x00, TSP_VERSION_CONFIG_PARA_LEN);
    memset(configInfo->swVersion, 0x00, TSP_VERSION_CONFIG_PARA_LEN);

    strncpy((char *)configInfo->hrVersion, HARDWARE_VERSION, sizeof(configInfo->hrVersion));
    snprintf((char *)configInfo->swVersion, sizeof(configInfo->swVersion), "V%2d.%02d.%02d", PRODUCTION_STAGE, ARM_MAIN_VERION, ARM_SUB_VERION);

    configInfo->heartbeatPeriod = GetIntProperty(c_heartbeatPeriod, TSP_DEFAULT_HEARTBEAT_PERIOD);
    TspLogI("TSP:GetIntProperty heartbeatPeriod [%d]\n\n", configInfo->heartbeatPeriod);

    configInfo->tboxAckTimeout = GetIntProperty(c_tboxAckTimeout, TSP_DEFAULT_TBOX_ACK_TIMEOUT); 
    TspLogI("TSP:GetIntProperty tboxAckTimeout [%d]\n\n", configInfo->tboxAckTimeout);

    configInfo->severAckTimeout = GetIntProperty(c_severAckTimeout, TSP_DEFAULT_SEVER_ACK_TIMEOUT); 
    TspLogI("TSP:GetIntProperty severAckTimeout [%d]\n\n", configInfo->severAckTimeout);
    
    configInfo->nextLoginPeriod = GetIntProperty(c_nextLoginPeriod, TSP_DEFAULT_NEXT_LOGIN_PERIOD); 
    TspLogI("TSP:GetIntProperty nextLoginPeriod [%d]\n\n", configInfo->nextLoginPeriod);


    configInfo->commonServerDomainLen = GetIntProperty(c_commonServerDomainLen, 20); 
    TspLogI("TSP:GetIntProperty commonServerDomainLen [%d]\n\n", configInfo->commonServerDomainLen);

    memset(long_buf, 0, TSP_DOMAIN_CONFIG_PARA_LEN);
    len=TSP_DOMAIN_CONFIG_PARA_LEN;
    memset(configInfo->commonServerDomain, 0x00, TSP_DOMAIN_CONFIG_PARA_LEN);
    ret = GetStringProperty(c_commonServerDomain, long_buf, len, TSP_NET_SERVER_DOMAIN);
    if(ret != 0)
    {
        strncpy((char *)g_tspRamData.configInfo.commonServerDomain, TSP_NET_SERVER_DOMAIN, strlen(TSP_NET_SERVER_DOMAIN));
        TspLogI("TSP:GetStringProperty c_commonServerDomain failed, ret=%d\n\n", ret);
    }
    else
    {
        memset(configInfo->commonServerDomain, 0, TSP_DOMAIN_CONFIG_PARA_LEN);
        snprintf((char *)configInfo->commonServerDomain,TSP_DOMAIN_CONFIG_PARA_LEN,"%s",long_buf);
    }

    configInfo->commonServerDomainPort = GetIntProperty(c_commonServerDomainPort, TSP_NET_SERVER_PORT);  
    TspLogI("TSP:Net:The commonServerDomain:[%s],Port [%d]\n\n",configInfo->commonServerDomain ,configInfo->commonServerDomainPort);

    configInfo->commonServerActiveFlag = GetIntProperty(c_commonServerActiveFlag, TSP_DEFAULT_COMMON_SERVER_ACTIVE);  
    TspLogI("TSP:GetIntProperty c_commonServerActiveFlag [%d]\n\n", configInfo->commonServerActiveFlag);

    // 读取新域名相关配置
    configInfo->newServerDomainLen = GetIntProperty(c_newServerDomainLen, 0);
    TspLogI("TSP:GetIntProperty newServerDomainLen [%d]\n\n", configInfo->newServerDomainLen);

    memset(long_buf, 0, TSP_DOMAIN_CONFIG_PARA_LEN);
    len = TSP_DOMAIN_CONFIG_PARA_LEN;
    memset(configInfo->newServerDomain, 0x00, TSP_DOMAIN_CONFIG_PARA_LEN);
    ret = GetStringProperty(c_newServerDomain, long_buf, len, "");
    if(ret != 0)
    {
        TspLogI("TSP:GetStringProperty c_newServerDomain failed, ret=%d\n\n", ret);
    }
    else
    {
        memset(configInfo->newServerDomain, 0, TSP_DOMAIN_CONFIG_PARA_LEN);
        snprintf((char *)configInfo->newServerDomain, TSP_DOMAIN_CONFIG_PARA_LEN, "%s", long_buf);
    }

    configInfo->newServerDomainPort = GetIntProperty(c_newServerDomainPort, 0);
    TspLogI("TSP:Net:The newServerDomain:[%s],Port [%d]\n\n", configInfo->newServerDomain, configInfo->newServerDomainPort);

    configInfo->newServerActiveFlag = GetIntProperty(c_newServerActiveFlag, 0);
    TspLogI("TSP:GetIntProperty c_newServerActiveFlag [%d]\n\n", configInfo->newServerActiveFlag);

    configInfo->LogoutReportPeriod = GetIntProperty(c_localStorelogoutPeriod, 3); 
    TspLogI("TSP:GetIntProperty c_localStorelogoutPeriod [%d]\n\n", configInfo->LogoutReportPeriod);

    UpdateNomalReportPeriod(configInfo->normalReportPeriod); //设置此参数后，立即生效

    return ret;
}

/*************************************************
函数名称: TspConfigInfoFlashSet
函数功能: tsp configInfo flash数据读取
输入参数: configInfo - 待设置参数
输出参数: 无
函数返回类型值：TBOX_OK - 设置成功，其他 - 失败
编写者: leeyanming
编写日期：2021/5/31
*************************************************/
int TspConfigInfoFlashSet(TspConfigInfo_s *configInfo)
{
    int ret = TBOX_OK;

    if (configInfo == NULL)
    {
        LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "Para null for set tsp config info\n\n");
        return TBOX_ERROR_PARAMTER_WRONG;
    }

    memcpy(&g_tspRamData.configInfo, configInfo, sizeof(TspConfigInfo_s));

    SET_INT_PARAM_AND_LOG(c_localStorePeriod, configInfo->localStorePeriod);
    SET_INT_PARAM_AND_LOG(c_normalReportPeriod, configInfo->normalReportPeriod);
    SET_INT_PARAM_AND_LOG(c_alarmReportPeriod, configInfo->alarmReportPeriod);
    SET_INT_PARAM_AND_LOG(c_remoteServerDomainLen, configInfo->remoteServerDomainLen);
    SET_STRING_PARAM_AND_LOG(c_remoteServerDomain, (char *)configInfo->remoteServerDomain);
    SET_INT_PARAM_AND_LOG(c_remoteServerDomainPort, configInfo->remoteServerDomainPort);
    SET_INT_PARAM_AND_LOG(c_remoteServerActiveFlag, configInfo->remoteServerActiveFlag);
    SET_INT_PARAM_AND_LOG(c_heartbeatPeriod, configInfo->heartbeatPeriod);
    SET_INT_PARAM_AND_LOG(c_tboxAckTimeout, configInfo->tboxAckTimeout);
    SET_INT_PARAM_AND_LOG(c_severAckTimeout, configInfo->severAckTimeout);
    SET_INT_PARAM_AND_LOG(c_nextLoginPeriod, configInfo->nextLoginPeriod);
    SET_INT_PARAM_AND_LOG(c_commonServerDomainLen, configInfo->commonServerDomainLen);
    SET_STRING_PARAM_AND_LOG(c_commonServerDomain, (char *)configInfo->commonServerDomain);
    SET_INT_PARAM_AND_LOG(c_commonServerDomainPort, configInfo->commonServerDomainPort);
    SET_INT_PARAM_AND_LOG(c_commonServerActiveFlag, configInfo->commonServerActiveFlag);
    SET_INT_PARAM_AND_LOG(c_newServerDomainLen, configInfo->newServerDomainLen);
    SET_STRING_PARAM_AND_LOG(c_newServerDomain, (char *)configInfo->newServerDomain);
    SET_INT_PARAM_AND_LOG(c_newServerDomainPort, configInfo->newServerDomainPort);
    SET_INT_PARAM_AND_LOG(c_newServerActiveFlag, configInfo->newServerActiveFlag);
    SetPlatformSecondServerConfig((char *)configInfo->remoteServerDomain, configInfo->remoteServerDomainPort);

    return ret;
}

//接口供企标参数设置国标平台时调用
int SetGbPlatformMainServerConfig(const char *ip, uint16_t port)
{
    int ret = TBOX_ERROR;

    if( !ip || port == 0)
    {
        TspLogI("The Para of SetGbPlatformServerConfig invalid");
    }

    ret |= SetIntProperty(c_commonServerDomainPort, port);
    if(ret != TBOX_ERROR)
    {
        TspLogI("TSP: Set Server Domain Port failed, ret=%d\n\n", ret);
    }

    ret |= SetStringProperty(c_commonServerDomain, ip);
    if(ret != 0)
    {
        TspLogI("TSP:Set Server Domain failed, ret=%d\n\n", ret);
    }

    return ret ; 
}

int SetPlatformSecondServerConfig(const char *ip, uint16_t port)
{
    int ret = TBOX_ERROR;

    if(!ip || port == 0)
    {
        TspLogI("The Para of SetPlatformSecondServerConfig invalid");
        return ret;
    }

    ret = SetIntProperty("rw.second.commonSDomaPort", port);
    if(ret != TBOX_OK)
    {
        TspLogI("TSP: Set Second Server Domain Port[%d] failed, ret=%d\n\n", port, ret);
        return ret;
    }

    ret = SetStringProperty("rw.second.commonSDomain", ip);
    if(ret != TBOX_OK)
    {
        TspLogI("TSP:Set Second Server Domain[%s] failed, ret=%d\n\n", ip, ret);
    }

    return ret;
}

int SetPlatformThirdServerConfig(const char *ip, uint16_t port)
{
    int ret = TBOX_ERROR;

    if( !ip || port == 0)
    {
        TspLogI("The Para of SetPlatformThirdServerConfig invalid");
        return ret;
    }

    ret = SetIntProperty("rw.third.commonSDomaPort", port);
    if(ret != TBOX_OK)
    {
        TspLogI("TSP: Set third Server Domain Port failed, ret=%d\n\n", ret);
        return ret;
    }

    ret = SetStringProperty("rw.third.commonSDomain", ip);
    if(ret != TBOX_OK)
    {
        TspLogI("TSP:Set third Server Domain failed, ret=%d\n\n", ret);
    }

    return ret ; 
}

/*************************************************
函数名称: TspGetConfigInfo
函数功能: 查询TSP配置信息
输入参数: 无
输出参数: configInfo - TSP配置信息
函数返回类型值：TBOX_OK - 获取成功，其他 - 失败
编写者: liuyuanqiang
编写日期：2020/12/23
*************************************************/
int TspGetConfigInfo(TspConfigInfo_s *configInfo)
{
    if(NULL == configInfo)
    {
        LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "Para null for get tsp config info\n\n");
        return TBOX_ERROR_PARAMTER_WRONG;
    }

    memcpy((void *)configInfo, (void *)&g_tspRamData.configInfo, sizeof(TspConfigInfo_s));
    return TBOX_OK;
}

/*************************************************
函数名称: TspRamDataInit
函数功能: TspRamData参数初始化服务
输入参数: 无
输出参数: 无
函数返回类型值：TBOX_OK - 初始化成功，其他 - 失败
编写者: liuyuanqiang
编写日期：2020/12/23
*************************************************/
int TspRamDataInit(void)
{
    memset(&g_tspRamData, 0, sizeof(g_tspRamData)); //TODO: 结构体不完整
    memset(&g_tspRamDataTime, 0, sizeof(g_tspRamDataTime));

    g_tspRamData.tspAutoNotifyThreadId = -1;
    g_tspRamData.tspHandleThreadId = -1;
    g_tspRamData.tspSendThreadId = -1;
    g_tspRamData.tspCollectDataThreadId = -1;
    g_tspRamData.protocolType = TSP_PROTOCOL_TYPE;   //设置当前TSP对接协议

    memset(&g_tspRamData.statusInfo, 0, sizeof(g_tspRamData.statusInfo));
    InitSleepFlagStatus();
    g_tspRamData.statusInfo.ledState = NET_LED_RED_FLICK;
    
    //初始化config
    memset(&g_tspRamData.configInfo, 0, sizeof(g_tspRamData.configInfo));

    g_tspRamData.configInfo.remoteServerDomainIP = 0xabd95c4c;
    
    //g_tspRamData.configInfo.commonServerDomainIP = 0x79E535B7;

    g_tspRamData.configInfo.newServerDomainLen = 0;
    memset((char *)g_tspRamData.configInfo.newServerDomain, 0x00, TSP_DOMAIN_CONFIG_PARA_LEN);
    g_tspRamData.configInfo.newServerDomainPort = 0;
    g_tspRamData.configInfo.newServerActiveFlag = 0; //1设置成功 2连接成功 0无效

    g_tspRamData.configInfo.encryptType = 1;
    g_tspRamData.configInfo.vinFlag = 0;
    g_tspRamData.configInfo.rxPrintf = 0;
    g_tspRamData.configInfo.txPrintf = 0;
    memset(g_tspRamData.configInfo.vin, 0x00, TSP_VIN_MAX_LEN);
    memset(g_tspRamData.configInfo.iccid, 0x00, TSP_ICCID_LENGTH);
    memset(g_tspRamData.configInfo.imei, 0x00, TSP_IMEI_LENGTH);
    memset(g_tspRamData.configInfo.serialNo, 0x00, TSP_SN_LENGTH);
    
//    ReadOdometerFromFlash();
    TspConfigInfoFlashGet((TspConfigInfo_s *)&g_tspRamData.configInfo);

    //初始化socket
    memset(&g_tspRamData.socketInfo, 0, sizeof(g_tspRamData.socketInfo));
    g_tspRamData.systemSleep = 0;
    
 //   memcpy(g_tspRamData.configInfo.iccid, "89861120245045036931", TSP_ICCID_LENGTH);   //   89860119801147941405
    snprintf(g_tspRamData.configInfo.iccid,  sizeof(g_tspRamData.configInfo.iccid), 
        "%s", "89861120245045036931");

    memcpy(g_tspRamData.configInfo.imei, "866545050850566", TSP_IMEI_LENGTH);
    memcpy(g_tspRamData.configInfo.serialNo, "123456", TSP_SN_LENGTH);
    
    TspProtoHandle_s * tspProtoHandle = TspGetProtoclHandle();
    tspProtoHandle->paramInit();
    g_tspRamData.statusInfo.carStartFlag = 1;
    LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO,  "g_tspRamData.configInfo.vin is %s\n\n",g_tspRamData.configInfo.vin);
    return TBOX_OK;
}

void InitSleepFlagStatus(void)
{
    pthread_mutex_init(&g_tspRamData.statusInfo.lock, NULL);
    SetSleepFlag(false);
}

void SetSleepFlag(bool val)
{
    pthread_mutex_lock(&g_tspRamData.statusInfo.lock);
    g_tspRamData.statusInfo.sleepFlag = val ? 1 : 0;
    pthread_mutex_unlock(&g_tspRamData.statusInfo.lock);
}

bool GetSleepFlag(void)
{
    bool ret;
    pthread_mutex_lock(&g_tspRamData.statusInfo.lock);
    ret = g_tspRamData.statusInfo.sleepFlag;
    pthread_mutex_unlock(&g_tspRamData.statusInfo.lock);
    return ret;
}

int RestoreFactorySettings(void)
{
    int ret = TBOX_OK;

    SET_RESTORE_PARAM_AND_LOG(localStorePeriod, TSP_DEFAULT_LOCAL_STORE_PERIOD);
    SET_RESTORE_PARAM_AND_LOG(normalReportPeriod, TSP_DEFAULT_NORMAL_REPORT_PERIOD);
    SET_RESTORE_PARAM_AND_LOG(alarmReportPeriod, TSP_DEFAULT_ALARM_REPORT_PERIOD);
    SET_RESTORE_PARAM_AND_LOG(remoteServerDomainLen, TSP_DEFAULT_REMOTE_SERVER_DOMAIN_LEN);
    SET_RESTORE_PARAM_AND_LOG(remoteServerDomainPort, TSP_DEFAULT_REMOTE_SERVER_PORT);
//    SET_RESTORE_PARAM_AND_LOG(remoteServerDomain, TSP_DEFAULT_REMOTE_SERVER_DOMAIN);
    SET_RESTORE_PARAM_AND_LOG(remoteServerActiveFlag, TSP_DEFAULT_REMOTE_SERVER_ACTIVE);
    SET_RESTORE_PARAM_AND_LOG(heartbeatPeriod, TSP_DEFAULT_HEARTBEAT_PERIOD);
    SET_RESTORE_PARAM_AND_LOG(tboxAckTimeout, TSP_DEFAULT_TBOX_ACK_TIMEOUT);
    SET_RESTORE_PARAM_AND_LOG(severAckTimeout, TSP_DEFAULT_SEVER_ACK_TIMEOUT);
    SET_RESTORE_PARAM_AND_LOG(nextLoginPeriod, TSP_DEFAULT_NEXT_LOGIN_PERIOD);

    memset(g_tspRamData.configInfo.remoteServerDomain, 0x00, TSP_DOMAIN_CONFIG_PARA_LEN);
    SET_STRING_PARAM_AND_LOG(c_remoteServerDomain, TSP_DEFAULT_REMOTE_SERVER_DOMAIN);
    
    snprintf((char *)g_tspRamData.configInfo.commonServerDomain, sizeof(g_tspRamData.configInfo.commonServerDomain),
             "%s", TSP_NET_SERVER_DOMAIN);
    SET_STRING_PARAM_AND_LOG(c_commonServerDomain, (char *)g_tspRamData.configInfo.commonServerDomain);

    SET_RESTORE_PARAM_AND_LOG(commonServerDomainLen, strlen((char *)g_tspRamData.configInfo.commonServerDomain));
    SET_RESTORE_PARAM_AND_LOG(commonServerDomainPort, TSP_NET_SERVER_PORT);
    SET_RESTORE_PARAM_AND_LOG(commonServerActiveFlag, TSP_DEFAULT_COMMON_SERVER_ACTIVE);
    RestoreSampleFlag();
    TspSocketClose(&g_tspRamData);
    return ret;
}

/*************************************************
函数名称: TspParamInit
函数功能: TSP参数初始化服务
输入参数: 无
输出参数: 无
函数返回类型值：TBOX_OK - 初始化成功，其他 - 失败
编写者: liuyuanqiang
编写日期：2020/12/23
*************************************************/
int TspParamInit(void)
{
    int ret = TBOX_OK;

    ret = TspRamDataInit();
    if(TBOX_OK != ret)
    {
        return ret;
    }

    return TspSendInit();
}


int TspGetVehicleDescriptor(void)
{
    int             ret          = TBOX_ERROR;
    char            outBuf[1024] = {0};

    ret = GetStringProperty(TBOX_MAIN_ID, outBuf, 7, TERMINAL_MAIN_ID);
    if (ret != 0)
    {
        TspLogE("GetStringProperty get Terminal id fail.So used the default id:%s",TERMINAL_MAIN_ID);
        snprintf(outBuf,sizeof(outBuf),"%s",TERMINAL_MAIN_ID);

    }
    TspLogE("Terminal id[%s]",outBuf);
    SetTerminalID(outBuf);

    memset(outBuf,0,sizeof(outBuf));
    ret = GetModuleIccid(sizeof(outBuf), outBuf);
    if (ret != TBOX_OK)
    {
        TspLogE("Get iccid failed, So used the default iccid:%s.", 0000000000000000000000);
        snprintf(outBuf,sizeof(outBuf),"%s","0000000000000000000000");
    }
    TspLogI("iccid[%s]", outBuf);
    snprintf(g_tspRamData.configInfo.iccid,TSP_ICCID_LENGTH,"%s",outBuf);
    
    memset(outBuf,0,sizeof(outBuf));
    ret = GetModuleImei(sizeof(outBuf), outBuf);
    if (ret != 0)
    {
        TspLogE("Get imei failed, ret=%d.use df\n", ret);
        snprintf(outBuf,TSP_IMEI_LENGTH,"%s", "861518059905785");
    }
    TspLogI("Imei[%s]", outBuf);
    memcpy(g_tspRamData.configInfo.imei, outBuf, TSP_IMEI_LENGTH);
    snprintf(g_tspRamData.configInfo.imei,TSP_IMEI_LENGTH,"%s",outBuf);
    
    memset(outBuf,0,sizeof(outBuf));
    ret = GetStringProperty(CAR_VIN_CODE_KEY, outBuf, TSP_VIN_MAX_LEN, DEFAULT_VEHICLE_VIN);
    if (ret != 0)
    {
        TspLogE("GetStringProperty get vin code fail.");
        snprintf(outBuf,TSP_VIN_MAX_LEN,"%s", DEFAULT_VEHICLE_VIN);
    }
    snprintf(g_tspRamData.configInfo.vin,TSP_VIN_MAX_LEN,"%s", outBuf);
    TspLogI("Vin:[%s]", g_tspRamData.configInfo.vin);

    ret = GetStringProperty(c_id, outBuf, TSP_SN_LENGTH, "123456");
    if (ret != 0)
    {
        TspLogE("GetStringProperty get SN  fail, use default value\n");
        snprintf(outBuf,TSP_SN_LENGTH,"%s", "123456");
    }
    snprintf(g_tspRamData.configInfo.serialNo,TSP_SN_LENGTH,"%s", outBuf);

    g_tspRamData.configInfo.vin[17]      = '\0';
    g_tspRamData.configInfo.serialNo[6] = '\0';
    TspLogI("VIN:[%s], SN:[%s].", g_tspRamData.configInfo.vin, g_tspRamData.configInfo.serialNo);
    ret = TBOX_OK;

    return ret;
}


/*************************************************
函数名称: TspApnDetectCb
函数功能: 检查APN网络状态回调函数
输入参数: profile - profileID， res - 检查结果
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期：2021/02/27
*************************************************/
//void TspApnDetectCb(ApnProfile_en profile, ApnDetectRes_en res)
//{
//    LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO,  "TSP:APN DETECT for profile:%d, result:%d\n\n", profile, res);
//}

/*************************************************
函数名称: TspSocketEventCb
函数功能: TSP socket连接事件回调函数
输入参数: fd - socket id，event - 事件
输出参数: 无
函数返回类型值：无
编写者: 郑勇
编写日期：2021/06/03
*************************************************/
void TspSocketEventCb(int fd, SystemSocketEvent_en event)
{
    LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO,  "Got socket event:%d for id:%d\n\n", event, fd);

    switch(event)
    {
        case SYSTEM_SOCKET_EVENT_CONNECT:
            break;
        case SYSTEM_SOCKET_EVENT_ERR:
            break;
        default:
            break;
    }
}

/*************************************************
函数名称: TspJudgeIpOrDns
函数功能: 检查是IP还是域名
输入参数: str域名
输出参数: 无
函数返回类型值：TBOX_OK - 初始化成功，其他 - 失败
编写者: liuyuanqiang
编写日期：2020/12/25
*************************************************/
TspIpTypeOrDns_e TspJudgeIpOrDns(char *str)
{
	int len= 0;
	int strLen = 0;
	TspIpTypeOrDns_e retValue = TSP_CUSTOM_IP_TYPE_OR_DNS_DNS;
	if(str == NULL )
	{
		return TSP_CUSTOM_IP_TYPE_OR_DNS_NONE;
	}
	else
	{
		if(strlen(str) <= 0 )
		{
			return TSP_CUSTOM_IP_TYPE_OR_DNS_NONE;
		}
	}

	strLen = strlen(str);

	for(len = 0;len < strLen; len++)
	{
		if( ((*(str+len) >= '0') && (*(str+len) <= '9')) || (*(str+len) == '.') )
		{
			continue;
		}
		else
		{
			break;
		}
	}
	if(len == strLen)
	{
		retValue = TSP_CUSTOM_IP_TYPE_OR_DNS_IPV4;
		return retValue;
	}
	len = 0;
	for(len = 0;len < strLen; len++)
	{
		if( ((*(str+len) >= '0') && (*(str+len) <= '9')) ||
			((*(str+len) >= 'a') && (*(str+len) <= 'f')) ||
			((*(str+len) >= 'A') && (*(str+len) <= 'F')) ||
			(*(str+len) == ':')
			)
		{
			continue;
		}
		else
		{
			break;
		}
	}
	if(len == strLen)
	{
		retValue = TSP_CUSTOM_IP_TYPE_OR_DNS_IPV6;
		return retValue;
	}
	return retValue;
}
/*************************************************
函数名称: GetIpByName
函数功能: 根据主机名称获取IP地址
输入参数: host - 主机名称
输出参数: ip地址
返回值：0 - 获取成功，其他 - 获取失败
*************************************************/
int GetIpByName(const char *host, unsigned int *ip)
{
    char               buf[128];
    int                ret = 0;
    struct addrinfo    hints;
    struct addrinfo    *res, *curr;
    struct sockaddr_in *sa;

    memset(&hints, 0, sizeof(struct addrinfo));
    hints.ai_family = AF_INET;   /* Allow IPv4 */
    hints.ai_flags = AI_PASSIVE; /* For wildcard IP address AI_CANONNAME */
    hints.ai_protocol = 0;       /* Any protocol */
    hints.ai_socktype = SOCK_STREAM;

    if((ret = getaddrinfo(host, NULL, &hints, &res)) != 0)
    {
        LogServiceBuffer(CLIENT_MODULE_TSP, TBOX_LOG_ERROR, "GetIpByName getaddrinfo: %s", gai_strerror(ret));
        return TBOX_ERROR;
    }

    for (curr = res; curr != NULL; curr = curr->ai_next)
    {
        sa = (struct sockaddr_in *)curr->ai_addr;
        inet_ntop(AF_INET,&sa->sin_addr, buf, sizeof (buf));

        LogServiceBuffer(CLIENT_MODULE_TSP, TBOX_LOG_INFO, "GetIpByName get ip:%s by:%s", buf, host);
        *ip = inet_addr(buf);
        freeaddrinfo(res);
        return TBOX_OK;
    }

    LogServiceBuffer(CLIENT_MODULE_TSP, TBOX_LOG_ERROR, "GetIpByName fail, name:%s.",host);
    freeaddrinfo(res);
    return TBOX_ERROR;
}

/*************************************************
函数名称: TspSocketConnect
函数功能: 
输入参数: 未使用
输出参数: 无
函数返回类型值：TBOX_OK - 初始化成功，其他 - 失败
编写者: liuyuanqiang
编写日期：2020/12/25
*************************************************/
int TspSocketConnect(TspRamData_s *ramData)
{   
    char addr[TSP_DOMAIN_CONFIG_PARA_LEN] = {0};
    int port = 0, ret = TBOX_ERROR, i = 0;

    if(0 == ramData->statusInfo.carStartFlag)
    {
        TspLogE("TSP:NET:carStartFlag is false. no need connect\n\n");
        return TBOX_ERROR;
    }
    if(1 == ramData->statusInfo.connectFlag)
    {
        TspLogE("TSP:NET:connectFlag is true. no need connect\n\n");
        return TBOX_ERROR;
    }

    // 优先判断是否使用新域名
    if(1 == ramData->configInfo.newServerActiveFlag)
    {
        // 如果重连次数超过阈值,切换到通用域名
        if(ramData->statusInfo.connectCount > TSP_NET_RECONECT_COUNT_MAX/2)
        {
            TspLogI("TSP:NET:Connect count exceed limit, switch to common domain");
            ramData->configInfo.newServerActiveFlag = 0;
            memcpy(addr, ramData->configInfo.commonServerDomain, sizeof(ramData->configInfo.commonServerDomain));
            port = ramData->configInfo.commonServerDomainPort;
            SetIntProperty(c_newServerActiveFlag, 0); // 持久化存储newServerActiveFlag状态
        }
        else
        {
            TspLogI("TSP:NET:Using new domain addr [%s]\n\n", ramData->configInfo.newServerDomain);
            memcpy(addr, ramData->configInfo.newServerDomain, sizeof(ramData->configInfo.newServerDomain));
            port = ramData->configInfo.newServerDomainPort;
        }
    }
    else // 使用通用域名
    {
        TspLogI("TSP:NET:Using common domain addr [%s]\n\n", ramData->configInfo.commonServerDomain);
        memcpy(addr, ramData->configInfo.commonServerDomain, sizeof(ramData->configInfo.commonServerDomain));
        port = ramData->configInfo.commonServerDomainPort;
    }

    TspLogI("TSP:NET:socket conect info: sinport:%d, IP:%s\n", port,addr);

    int sockfd,new_fd,errsv;/*socket句柄和接受到连接后的句柄 */
    struct sockaddr_in dest_addr; /*目标地址信息*/
    sockfd = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);/*建立socket*/
    if(sockfd < 0) 
    {
        TspLogI("TSP:NET:SystemSocketOpen failed\n\n");
        return TBOX_ERROR;
    }
    
    // 设置socket连接超时
    struct timeval timeout;
    timeout.tv_sec = 10;  // 设置10秒超时
    timeout.tv_usec = 0;
    if (setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, (char *)&timeout, sizeof(timeout)) < 0) 
    {
        TspLogE("TSP:NET:setsockopt SO_SNDTIMEO failed");
    }
    if (setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, (char *)&timeout, sizeof(timeout)) < 0) 
    {
        TspLogE("TSP:NET:setsockopt SO_RCVTIMEO failed");
    }
    
    dest_addr.sin_family=AF_INET;
    dest_addr.sin_port= htons(port);

    ret = GetIpByName(addr,&dest_addr.sin_addr.s_addr);
    if(TBOX_OK != ret)
    {
        TspLogE("TSP:NET:Get ip address by name failed, ret=%d.", ret);
        return TBOX_ERROR;
    }

    //dest_addr.sin_addr.s_addr=inet_addr(addr);
    bzero(&(dest_addr.sin_zero),8);

    ramData->statusInfo.connectFlag = 0;
    ramData->statusInfo.logInFlag = 0;
    //在此处增加对企标变量的reset
    GetQbLoginInfo()->connectFlag = 0;
    GetQbLoginInfo()->connectCount = 0;
    GetQbLoginInfo()->logInFlag = 0;
    TspLogI("QBHandleLogin:GetQbLoginInfo()->connectCount = 0");


    // 多次重试进行连接
    do
    {
        ret = connect(sockfd,(struct sockaddr*)&dest_addr,sizeof(struct sockaddr));
        if(ret < 0)
        {
			errsv = errno;
            TspLogE("TSP:NET:socket connect fail ret[%d]errsv[%d] ENETUNREACH[%d]ECONNREFUSED[%d]ETIMEDOUT[%d]",ret,errsv,ENETUNREACH,ECONNREFUSED,ETIMEDOUT);
        }
        else
        {
            TspLogE("TSP:NET:connect success\n");
            ramData->statusInfo.connectFlag = 1;
            ramData->socketInfo.socketFd = sockfd;
            TspCopyDomain(ramData);  //若使用newServerDomain连接成功，则将newServerDomain复制到commonServerDomain
            return TBOX_OK;
        }

        TspLogE("TSP:NET:socket retry count %d",i);
        if(i >= TSP_NET_RECONECT_COUNT_MAX)
        {
            TspLogE("TSP:NET:socket connect retry count reached limit %d\n\n", TSP_NET_RECONECT_COUNT_MAX);
            close(sockfd);
            sockfd = 0;
            return TBOX_ERROR;
        }
        
        i++;
        
        if(errsv == ETIMEDOUT) // 连接超时
        {
            msleep(500); 
        }
        else if(errsv == ENETUNREACH) // 网络不可达
        {
            if(i >= 3) // 如果连续3次网络不可达，则退出内部循环
            {
                TspLogE("TSP:NET:Network unreachable after 3 retries, exit internal loop");
                close(sockfd);
                sockfd = 0;
                return TBOX_ERROR;
            }
            msleep(300); 
        }
        else
        {
            msleep(100); 
        }
    }while(1);
    return TBOX_OK;
}


void TspCopyDomain(TspRamData_s *ramData)
{
    if(!ramData)
    {
        return ;
    }

    if(1 == ramData->configInfo.newServerActiveFlag)
    {
        // 复制通信服务器域名和端口
        memcpy(ramData->configInfo.commonServerDomain, ramData->configInfo.newServerDomain, sizeof(ramData->configInfo.newServerDomain));
        ramData->configInfo.commonServerDomainPort = ramData->configInfo.newServerDomainPort;

        SetStringProperty(c_commonServerDomain, ramData->configInfo.commonServerDomain);
        SetIntProperty(c_commonServerDomainLen, strlen(ramData->configInfo.commonServerDomain));
        SetIntProperty(c_commonServerDomainPort, ramData->configInfo.commonServerDomainPort);
        SetIntProperty(c_commonServerActiveFlag, 1);
    }
}
/*************************************************
函数名称: TspSocketClose
函数功能: 关闭socket
输入参数: ramData
输出参数: 无
函数返回类型值：TBOX_OK - 初始化成功，其他 - 失败
编写者: liuyuanqiang
编写日期：2020/12/25
*************************************************/
int TspSocketClose(TspRamData_s *ramData)
{
    ramData->statusInfo.connectFlag = 0;
    ramData->statusInfo.logInFlag = 0;
    ramData->statusInfo.loginFristCount = 0;
    ramData->statusInfo.beatHeartCount = 0;
    ramData->statusInfo.loginFristFlag = 0;

    //重新对企标登录标志清零
    GetQbLoginInfo()->connectCount = 0;
    GetQbLoginInfo()->logInFlag = 0;

    close(ramData->socketInfo.socketFd);//socket关闭

    TspSendGeneralDelAll();
    TspSendRtimePause();
    TspSendGeneralPause();
    GBSaveRtimeData();
    //BQCacheReset();

    LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO,  "Close tsp socket\n\n");
    return TBOX_OK;
}

/*************************************************
函数名称: TspSocketDestroy
函数功能: 永久关闭TSP连接
输入参数: 
输出参数: 无
函数返回类型值：
编写者: liuyuanqiang
编写日期：2021/3/9
*************************************************/
void TspSocketDestroy(void)
{
    g_tspRamData.statusInfo.carStartFlag = 0;
    g_tspRamData.systemSleep = TRUE;
    if(g_tspRamData.statusInfo.connectFlag)
    {
        TspSocketClose(&g_tspRamData);
        LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO,  "Tsp socket destroy\n\n");
    }
}

/*************************************************
函数名称: ApnRecoverDirectly
函数功能: 直接尝试恢复网络
输入参数: recoverState - 从0开始依次是切换飞行模式、重启APN、复位
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期 :2021/04/25
*************************************************/
#define AT_CMD_SET_FLY_MODE         "AT+CFUN=4"
#define AT_CMD_SET_FULL_FUNC        "AT+CFUN=1"
#define AT_CMD_REBOOT_MODULE        "AT+CFUN=1,1"
void TspApnRecoverDirectly(uint8_t recoverState)
{
    char atRes[64] = {0};
    uint8_t curState = recoverState % 3; //当前三种方法恢复网络

    LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "Try recover apn directlry, recoverState:%u\n\n", curState);
    switch(curState)
    {
        case 0:
            SendModuleGeneralCmd(AT_CMD_SET_FLY_MODE, atRes, sizeof(AT_CMD_SET_FLY_MODE));
            msleep(500); //切换后间隔0.5秒再切换全功能模式
            SendModuleGeneralCmd(AT_CMD_SET_FULL_FUNC, atRes, sizeof(AT_CMD_SET_FULL_FUNC));
            break;
        case 1://删除再创建
            //DelAManualApn(APN_PROFILE_MANUAL_1);
            //AddAManualApn(APN_PROFILE_MANUAL_1, APP_TECH_AUTO,PRIVATE_APN_NAME); //TODO:确定APN名字
            
            //DelAManualApn(APN_PROFILE_MANUAL_3);
            //AddAManualApn(APN_PROFILE_MANUAL_3, APP_TECH_AUTO, PRIVATE_APN_NAME);
            break;
        case 2:
        default:
            SendModuleGeneralCmd(AT_CMD_REBOOT_MODULE, atRes, sizeof(AT_CMD_REBOOT_MODULE));
            break;
    }
}
/*************************************************
函数名称: TspHandleTask
函数功能: socket处理线程主函数
输入参数: 未使用
输出参数: 无
函数返回类型值：无
编写者: liuyuanqiang
编写日期：2020/12/23
*************************************************/
static void* TspHandleThread(void *arg)
{
    int ret = TBOX_ERROR;
    char recv_buff[512] = {0};
    int recv_len =0, result, s;
    uint8_t recoverSta = 0;
    uint32_t connectTime = 0;
    uint32_t connExitCnt = 0;
    boolean apnConnectedStatus = FALSE;
    bool sleepflag = false;

    
    while(TRUE)
    {
        sleepflag = GetSleepFlag();
        //判断是否需要休眠，若休眠则不做其他逻辑处理
        if(1 == sleepflag && g_tspRamData.statusInfo.sleepDisagreeFlag == TSP_SLEEP_AGREE_FALG )
        {
            TspLogI("sleepFlag[%u]sleepDisagreeFlag[%u]",sleepflag,g_tspRamData.statusInfo.sleepDisagreeFlag);
            msleep(TASK_SLEEP_PERIOD);
            continue;
        }

        //判断车辆是否启动
        if(0 == g_tspRamData.statusInfo.carStartFlag) 
        {
            msleep(TSP_SLEEP_PERIOD);
            continue;
        }
        TspGetVehicleDescriptor();
        if(g_tspRamData.statusInfo.connectCount > TSP_NET_RECONECT_COUNT_MAX)
        {
            TspApnRecoverDirectly(recoverSta);
            recoverSta++;
            g_tspRamData.statusInfo.connectCount = 0;
        }

#ifndef TSP_APN
        apnConnectedStatus = FALSE;
        //ret = GetApnConnStatus(APN_PROFILE_MANUAL_1, &apnConnectedStatus);
        apnConnectedStatus = TRUE;
        if(apnConnectedStatus == FALSE)
        {
            TspLogI("TSP:SystemApnGetState failed,wait for network successful111, ret = %d\n\n", ret);
            g_tspRamData.statusInfo.connectCount++;
            msleep(g_tspRamData.statusInfo.connectCount * 2000);
            continue;
        }
#else
        apnConnectedStatus = FALSE;
        ret = GetApnConnStatus(APN_PROFILE_MANUAL_3, &apnConnectedStatus);
        if(apnConnectedStatus == FALSE)
        {
            ret = TspCreatePriApn();
            if(TBOX_OK != ret)
            {
                TspLogI("TSP:NET:TspCreatePriApn failed %d,wait for network successful222\n\n", ret);
                g_tspRamData.statusInfo.connectCount++;
                msleep(g_tspRamData.statusInfo.connectCount * 2000);
                continue;
            }
            LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "TSP:TspCreatePriApn successful\n\n");
            msleep(5000);
        }
#endif
        TspLogI("TSP:NET:Check The Socket Connect Status ......\n");
        if(1 == g_tspRamData.statusInfo.carStartFlag && 0 == g_tspRamData.statusInfo.connectFlag)
        {
            ret = TspSocketConnect(&g_tspRamData);
            if(TBOX_ERROR == ret)
            {
                LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "TSP:TspSocketConnect failed\n\n");
                g_tspRamData.statusInfo.connectCount++;
                msleep(g_tspRamData.statusInfo.connectCount * 2000);
                continue;
            }
            TspSendRtimeRun();
            TspSendGeneralRun();
            TspSendRtimeSocketSet(g_tspRamData.socketInfo.socketFd);
            TspSendGeneralSocketSet(g_tspRamData.socketInfo.socketFd);
            g_tspRamData.statusInfo.connectCount = 0;
            recoverSta = 0;

            TspLogI("TSP:NET:TspSocketConnect successful\n");
        }

        s = g_tspRamData.socketInfo.socketFd;

        

        connectTime = GetCurrentTimeSec();
        TspLogI("TSP:NET:while condition:[%d][%d][%d]", g_tspRamData.statusInfo.carStartFlag, g_tspRamData.statusInfo.connectFlag,g_tspRamData.systemSleep);

        int timeCount = 0;
        while(g_tspRamData.statusInfo.carStartFlag && g_tspRamData.statusInfo.connectFlag)
        {

            memset(recv_buff, 0, 512);

            recv_len = recv(s, recv_buff, sizeof(recv_buff), MSG_DONTWAIT);
            if(recv_len >0)
            {
                TspLogI("recv len[%d]",recv_len);
                recv_buff[recv_len] = '\0';
                char *pDest = malloc(recv_len * 2 + 1);
                if (NULL != pDest)
                {
                    SystemApiHexToStr(recv_buff, recv_len, pDest);
                    LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "Trans:FD_ISSET recv Data:%s\r", pDest);
                    free(pDest);
                }

                TspProtoHandle_s * tspProtoHandle = TspGetProtoclHandle();
                tspProtoHandle->msgHandle(recv_buff, recv_len);
            }
            else if(recv_len == 0)
            {
                TspLogI("TSP:tcp select error,so Close the Socket\n\n");
                TspSocketClose(&g_tspRamData);
                break;
            }
            else
            {
                int recverrno = errno;
                if(11 == recverrno)
                {
                    timeCount ++;
                    if((timeCount % (1000/PROCESS_CIRCLE_MS)) == 0)
                    {
                    //    TspLogD("rc < 0 timeCount[%d]",timeCount);
                    }
                }
                else
                {
                    TspLogI("recv rc[%d]<0 errno[%d]",recv_len,recverrno);
                    TspSocketClose(&g_tspRamData);
                    break;
                }
            }
            msleep(PROCESS_CIRCLE_MS);
        }
        TspSocketClose(&g_tspRamData);
    }
    TspLogI("TSP:**********End Tsp handle task**************\n\n");
}

/*************************************************
函数名称: TspStartHandleThread
函数功能: 创建socket处理主线程
输入参数: 无
输出参数: 无
函数返回类型值：TBOX_OK - 初始化成功，其他 - 失败
编写者: liuyuanqiang
编写日期：2020/12/23
*************************************************/
int TspStartHandleThread(pthread_t *returnTid)
{
    int err;
    pthread_t tid;
    pthread_attr_t attr;

    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    err = pthread_create(&tid, &attr, TspHandleThread, NULL);
    if (err != 0)
    {
        LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "Tsp TspHandleThread thread failed\n");
        return TBOX_ERROR;
    }
    else
    {
        *returnTid = tid;
        LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "Tsp TspHandleThread thread running\n");
    }
    return TBOX_OK;
}

/*************************************************
函数名称: PmWakeupCb
函数功能: 电源唤醒回调
输入参数: 无
输出参数: 无
函数返回类型值：TBOX_OK - 初始化成功，其他 - 失败
编写者: liuyuanqiang
编写日期：2020/12/23
*************************************************/
void PmWakeupCb(WakeupSource wakeupSource)
{
    //更新唤醒源
    g_tspRamData.statusInfo.sleepWakeUpSource = wakeupSource;
    //为避免冷启动的时候重复初始化
    if (g_tspRamData.statusInfo.sleepFlag)
    {
        SetSleepFlag(false);
        g_tspRamData.statusInfo.carStartFlag = 1;
        g_tspRamData.statusInfo.getVehicleDescriptorFlag = FALSE;
        // g_tspRamData.statusInfo.socketParamInitFinished  = false;
        // gb_connection.gbStatus.carStartFlag             = 1;
        SetIntProperty(FIRST_LINK_SYNC_TIME_FINISHED, 0);
        SetIntProperty(FIRST_LINK_LOGIN_STATUS_KEY, GetGBLoginStatus());
    }
    else
    {
        LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "task already start. no need to init again.");
    }
    LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "Get wake up source from systemctrl:%d.", wakeupSource);
}

/*************************************************
函数名称: TspInit
函数功能: TSP初始化服务
输入参数: 无
输出参数: 无
函数返回类型值：TBOX_OK - 初始化成功，其他 - 失败
编写者: liuyuanqiang
编写日期：2020/12/23
*************************************************/
int TspInit(void)
{
    int ret = TBOX_OK;
    int err = 0;

    CanApiInit();
    
    ret = TspParamInit();
    if(TBOX_OK != ret)
    {
        LogServiceBuffer(CLIENT_MODULE_TSP ,TBOX_LOG_INFO, "Tsp init failed, ret=%d\n\n", ret);
        return ret;
    }

    //创建3个线程用于完成各自任务
    TspStartHandleThread(&g_tspRamData.tspHandleThreadId); 
    TspStartSendThread(&g_tspRamData.tspSendThreadId);
    TspStartNotifyThread(&g_tspRamData.tspAutoNotifyThreadId);
//    TspStartMonitorFileThread(&g_tspRamData.tspfileMonitThreadId);
    
    //注册系统控制回调
    SysCtrlRegCallback_s sysCtrlCb;
    sysCtrlCb.pPMWakeup = PmWakeupCb;
    sysCtrlCb.pPMSleepEvent = PMSleepEventCb;
    sysCtrlCb.pTimeCalibrate = TimeCalibrateCb;
    RegistSysCtrlCallback(&sysCtrlCb);
    return ret;
}


void FullCanDumpFileNameCallBack(const char *fileName)
{
    TspLogD("Full can dump file callback, fileName: %s\n", fileName);
    GetCanFullDataToTsp(fileName);
}

void HandSignal(int signo)
{
    if (signo == SIGTERM) 
    {
        TspLogI("Received SIGTERM signal: System is rebooting");
        SetTotalOdometerToFlash();  
        SetSocToFlash();
        _exit(0);    
    }
}

 
int main()
{
//    LogServiceBuffer(CLIENT_MODULE_TSP,TBOX_LOG_INFO, "===TBOX TSP START=== V%d.%d.%d", ARM_MAIN_VERION, ARM_SUB_VERION, ARM_MODIFY_VERSION);
    LogServiceBuffer(CLIENT_MODULE_TSP,TBOX_LOG_INFO, "===TBOX TSP START=== V1.13.0");
    LogServiceBuffer(CLIENT_MODULE_TSP,TBOX_LOG_INFO, "*******************Compilation Time:%s %s**************",__DATE__, __TIME__);

    msleep(100*2);
    if (signal(SIGTERM, HandSignal) == SIG_ERR) 
    {
        TspLogE("Error: Unable to catch SIGTERM");
        return 1;
    }

    InitMmProperty();  //初始化属性模块接口

    //创建D-Bus连接
    int ret = DBusCreateConnection(DBUS_SERVICE_TSP_NAME);
    if(ret != 0)
    {
        TspLogE("create dbus connect fail, ret = %d\n", ret);
        return -1;
    }

    TspInit();


    StartAlarmThread();    
    parseXml(GB_XML_PATH,GB_DECODE_PATH);     
#if 1
    ret = RegFullCanCfgApi(QB_FULLCAN_PATH, PartialFullCanDataCallback,QbFaultCodeDataCallback, NULL);
    if (ret)
    {
        TspLogI("Request register Can RegFullCanCfgApi config failed, ret=%d.\n", ret);
    }
    else
    {
        TspLogI("Request register Can RegFullCanCfgApi config success\n");
    }
#endif   
    while(1)
    {
        g_tspRamData.statusInfo.currentTime = GetCurrentTimeSec();
        msleep(1000*1);
        DoUpgradeIfNeed();
        SetIntProperty(FIRST_LINK_LOGIN_STATUS_KEY, GetGBLoginStatus());
    }
    //释放D-Bus连接
    DBusDestroyConnection();
    return 0;
}



<?xml version="1.0" encoding="utf-8"?>
<ISOFT Name="ImmediateNotifyFullCan" Desc="Immediate Notify Full CAN Configuration">
    <!-- 最新数据通知周期，0表示不通知，单位：秒 -->
    <NotifyInterval>0</NotifyInterval>
    <!-- 最新数据查询后是否清除 -->
    <ClearAfterQuery>1</ClearAfterQuery>
    <!-- 全量CAN文件存储周期，单位：秒 -->
    <FileStorageInterval>10</FileStorageInterval>
    <!-- 全量CAN文件存储个数，不能超过 MAX_FULL_CAN_FILE_COUNT -->
    <TotalFileCount>0</TotalFileCount>
    <!-- 可能的最大 CAN 数量，当 SpecifiedCanIdCount 为 0 时有效，不能超过CAN_CACHE_MAX_SIZE(300) -->
    <MaxCanIdCount>150</MaxCanIdCount>
    <!-- 指定 CAN ID 数量，0 表示全量 -->
    <SpecifiedCanIdCount>4</SpecifiedCanIdCount>
    <!-- 指定 CAN ID 列表，当 SpecifiedCanIdCount 非 0 时有效 -->
    <SpecifiedCanIdList>
        <CanInfo>
            <CanId>0x1822d0f3</CanId>
            <CanCh>1</CanCh>
            <TriggerOnReceive>True</TriggerOnReceive>
        </CanInfo>
        <CanInfo>
            <CanId>0x181ed0f3</CanId>
            <CanCh>1</CanCh>
            <TriggerOnReceive>True</TriggerOnReceive>
        </CanInfo>
        <CanInfo>
            <CanId>0xc04a1a7</CanId>
            <CanCh>1</CanCh>
            <TriggerOnReceive>True</TriggerOnReceive>
        </CanInfo>
        <CanInfo>
            <CanId>0xc0ca1a7</CanId>
            <CanCh>1</CanCh>
            <TriggerOnReceive>True</TriggerOnReceive>
        </CanInfo>
    </SpecifiedCanIdList>
</ISOFT>

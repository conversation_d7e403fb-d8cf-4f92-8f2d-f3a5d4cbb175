/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-01-01 22:29:54
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-01-10 22:34:19
 * @FilePath: /tbox_platform-master/platform/tsp/common/SpecialDataProcess.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */


#ifndef _SPECIALDATAPROCESS_H_
#define _SPECIALDATAPROCESS_H_

#define OTA_TIME_UP_HOUR   4



#ifdef MBEDTLS_TLS
#define        TSP_NET_SERVER_DOMAIN              "***********"
#define        TSP_NET_SERVER_PORT                 8873
//************* 9001
#elif TSP_APN
#define        TSP_NET_SERVER_DOMAIN              "tsp.zznissan.com.cn"
#define        TSP_NET_SERVER_PORT                7122
//***************  10000    公共平台
#else
//#define        TSP_NET_SERVER_DOMAIN              "***********"
//#define        TSP_NET_SERVER_PORT                 8873

#if 1
#define        TSP_NET_SERVER_DOMAIN              "zccloud32960.crrczy.com"
#define        TSP_NET_SERVER_PORT                 9503
#else 
#define        TSP_NET_SERVER_DOMAIN              "vcloud.crrczy.com"
#define        TSP_NET_SERVER_PORT                 9014
#endif

#endif


#define     GB_DATA_MAX_LEN                 2048
#define     GB_VIN_LEN                      17


typedef enum {
    STANDARD_GB = 0,  // 国标
    STANDARD_ENTERPRISE // 企标
} StandardType;

typedef struct{
    StandardType  protoType;   //协议类型
    uint16_t      msgType ;    //报文类型
}StatusReport;

#endif



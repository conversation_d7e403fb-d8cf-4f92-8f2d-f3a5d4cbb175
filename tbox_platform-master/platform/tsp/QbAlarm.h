#ifndef _TSP_QBALARM__
#define _TSP_QBALARM__

#include <pthread.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <FullCanCollect/AscHandler/AscFileOperation.h>

#define MONITORED_CAN_COUNT 4
#define MAX_BATCH_ALARMS    50         // 一次最多批量报警数量
#define MODE_CAN_ID         0x181ED0F3 // 故障码监测模式帧的CAN ID


// 报警时间结构
#pragma pack(push, 1)
typedef struct 
{
    uint8_t year;
    uint8_t month;
    uint8_t day;
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
} AlarmTime;
#pragma pack(pop)

// 单个报警体结构 (链表节点)
#pragma pack(push, 1)
typedef struct AlarmInfo 
{
    uint8_t alarm_flag;     // 报警标示 (0:系统故障码, 1:阈值故障码)
    uint32_t alarm_code;    // 报警故障码
    uint8_t alarm_status;   // 报警状态 (0:报警结束, 1:报警开始)
    AlarmTime alarm_time;   // 报警时间
    struct AlarmInfo* next; // 链表下一个节点
} AlarmInfo;
#pragma pack(pop)

// 主结构定义 (报警信息链表) 根据上报格式而定义的类型
#pragma pack(push, 1)
typedef struct {
    int32_t longitude;       // 经度
    int32_t latitude;        // 纬度
    uint8_t alarm_count;     // 报警个数
    AlarmInfo* alarms_head;  // 链表头指针
} AlarmStatusMessage;
#pragma pack(pop)

// CAN帧结构
#pragma pack(push, 1)
typedef struct 
{
    uint32_t canId;
    uint8_t data[8];
    uint8_t data_len;
    uint32_t timestamp;
} CanFrame;
#pragma pack(pop)

// CAN帧包结构
#pragma pack(push, 1)
typedef struct CanFramePack 
{
    CanFrame* frames;
    uint16_t frame_count;
    struct CanFramePack* next;
} CanFramePack;
#pragma pack(pop)

// CAN帧包队列结构
#pragma pack(push, 1)
typedef struct {
    CanFramePack* head;
    CanFramePack* tail;
    pthread_mutex_t lock;
    pthread_cond_t cond;
} CanFramePackQueue;
#pragma pack(pop)



// 单个报警状态节点（链表形式管理）
#pragma pack(push, 1)
typedef struct AlarmStateNode 
{
    uint16_t state_code;          // 故障码（支持2字节，即最大0xFFFF）
    uint8_t is_active;            // 状态是否激活（0:未激活，1:激活）
    time_t last_report_time;      // 最近一次更新时间
    struct AlarmStateNode* next;  // 链表下一节点
} AlarmStateNode;
#pragma pack(pop)

// 单个CAN ID的报警监控结构
#pragma pack(push, 1)
typedef struct 
{
    uint32_t canId;
    AlarmStateNode* state_list;   // 链表头指针（动态管理任意多个状态）
} CanAlarmStatus;
#pragma pack(pop)


#pragma pack(push, 1)
typedef struct 
{
    AlarmInfo alarms[MAX_BATCH_ALARMS];
    uint8_t count;
} AlarmBatch;
#pragma pack(pop)

// 序列化后的结构（无指针）为最后发送数据做准备的数据结构
#pragma pack(push, 1)
typedef struct {
    uint8_t alarm_flag;
    uint32_t alarm_code;
    uint8_t alarm_status;
    AlarmTime alarm_time;
} SerializedAlarmInfo;

typedef struct {
    uint32_t longitude;
    uint32_t latitude;
    uint8_t alarm_count;
    SerializedAlarmInfo alarms[50]; // 无指针结构
} SerializedAlarmMessage;
#pragma pack(pop)

typedef enum {
    MODE_UNDEFINED, // 尚未确定模式
    MODE_24HOUR,    // 24小时监控模式
    MODE_NORMAL     // 普通模式
} SystemWorkMode;




void StartAlarmThread();
void EnqueueFramesToAlarmQueue(uint8_t *data, uint16_t len);
int QbProtoAlarmPack(SerializedAlarmMessage *AlarmMsg,uint8_t *outputbuf, uint32_t *inOutLen);
void QbFaultCodeDataCallback(uint8_t *data, uint16_t len);
#endif
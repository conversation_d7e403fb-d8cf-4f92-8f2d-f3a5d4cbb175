#include "QbAlarm.h"
#include "gb.h"
//#include "GBProtoHandle.h"
#include <FullCanCollect/AscHandler/AscFileOperation.h>
#include <errno.h>
#include "qb.h"
#include "TspMain.h"
#include "tbox_error.h"
#include "SystemApi.h"

void InitCanFramePackQueue(CanFramePackQueue* queue);
void* AlarmProcessThread(void* arg);
void EnqueueCanFramePack(CanFramePackQueue *queue, CanFramePack *pack);
CanFramePack* DequeueCanFramePackWithTimeout(CanFramePackQueue* queue, int timeout_sec);
AlarmStateNode* FindOrCreateStateNode(CanAlarmStatus* alarmStatus, uint16_t faultCode, time_t currentTime);
int UpdateAlarmStatus(CanFrame* frame, time_t currentTime, AlarmBatch* batch);
void ReportFault(uint32_t canId, uint32_t faultCode, uint8_t status, time_t currentTime);
void ReportBatchAlarms(AlarmBatch* batch) ;
void CheckAndClearAlarmTimeout(time_t currentTime);
void SerializeAlarmBatch(AlarmBatch* batch, SerializedAlarmMessage* outMsg);
int CheckAndExtractFaultCode(CanFrame* frame,uint16_t *faultCode);
void UpdateWorkModeFromFrame(const CanFrame* frame);
CanFramePack* ExtractMonitorCanToPack(const uint8_t* inputData, uint16_t inputLen);

static CANMessage *monitorCandHead = NULL;
CanFramePackQueue alarmQueue;
static SystemWorkMode currentMode = MODE_UNDEFINED;

// 待监控CAN ID数组
CanAlarmStatus monitoredCanIds[MONITORED_CAN_COUNT] = {
    {0x1822D0F3, NULL},
    {0x18EBA1A6, NULL},
    {0x0C04A1A7, NULL},
    {0x181ED0F3, NULL}, //用于判断类型
};

CanFramePackQueue alarmQueue; // 全局定义的

// 启动报警线程
void StartAlarmThread() 
{
    pthread_t threadId;

    InitCanFramePackQueue(&alarmQueue);
    pthread_create(&threadId, NULL, AlarmProcessThread, &alarmQueue);
}

// 企标报警处理线程
void* AlarmProcessThread(void* arg) 
{
    uint16_t i = 0;
    CanFramePackQueue* alarmQueue = (CanFramePackQueue*)arg;
    AlarmBatch batch = { .count = 0 };

    while (1) 
    {
        CanFramePack* pack = DequeueCanFramePackWithTimeout(alarmQueue, 1);
        time_t currentTime = time(NULL);

        if (pack) 
        {
            for ( i = 0; i < pack->frame_count; ++i) 
            {
                UpdateAlarmStatus(&pack->frames[i], pack->frames[i].timestamp, &batch);
            }
            free(pack->frames);  //此内存是在入队时申请，因此需要清除
            free(pack);

            // 批量统一上报报警信息
            ReportBatchAlarms(&batch);
        }

        CheckAndClearAlarmTimeout(currentTime);  // 单独处理超时消除报警
    }
    return NULL;
}



void CheckAndClearAlarmTimeout(time_t currentTime) 
{
    int i = 0;

    for (i = 0; i < MONITORED_CAN_COUNT; ++i) 
    {
        CanAlarmStatus* alarmStatus = &monitoredCanIds[i];
        AlarmStateNode** current = &alarmStatus->state_list;

        while (*current) 
        {
            AlarmStateNode* state = *current;
        //    TspLogD("monitor:Check if the FaultCode:%d has expired",state->state_code);
            if (state->is_active && difftime(currentTime, state->last_report_time) > 30) 
            {
                TspLogD("monitor:FaultCode: %d report time is %u",state->state_code,state->last_report_time);
                TspLogD("monitor:The Current Time is %u",currentTime);
                TspLogD("monitor:So Clear The FaultCode: %d",state->state_code);
                state->is_active = 0;
                // 超时，报警消除，上报
                ReportFault(alarmStatus->canId, state->state_code, 0, currentTime);
            }
            else
            {
        //        TspLogD("monitor:The FaultCode :%d State is Active ,So Do not Clear",state->state_code);
            }

            // 若状态不活跃，可释放此节点内存
            if (!state->is_active) 
            {
                *current = state->next;
                free(state);
            } 
            else 
            {
                current = &state->next;
            }
        }
    }
}

void ReportFault(uint32_t canId, uint32_t faultCode, uint8_t status, time_t currentTime) 
{
    StatusReport proto = {STANDARD_ENTERPRISE, ALERT_COMMAND_REPORT_REQUEST};
    SerializedAlarmMessage serializedMsg = {0};
    struct tm* t = localtime(&currentTime);

    serializedMsg.longitude = 0;
    serializedMsg.latitude = 0;
    serializedMsg.alarm_count = 1;

    serializedMsg.alarms[0].alarm_flag = 1;
    serializedMsg.alarms[0].alarm_code = faultCode;
    serializedMsg.alarms[0].alarm_status = status;

    serializedMsg.alarms[0].alarm_time.year = t->tm_year % 100;
    serializedMsg.alarms[0].alarm_time.month = t->tm_mon + 1;
    serializedMsg.alarms[0].alarm_time.day = t->tm_mday ;
    serializedMsg.alarms[0].alarm_time.hour = t->tm_hour ;
    serializedMsg.alarms[0].alarm_time.minute = t->tm_min ;
    serializedMsg.alarms[0].alarm_time.second = t->tm_sec ;

    TspReportData(
        (uint8_t*)&serializedMsg, 
        sizeof(int)*2 + 1 + serializedMsg.alarm_count * sizeof(SerializedAlarmInfo), 
        false, 
        proto
    );
}


/*
    (1)解析收到的报文,分析是否有故障码
    (2)若刚接手的报文有故障码,则去对应的Canid报警状态中查询是否记录过
*/
int UpdateAlarmStatus(CanFrame* frame, time_t currentTime, AlarmBatch* batch) 
{
    CanAlarmStatus *alarmStatus = NULL;
    int ret = TBOX_ERROR;
    uint16_t faultCode = 0;

    // 首先单独处理模式决定帧
    if(frame->canId == MODE_CAN_ID) 
    {
        UpdateWorkModeFromFrame(frame);
        return TBOX_ERROR; // 仅更新模式，不做其他处理
    }

    // 如果模式未确定，则不处理任何其他帧
    if (currentMode == MODE_UNDEFINED) 
    {
        TspLogD("monitor:current Mode is Undefined Mode");
        return TBOX_ERROR;
    }

    for (int i = 0; i < MONITORED_CAN_COUNT; ++i) 
    {
        if (monitoredCanIds[i].canId == frame->canId) 
        {
            alarmStatus = &monitoredCanIds[i];
            break;
        }
    }

    if (!alarmStatus) //未找到对应的canid
    {
        return TBOX_ERROR;
    }

    TspLogD("monitor: The frame canId:0x%x Will Extract Falut Code",frame->canId);

    ret = CheckAndExtractFaultCode(frame,&faultCode);

    if(ret == TBOX_OK) 
    {
        AlarmStateNode* state = FindOrCreateStateNode(alarmStatus, faultCode, currentTime);  //先查找此故障码是否有记录
        if (!state->is_active)  //发现没有上报过后，则放入到batch中，准备上报
        {
            state->is_active = 1;
            state->last_report_time = currentTime;

            // 不立即上报，而是缓存到batch里，稍后统一上报
            if (batch->count < MAX_BATCH_ALARMS) 
            {
                AlarmInfo* alarm = &batch->alarms[batch->count++];
                alarm->alarm_flag = 1;
                alarm->alarm_code = faultCode;
                alarm->alarm_status = 1; // 故障产生
                struct tm* t = localtime(&currentTime);
                alarm->alarm_time.year   = t->tm_year % 100;
                alarm->alarm_time.month  = t->tm_mon + 1;
                alarm->alarm_time.day    = t->tm_mday;
                alarm->alarm_time.hour   = t->tm_hour;
                alarm->alarm_time.minute = t->tm_min;
                alarm->alarm_time.second = t->tm_sec;
            }
        } 
        else 
        {
            TspLogD("monitor:Updata faultCode:%d Time:%d",faultCode,currentTime);
            state->last_report_time = currentTime;
            ret = TBOX_ERROR;
        }
    }

    return ret;
}


void UpdateWorkModeFromFrame(const CanFrame* frame) 
{
    if (!frame) 
    {
        return;
    }

    uint8_t bmsMode = frame->data[7] & 0x03;  // 提取低两位，bit 0~1 即为bit62~63

    switch (bmsMode) 
    {
        case 0b00:  // 行车状态
        case 0b01:  // 充电状态
            currentMode = MODE_NORMAL;
            TspLogD("monitor:========Set Monitor In Normal Mode========");
            break;
        case 0b10:  // 24h监控
            currentMode = MODE_24HOUR;
            TspLogD("monitor:========Set Monitor In 24 Hour Mode========");
            break;
        case 0b11:  // 不可信
        default:
            TspLogD("monitor:========Monitor Mode Unknown or Untrusted, Not Set========");
            break;
    }
}


int CheckAndExtractFaultCode(CanFrame* frame,uint16_t *faultCode)
{
    int ret = TBOX_OK;

    if (currentMode == MODE_24HOUR) 
    {
        switch (frame->canId) 
        {
            case 0x1822D0F3:
                if (frame->data[7] > 0) 
                {
                    *faultCode = frame->data[7] + 3000;
                    TspLogD("monitor:Canid[0x%x]The faultCode is %u",frame->canId,*faultCode);
                }
                else
                {
                    ret = TBOX_ERROR;
                }
                break;
            case 0x18EBA1A6:
                if (frame->data[0] > 0) 
                {
                    *faultCode = frame->data[0] + 3000;   
                    TspLogD("monitor:Canid[0x%x]The faultCode is %u",frame->canId,*faultCode);
                }
                else
                {
                    ret = TBOX_ERROR;
                }
                break;
            default :
                TspLogD("monitor:In 24hour Mode no have %x case",frame->canId);
                ret = TBOX_ERROR;
        }
    }
    else 
    {
        switch (frame->canId) 
        {
            case 0x0C04A1A7:
                *faultCode = (frame->data[7] << 8) | frame->data[6];
                if (*faultCode == 0) { ret = TBOX_ERROR; }
                TspLogD("monitor:Canid[0x%x]The faultCode is %u",frame->canId,*faultCode);
                break;
            default :
                TspLogD("monitor:In Normal Mode no have %x case",frame->canId);
                ret = TBOX_ERROR;
        }
    }
    return ret;
}

AlarmStateNode* FindOrCreateStateNode(CanAlarmStatus* alarmStatus, uint16_t faultCode, time_t currentTime) 
{
    AlarmStateNode **current = &alarmStatus->state_list;

    while (*current)  //循环查找已经记录的故障码
    {
        if ((*current)->state_code == faultCode)
        {
            return *current;
        }
        current = &(*current)->next;
    }

    // 没找到则创建新节点
    AlarmStateNode* newNode = malloc(sizeof(AlarmStateNode));
    newNode->state_code = faultCode;
    newNode->is_active = 0;
    newNode->last_report_time = currentTime;
    newNode->next = alarmStatus->state_list;
    alarmStatus->state_list = newNode;
    TspLogD("monitor:Great %d FaultCode Node",faultCode);
    return newNode;
}


void ReportBatchAlarms(AlarmBatch* batch)
{
    StatusReport proto = {STANDARD_ENTERPRISE,ALERT_COMMAND_REPORT_REQUEST }; // CAN_NETWORK_DATA_REPORT
    SerializedAlarmMessage serializedMsg;

    if (batch->count == 0)
    {   
    //    TspLogD("monitor:Batch Alarms count is zero");
        return;
    } 
    
    SerializeAlarmBatch(batch, &serializedMsg);
    SystemPrintHexData(CLIENT_MODULE_TSP, TBOX_LOG_INFO, 
                       (uint8_t*)&serializedMsg,  
                       sizeof(int)*2 + 1 + batch->count * sizeof(SerializedAlarmInfo),
                        "monitor:Send batch data:");
    
    TspReportData(
        (uint8_t*)&serializedMsg, 
        sizeof(int)*2 + 1 + batch->count * sizeof(SerializedAlarmInfo), 
        false, 
        proto
    );

    batch->count = 0; // 清空缓存
}

void SerializeAlarmBatch(AlarmBatch* batch, SerializedAlarmMessage* outMsg) 
{
    outMsg->longitude = 0;
    outMsg->latitude = 0;
    outMsg->alarm_count = batch->count;

    for (uint8_t i = 0; i < batch->count; i++)
    {
        outMsg->alarms[i].alarm_flag = batch->alarms[i].alarm_flag;
        outMsg->alarms[i].alarm_code = batch->alarms[i].alarm_code;
        outMsg->alarms[i].alarm_status = batch->alarms[i].alarm_status;
        outMsg->alarms[i].alarm_time.year = batch->alarms[i].alarm_time.year;
        outMsg->alarms[i].alarm_time.month = batch->alarms[i].alarm_time.month;
        outMsg->alarms[i].alarm_time.day = batch->alarms[i].alarm_time.day;
        outMsg->alarms[i].alarm_time.hour = batch->alarms[i].alarm_time.hour;
        outMsg->alarms[i].alarm_time.minute = batch->alarms[i].alarm_time.minute;
        outMsg->alarms[i].alarm_time.second = batch->alarms[i].alarm_time.second;
    }
}


// 初始化CAN帧包队列
void InitCanFramePackQueue(CanFramePackQueue* queue) 
{
    queue->head = queue->tail = NULL;
    pthread_mutex_init(&queue->lock, NULL);
    pthread_cond_init(&queue->cond, NULL);
}

pthread_mutex_t callCanErrorLock = PTHREAD_MUTEX_INITIALIZER;
void QbFaultCodeDataCallback(uint8_t *data, uint16_t len)
{
    TspLogD("===============monitor:FaultCodeDataCallback==========================");
    pthread_mutex_lock(&callCanErrorLock); 
    EnqueueFramesToAlarmQueue(data, len);  
    pthread_mutex_unlock(&callCanErrorLock);
}


// 封装CAN帧数据入队逻辑
void EnqueueFramesToAlarmQueue(uint8_t *data, uint16_t len)
{
    CanFramePack* pack = ExtractMonitorCanToPack(data, len);
    if (!pack) 
    {
        TspLogD("monitor:Not Found Monitor Can Data");
        return;
    }
    EnqueueCanFramePack(&alarmQueue, pack);
}

CanFramePack* ExtractMonitorCanToPack(const uint8_t* inputData, uint16_t inputLen)
{
	uint16_t i = 0;
	uint16_t count = 0;
	
    if (!inputData || inputLen == 0) return NULL;

    const CanMessage* canMsgs = (const CanMessage*)inputData;
    uint16_t totalCanidCount = inputLen / sizeof(CanMessage);

    CanFrame* frameBuf = malloc(totalCanidCount * sizeof(CanFrame));
    if (!frameBuf) return NULL;

    for (i = 0; i < totalCanidCount; i++) 
    {
        uint32_t cid = canMsgs[i].canId;
        if (cid == 0) continue;

        for (int j = 0; j < MONITORED_CAN_COUNT; j++) 
        {
            if (cid == monitoredCanIds[j].canId) 
            {
                TspLogD("The monitor Canid:0x%x",cid);
                SystemPrintHexData(CLIENT_MODULE_TSP, TBOX_LOG_DEBUG, canMsgs[i].data, 8, "Tsp:monitor data:");
                frameBuf[count].canId = cid;
                memcpy(frameBuf[count].data, canMsgs[i].data, 8);
                frameBuf[count].data_len = 8;
                frameBuf[count].timestamp = canMsgs[i].timestamp;
                TspLogD("The monitor Canid:0x%x,Record Time:%u",cid,frameBuf[count].timestamp);
                count++;
                break;
            }
        }
    }

    if (count == 0) 
    {
        free(frameBuf);
        return NULL;
    }

    CanFramePack* pack = malloc(sizeof(CanFramePack));
    if (!pack) 
    {
        free(frameBuf);
        return NULL;
    }

    pack->frames = frameBuf;
    pack->frame_count = count;
    pack->next = NULL;
    return pack;
}

// 入队CAN帧包
void EnqueueCanFramePack(CanFramePackQueue *queue, CanFramePack *pack) 
{
    pthread_mutex_lock(&queue->lock);
    if (queue->tail) 
    {
        queue->tail->next = pack;
        queue->tail = pack;                  //始终保证tail在最后一个节点，否则在入队时会发生问题(由于设计的为非循环链表)
    } 
    else 
    {
        queue->head = queue->tail = pack;   //保证head始终指向第一个节点
    }
    pthread_cond_signal(&queue->cond);
    pthread_mutex_unlock(&queue->lock);
}


// 出队CAN帧包
CanFramePack* DequeueCanFramePackWithTimeout(CanFramePackQueue* queue, int timeout_sec) 
{
    CanFramePack* pack = NULL;
    struct timespec ts;

    clock_gettime(CLOCK_REALTIME, &ts);
    ts.tv_sec += timeout_sec;

    pthread_mutex_lock(&queue->lock);
    while (queue->head == NULL) 
    {
        if (pthread_cond_timedwait(&queue->cond, &queue->lock, &ts) == ETIMEDOUT) 
        {
            pthread_mutex_unlock(&queue->lock);
            return NULL;
        }
    }

    pack = queue->head;               //从头部取数据
    queue->head = queue->head->next;
    if (queue->head == NULL) 
    {
        queue->tail = NULL;
    }

    pthread_mutex_unlock(&queue->lock);
    return pack;
}


//Can报警消息体组包
int QbProtoAlarmPack(SerializedAlarmMessage *AlarmMsg,uint8_t *outputbuf, uint32_t *inOutLen)
{
    int count = 0;
    uint8_t p[GB_DATA_NORMAL_LEN] = {0};
    TimeInfo timeInfo;
    GpsDataType gpsinfo= {0};

    if(!AlarmMsg || !outputbuf || !inOutLen)
    {
        TspLogE("monitor:Can Alarm Pack parm is invalid ... ");
        return TBOX_ERROR;
    }

    GetGpsInfo(&gpsinfo);
    AlarmMsg->longitude = gpsinfo.longitude* 1000000;
    AlarmMsg->latitude = gpsinfo.latitude* 1000000;

    memcpy(outputbuf, (uint8_t *)AlarmMsg, *inOutLen);
    return TBOX_OK;

}

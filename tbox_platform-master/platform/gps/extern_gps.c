#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/select.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <termios.h>
#include <unistd.h>
#include <pthread.h>
#include <time.h>

#include "iot_logc.h"
#include "extern_gps.h"


#define NMEA_CMD_DELAY_MS(x) usleep((x) * 1000)
#define NMEA_MAX_BUF_LEN     (512)
#define EXTERN_GSP_UART_BPS  (115200)
#define EXTERN_GSP_UART_DEV  "/dev/ttyS2"


static int int_fd = 0;  // 与解析线程通信的fd
static int ext_fd = 0;  // 与gps模块通信的fd
static int gps_output = 0; // 是否输出gps
static int gps_calibrate = 0; // 是否校准

// 线程管理相关变量
static pthread_t gps_thread_id = 0;
static int thread_running = 0;
static pthread_mutex_t thread_mutex = PTHREAD_MUTEX_INITIALIZER;

// 函数声明
static void gps_recv_thread(void *arg);


int uart_init(const char *dev, int speed)
{
	int speed_arr[] = {B115200, B57600, B38400, B19200, B9600, B4800, B2400, B1200, B300};
	int name_arr[] = {115200, 57600, 38400, 19200, 9600, 4800, 2400, 1200, 300};
	struct termios options;
	int size = sizeof(speed_arr) / sizeof(int);
	int fd, i;
	
	
	if (NULL == dev || 115200 < speed)
	{
		LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "uart(%s) init args(%d) invalid", dev ? : "NULL", speed);
		return -1;
	}
	
	if (0 > (fd = open(dev, O_RDWR | O_NOCTTY)))
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "open uart(%s) failed", dev);
        return -1;
    }
	tcgetattr(fd, &options);
	
	options.c_cflag |= (CLOCAL | CREAD);
	options.c_cflag &= ~CSIZE;	
	options.c_cflag &= ~CRTSCTS;	
	options.c_cflag |= CS8;		
	options.c_cflag &= ~CSTOPB;
	options.c_cflag &= ~PARENB;     
	options.c_iflag |= IGNPAR;
	options.c_iflag &= ~(BRKINT | ICRNL | INPCK | ISTRIP | IXON);
	
	options.c_oflag &= 0;  
	options.c_lflag = 0; 
	
	options.c_cc[VTIME] = 1;  
	options.c_cc[VMIN]  = 10; 
	
	for (i = 0; i < size; i++)
	{
		if (speed == name_arr[i])
		{
			cfsetispeed(&options, speed_arr[i]);
			cfsetospeed(&options, speed_arr[i]);
			break;
		}
	}
	if (i >= size)
	{
		LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "set uart(%s) speed(%d) failed", dev, speed);
		return -1;
	}
	
	/**3. 设置新属性，TCSANOW：所有改变立即生效*/
	tcflush(fd, TCIFLUSH);
	tcsetattr(fd, TCSANOW, &options);
	
	return fd;
}

void uart_destroy(int fd)
{
	if (0 < fd) close(fd);
}

int uart_send(int fd, char *data, int datalen)
{
	int len = write(fd, data, datalen);//实际写入的长度;
	
	if (len == datalen)
	{
		return len;
	}
	else
	{
		tcflush(fd, TCOFLUSH);//TCOFLUSH刷新写入的数据但不传送
		return -1;
	}
	
	return 0;
}

int uart_recv(int fd, char *data, int datalen, int msec)
{
	struct timeval tv_timeout;
	fd_set fd_read;
	int ret = 0;
	
	FD_ZERO(&fd_read);
	FD_SET(fd, &fd_read);
	
	if (msec)
	{
		tv_timeout.tv_sec = msec / 1000;
		tv_timeout.tv_usec = (msec % 1000) * 1000;
	}
	
	ret = select(fd + 1, &fd_read, NULL, NULL, msec ? &tv_timeout : NULL);
	if (0 < ret)  // 接收数据通知
	{
		if (FD_ISSET(fd, &fd_read)) 
		{
			return read(fd, data, datalen);
		}
		else
		{
			return 0;
		}
	}
	else if (0 == ret)  // 接收数据超时
	{
		return 0;
	}
	
	return -1;	// 接收数据出错
}

// 彻底清空串口缓冲区
static void clear_uart_buffer(int fd)
{
	char temp_buf[256];
	int bytes_read;

	if (fd <= 0) return;

	// 清空内核缓冲区
	tcflush(fd, TCIOFLUSH);

	// 读取并丢弃剩余数据，确保应用层缓冲区也清空
	while ((bytes_read = uart_recv(fd, temp_buf, sizeof(temp_buf), 10)) > 0)
	{
		// 丢弃读取的数据
		LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_DEBUG, "Discarded %d bytes of old GPS data", bytes_read);
	}
}

// 停止GPS接收线程
static int stop_gps_thread(void)
{
	int ret = 0;

	pthread_mutex_lock(&thread_mutex);
	if (thread_running && gps_thread_id != 0)
	{
		thread_running = 0;
		pthread_mutex_unlock(&thread_mutex);

		// 等待线程结束
		ret = pthread_join(gps_thread_id, NULL);
		if (ret != 0)
		{
			LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_WARN, "GPS thread join failed: %d", ret);
		}

		pthread_mutex_lock(&thread_mutex);
		gps_thread_id = 0;
		thread_running = 0;
	}
	pthread_mutex_unlock(&thread_mutex);

	return ret;
}

// 启动GPS接收线程
static int start_gps_thread(void)
{
	int ret = 0;

	pthread_mutex_lock(&thread_mutex);
	if (!thread_running)
	{
		thread_running = 1;
		ret = pthread_create(&gps_thread_id, NULL, (void*)gps_recv_thread, NULL);
		if (ret != 0)
		{
			thread_running = 0;
			gps_thread_id = 0;
			LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Failed to create GPS thread: %d", ret);
		}
		else
		{
			LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "GPS thread created successfully");
		}
	}
	pthread_mutex_unlock(&thread_mutex);

	return ret;
}

int extern_gps_start(void)
{
	int ret = 0;

	LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "Starting external GPS");

	// 先停止现有线程（如果在运行）
	stop_gps_thread();

	// 清空串口缓冲区，确保没有旧数据
	clear_uart_buffer(ext_fd);

	// 启动新的接收线程
	ret = start_gps_thread();
	if (ret == 0)
	{
		gps_output = 1;
		LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "External GPS started successfully");
	}
	else
	{
		LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Failed to start external GPS");
	}

	return ret;
}

int extern_gps_stop(void)
{
	LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "Stopping external GPS");

	// 停止数据输出
	gps_output = 0;

	// 停止并销毁线程
	stop_gps_thread();

	// 彻底清空GPS串口缓冲区
	clear_uart_buffer(ext_fd);

	LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "External GPS stopped successfully");
	return 0;
}


int extern_gps_calibrate(void)
{
    const char *cmd[] = {"$PINVCRES,0*1A", "$PINVCSTR,14*3E", "$CCSIR,3,1*4A"};
    int ret = -1;


    ret = uart_send(ext_fd, (char *)cmd[0], strlen(cmd[0]));
    if (0 > ret)
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Uart send (%s) %d", cmd[0], ret);
		ret = -1;
        goto error;
    }
    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "Uart send (%s) %d", cmd[0], ret);
    NMEA_CMD_DELAY_MS(500);

    ret = uart_send(ext_fd, (char *)cmd[1], strlen(cmd[1]));
    if (0 > ret)
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Uart send (%s) %d", cmd[1], ret);
		ret = -1;
        goto error;
    }
    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "Uart send (%s) %d", cmd[1], ret);

    NMEA_CMD_DELAY_MS(500);

    ret = uart_send(ext_fd, (char *)cmd[2], strlen(cmd[2]));
    if (0 > ret)
    {
		ret = -1;
    }
    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "Uart send (%s) %d", cmd[2], ret);

error:
    return ret;
}


static void gps_recv_thread(void *arg)
{
	char buf[NMEA_MAX_BUF_LEN] = {0};
	int rx_len = 0;
	int tx_len = 0;
	int running = 1;

	LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "GPS receive thread started");

	while (running)
	{
		// 检查线程是否应该退出
		pthread_mutex_lock(&thread_mutex);
		running = thread_running;
		pthread_mutex_unlock(&thread_mutex);

		if (!running) break;

		// 使用带超时的接收，以便能及时响应退出信号
		rx_len = uart_recv(ext_fd, buf, sizeof(buf), 100); // 100ms超时
		if (0 < rx_len && gps_output)
		{
			buf[rx_len] = 0;
			tx_len = uart_send(int_fd, buf, rx_len);
			if (tx_len != rx_len)
			{
				 LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Uart send failed rx(%d) tx(%d)", rx_len, tx_len);
			}
		}
		else if (rx_len < 0)
		{
			// 接收出错，短暂休眠后继续
			usleep(10000); // 10ms
		}
		// rx_len == 0 表示超时，继续循环检查退出条件
	}

	LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "GPS receive thread exited");
}


int extern_gps_init(int fd)
{
	int uart_fd = -1;
	int ret = 0;

	LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "Initializing external GPS");

	uart_fd = uart_init(EXTERN_GSP_UART_DEV, EXTERN_GSP_UART_BPS);
	if (0 > uart_fd)
	{
		LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "ExGPS Uart init failed");
		return -1;
	}

	// 初始化线程管理变量
	pthread_mutex_lock(&thread_mutex);
	thread_running = 0;
	gps_thread_id = 0;
	pthread_mutex_unlock(&thread_mutex);

	int_fd = fd;
	ext_fd = uart_fd;
	gps_output = 0;

	LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "External GPS initialized successfully");
	return ret;
}


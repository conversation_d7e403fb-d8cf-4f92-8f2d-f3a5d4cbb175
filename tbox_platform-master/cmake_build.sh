#!/usr/bin/env bash
set -x
# 定义关键变量
PROJECT_NAME="CRRC"
CUR_DIR=$(dirname "$(readlink -f "$0")")
BUILD_DIR="${CUR_DIR}/build"
TARGET="all" # 默认构建目标
ENV_FILE="${HOME}/.${PROJECT_NAME}-T106-ENV"
DEFAULT_SDK_PATH="${HOME}/${PROJECT_NAME}-T106-SDK"
SDK_PATH=""
SDK_FILE=""
COMMAND="build"
VERSION="1.0.0"
KERNEL_BUILD_LOCK="${HOME}/.${PROJECT_NAME}_KERNEL_LOCK"
ROOTFS_BUILD_LOCK="${HOME}/.${PROJECT_NAME}_ROOTFS_LOCK"
UBOOT_BUILD_LOCK="${HOME}/.${PROJECT_NAME}_UBOOT_LOCK"
LOCK_FILES=()
firstbuild=${firstbuild:-0}
trapflag=${trapflag:-0}
release_flag=${release_flag:-0}
debug_mode_flag=${debug_mode_flag:-0}
environment_mode=${environment_mode:-"PRD"}
fota_system_flag=${fota_system_flag:-0}
FOTA_TOOLS_DIR="${CUR_DIR}/tools/Fota"
rebuild_flag=${rebuild_flag:-0}
MCU_APP_DIR="${CUR_DIR}/../mcu_code"
MCU_BOOT_DIR="${CUR_DIR}/../mcu_boot"
MCU_APP_BIN_FILE="${MCU_APP_DIR}/build/mcu_app.bin"
MCU_APP_HEX_FILE="${MCU_APP_DIR}/build/mcu_app.hex"
MCU_BOOT_HEX_FILE="${MCU_BOOT_DIR}/build/mcu_boot.hex"
MCU_SECURE_HEX_FILE="mcu_boot_app.hex"

# 定义日志颜色
COLOR_RESET="\033[0m"
COLOR_INFO="\033[32m"  # 绿色
COLOR_WARN="\033[33m"  # 黄色
COLOR_ERROR="\033[31m" # 红色

# 函数：设置SDK相关路径
init_sdk_path() {
    # esdk
    ESDK_DIR="${SDK_PATH}/esdk"
    SDK_ENV_PATH="${ESDK_DIR}/environment-setup-armv7a-zxic-linux-gnueabi"
    OEM_ROOT_DIR="${ESDK_DIR}/layers/meta-zxic-custom/recipes-core/images/files/zx297520v3/vehicle_dc_ref/fs/normal/oem"
    SDK_CONFIG_FILE="${ESDK_DIR}/layers/meta-zxic-custom/conf/distro/vehicle_dc_ref.conf"
    IMAGE_BB_FILE="${ESDK_DIR}/layers/meta-zxic-custom/recipes-core/images/zxic-image.bb"
    KERNEL_FILE_DIR="${ESDK_DIR}/workspace/sources/linux-zxic"
    MKIMGSIG_FILE_DIR="${ESDK_DIR}/workspace/sources/mkimgsig"
    REFPOLICY_MLS_FILE_DIR="${ESDK_DIR}/workspace/sources/refpolicy-mls"
    # do_package_tool
    PACKAGE_TOOL_ROOT="${SDK_PATH}/do_package_tool"
    SIGN_TOOL_DIR="${PACKAGE_TOOL_ROOT}/tools"
    BIN_PACKAGE_DIR="${PACKAGE_TOOL_ROOT}/allbins/zx297520v3/prj_vehicle/scripts_linux"
    ALL_BIN_DEST_M42="${PACKAGE_TOOL_ROOT}/allbins/zx297520v3/prj_vehicle/allbins_dc_ref"
    ALL_BIN_DEST_M22="${PACKAGE_TOOL_ROOT}/allbins/zx297520v3/prj_vehicle/allbins_dc_ref_M22"
    # sources
    SDK_SOURCES="${SDK_PATH}/sdk_sources"
    UBOOT_SOURCE_DIR="${SDK_SOURCES}/Uboot"
    UBOOT_BUILD_DIR="${UBOOT_SOURCE_DIR}/boot/prj/zx297520v3/vehicle_dc_ref/build"
    UBOOT_BIN_SOURCE_ROOT="${UBOOT_SOURCE_DIR}/boot/prj/zx297520v3/vehicle_dc_ref/bin"
    UBOOT_ROM_DIR="${UBOOT_SOURCE_DIR}/boot/prj/zx297520v3/bootrom"
    # image build bins
    ALL_BIN_SOURCE="${SDK_PATH}/allbins/zx297520v3/vehicle_dc_ref/bins"
}

# 函数：编译过程日志
log() {
    local level="$1"          # 日志级别
    local message="$2"        # 日志信息
    local newline="${3:-yes}" # 是否换行，默认换行

    # 获取当前时间（格式：YYYY-MM-DD HH:MM:SS）
    local timestamp
    timestamp=$(date "+%Y-%m-%d %H:%M:%S")

    # 日志格式：[时间戳] [日志级别] 日志内容
    case "$level" in
    INFO)
        if [[ "$newline" == "no" ]]; then
            echo -ne "${COLOR_INFO}[$timestamp] [INFO] ${message}${COLOR_RESET}"
        else
            echo -e "${COLOR_INFO}[$timestamp] [INFO] ${message}${COLOR_RESET}"
        fi
        ;;
    WARN)
        if [[ "$newline" == "no" ]]; then
            echo -ne "${COLOR_WARN}[$timestamp] [WARN] ${message}${COLOR_RESET}"
        else
            echo -e "${COLOR_WARN}[$timestamp] [WARN] ${message}${COLOR_RESET}"
        fi
        ;;
    ERROR)
        if [[ "$newline" == "no" ]]; then
            echo -ne "${COLOR_ERROR}[$timestamp] [ERROR] ${message}${COLOR_RESET}"
        else
            echo -e "${COLOR_ERROR}[$timestamp] [ERROR] ${message}${COLOR_RESET}"
        fi
        ;;
    *)
        if [[ "$newline" == "no" ]]; then
            echo -ne "[$timestamp] [UNKNOWN] ${message}"
        else
            echo -e "[$timestamp] [UNKNOWN] ${message}"
        fi
        ;;
    esac
}

# 函数：获取选项
read_opt() {
    local level="$1"
    local message="$2"
    local default="$3"
    local variable_name="$4"

    log "$level" "$message" "no"

    read -r input

    if [[ -z "$input" ]]; then
        input="$default"
    fi

    # 使用 indirect reference 替代 eval
    printf -v "$variable_name" '%s' "$input"
}

# 定义要检查的程序列表
required_programs=(
    gawk wget git diffstat unzip texinfo gcc-multilib build-essential chrpath socat cpio
    python3 python3-pip python3-pexpect xz-utils debianutils iputils-ping python3-git python3-jinja2
    libegl1-mesa libsdl1.2-dev pylint3 xterm python3-subunit mesa-common-dev python pv make gcovr ninja
)

# 用于存储未安装或安装失败的程序
not_installed=()

# 检查并安装YOCTO环境
check_yocto_env() {
    log INFO "开始检查YOCTO环境..."
    for program in "${required_programs[@]}"; do
        # 检查程序/包是否已安装
        if ! dpkg -l | awk '{print $2}' | grep -w "$program" >/dev/null; then
            log WARN "$program 未安装，尝试安装..."
            # 尝试安装程序
            if sudo apt-get install -y "$program"; then
                log INFO "$program 安装成功"
            else
                log ERROR "$program 安装失败"
                not_installed+=("$program")
            fi
        else
            log INFO "$program 已安装"
        fi
    done

    # 输出未安装或安装失败的程序
    if [ ${#not_installed[@]} -gt 0 ]; then
        log ERROR "以下程序安装失败或未安装："
        for program in "${not_installed[@]}"; do
            echo "$program"
        done
        exit 1
    else
        log INFO "所有程序已成功安装！"
    fi
}

# 函数：出错时清除锁
cleanup_locks() {
    for lock_file in "${LOCK_FILES[@]}"; do
        [ -e "${lock_file}" ] && rm -f "${lock_file}"
    done
}

# 函数：编译锁
take_lock() {
    local file="$1"
    if [ -f "$file" ]; then
        log WARN "有人正在编译中，请等待"
        while [ -e "$file" ]; do
            echo -n "."
            sleep 1
        done
    fi
    log INFO "Create lock ${file} success"
    touch "$file"
    LOCK_FILES+=("$file") # 记录创建的锁文件
    if [ "$trapflag" -eq 0 ]; then
        trap 'cleanup_locks' EXIT
        trapflag=1
    fi
}

# 函数：释放编译锁
release_lock() {
    local file="$1"
    rm -f "$file"
    if [ "$trapflag" -eq 1 ]; then
        trapflag=0
    fi
    log INFO "Release lock ${file} success"
}

# 函数：更新esdk定制化
do_esdk_patch_update() {
    log INFO "开始更新 esdk patchs 到: ${ESDK_DIR}"

    log INFO "更新 layers..."
    cp "${CUR_DIR}/os_customized/layers" "${ESDK_DIR}/" -rf

    log INFO "更新 prebuilt..."
    cp "${CUR_DIR}/os_customized/prebuilt" "${ESDK_DIR}/" -rf

    log INFO "成功更新 esdk patchs."
}

# 函数：更新kernel定制化
do_kernel_patch_update() {
    log INFO "开始更新 kernel patchs..."
    cp "${CUR_DIR}/os_customized/workspace" "${ESDK_DIR}/" -rf
    firstbuild=0
    log INFO "成功更新 kernel patchs."
}

# 函数：编译Uboot，不能source esdk工具链
build_uboot() {
    if [ -n "$CC" ]; then
        log ERROR "Uboot 不能使用 esdk 编译链, 请重新打开终端."
        return 1
    fi
    take_lock "$UBOOT_BUILD_LOCK"
    log INFO "开始编译 Uboot..."
    cp "${CUR_DIR}/os_customized/Uboot" "${SDK_SOURCES}" -rf
    cd "${UBOOT_BUILD_DIR}" || exit 1
    make allclean all
    log INFO "执行合并 bootrom..."
    cd "${UBOOT_ROM_DIR}" || exit 1
    ./merge_boorom.sh
    # 备份Uboot生成文件
    log INFO "保存 Uboot 编译文件到: ${ALL_BIN_SOURCE}"
    cd "${UBOOT_BIN_SOURCE_ROOT}" || exit 1
    cp -f tboot.bin tloader.bin uboot.bin dl_off/zloader.bin "${ALL_BIN_SOURCE}"
    release_lock "$UBOOT_BUILD_LOCK"
    log INFO "成功编译 Uboot."
}

# 函数：编译Kernel
build_kernel() {
    log INFO "开始编译 Kernel..."
    cd "${ESDK_DIR}" || exit 1
    take_lock "$KERNEL_BUILD_LOCK"
    if [ "$firstbuild" -eq 0 ]; then
        read_opt WARN "是否更改过 Kernel Patchs? Y/N (default:N) :" "N" update_opt
        if [[ "${update_opt}" == "Y" || "${update_opt}" == "y" ]]; then
            do_kernel_patch_update
        fi
    fi
    log INFO "执行编译 linux-zxic..."
    if [ ! -d "${KERNEL_FILE_DIR}" ]; then
        devtool modify linux-zxic
    fi
    devtool build linux-zxic

    log INFO "执行签名 mkimgsig..."
    if [ ! -d "${MKIMGSIG_FILE_DIR}" ]; then
        devtool modify mkimgsig
    fi
    devtool build mkimgsig

    release_lock "$KERNEL_BUILD_LOCK"
    log INFO "成功编译 Kernel."
}

# 函数：重新编译Kernel
rebuild_kernel() {
    log INFO "开始重新编译 Kernel..."
    cd "${ESDK_DIR}"
    if [ -d "${KERNEL_FILE_DIR}" ]; then
        log INFO "重置 linux-zxic"
        devtool reset linux-zxic
        rm -rf "${KERNEL_FILE_DIR}"
    else
        log WARN "无需重置 linux-zxic"
    fi
    if [ -d "${MKIMGSIG_FILE_DIR}" ]; then
        log INFO "重置 mkimgsig"
        devtool reset mkimgsig
        rm -rf "${MKIMGSIG_FILE_DIR}"
    else
        log WARN "无需重置 mkimgsig"
    fi
    firstbuild=1
    do_esdk_patch_update
    build_kernel
    do_kernel_patch_update
    firstbuild=1
    build_kernel
}

# 函数：编译Rootfs
build_rootfs() {
    cd "${ESDK_DIR}" || exit 1
    log INFO "开始编译 Rootfs..."
    take_lock "$ROOTFS_BUILD_LOCK"

    if [ ${release_flag} -eq 0 ] && [ ${debug_mode_flag} -eq 0 ]; then
        read_opt WARN "是否更改过 esdk Patchs? Y/N (default:N) :" "N" update_opt
        if [[ "${update_opt}" == "Y" || "${update_opt}" == "y" ]]; then
            do_esdk_patch_update
        fi
    else
        do_esdk_patch_update
    fi

    if [ ${debug_mode_flag} -eq 1 ]; then
        sed -i 's/MOBILETEK_ADB_LOGIN = "YES"/MOBILETEK_ADB_LOGIN = "NO"/' "${SDK_CONFIG_FILE}"
        sed -i 's/TBOX_DEBUG_FLAG="NO"/TBOX_DEBUG_FLAG="YES"/' "${IMAGE_BB_FILE}"
    fi
    if [ -d ${REFPOLICY_MLS_FILE_DIR} ]; then
        log INFO "Reset and delete refpolicy-mls"
        devtool reset refpolicy-mls
        rm ${REFPOLICY_MLS_FILE_DIR} -rf
    fi
    log INFO "执行命令 modify refpolicy-mls..."
    devtool modify refpolicy-mls
    log INFO "执行编译 build-image..."
    devtool build-image
    release_lock "$ROOTFS_BUILD_LOCK"
    log INFO "成功编译 Rootfs."
}

# 函数：编译MCU
build_mcu() {
    local build_dir=$1
    local build_type="Debug"

    if ! command -v arm-none-eabi-gcc >/dev/null 2>&1; then
        log ERROR "找不到交叉编译器 arm-none-eabi-gcc，请检查是否已安装并在 PATH 中！"
        exit 1
    fi

    if [ "${release_flag}" -eq 1 ]; then
        build_type="Release"
    fi

    cd "${build_dir}" || {
        log ERROR "Directory ${build_dir} not found!"
        exit 1
    }
    rm -rf build || {
        log ERROR "Failed to remove build directory"
        exit 1
    }
    mkdir -p build && cd build || {
        log ERROR "Failed to create/enter build directory"
        exit 1
    }

    log INFO "Configuring CMake for ${build_type} build..."
    cmake -DCMAKE_BUILD_TYPE="${build_type}" \
        -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
        -DCMAKE_TOOLCHAIN_FILE="../cmake/gcc.cmake" \
        -DARM_CPU="cortex-m33" \
        -G Ninja .. || {
        log ERROR "CMake configuration failed"
        exit 1
    }

    log INFO "Building project..."
    cmake --build . || {
        log ERROR "Build failed"
        exit 1
    }
}

# 函数：保存fota相关bin文件
collect_fota_bins() {
    local backup_dir="${BUILD_DIR}/fota"
    local vehicle_dc_ref_dir="${SDK_PATH}/allbins/zx297520v3"
    local M42bins_dir="${backup_dir}/M42bins"
    local M22bins_dir="${backup_dir}/M22bins"

    # Remove old backup directory if it exists
    if [ -d "${backup_dir}" ]; then
        rm -rf "${backup_dir}"
    fi
    mkdir -p "${backup_dir}"

    # Create necessary directories
    mkdir -p "${M42bins_dir}" "${M22bins_dir}"

    # Collect vehicle_dc_ref
    cd "${vehicle_dc_ref_dir}" || exit
    log INFO "Start Collect vehicle_dc_ref"
    tar -cf - vehicle_dc_ref/ | pv -s "$(du -sb vehicle_dc_ref | awk '{print $1}')" | gzip >vehicle_dc_ref.tar.gz
    mv vehicle_dc_ref.tar.gz "${backup_dir}"

    # Collect fota M42bins and M22bins
    log INFO "Start Collect fota M42bins"
    cd "${ALL_BIN_DEST_M42}" || exit
    cp -f uboot.bin ap_caprootfs.img cap_oem.img ap_imagefs.img ap_rootfs.img "${M42bins_dir}"
    log INFO "Finished Collect fota bins in ${M42bins_dir}"

    log INFO "Start Collect fota M22bins"
    cd "${ALL_BIN_DEST_M22}" || exit
    cp -f uboot.bin ap_caprootfs.img cap_oem.img ap_imagefs.img ap_rootfs.img "${M22bins_dir}"
    log INFO "Finished Collect fota bins in ${M42bins_dir}"
}

# 函数：生成T-Box的FOTA升级包
create_fota_package() {
    local backup_dir="${BUILD_DIR}/fota"
    local M42bins_dir="${backup_dir}/M42bins"
    local M22bins_dir="${backup_dir}/M22bins"
    local isM42binsEmpty=$(find "${M42bins_dir}" -mindepth 1 -maxdepth 1 | wc -l)
    local isM22binsEmpty=$(find "${M22bins_dir}" -mindepth 1 -maxdepth 1 | wc -l)
    local M42upgrade_dir="${backup_dir}/M42upgrade/upgrade"
    local M22upgrade_dir="${backup_dir}/M22upgrade/upgrade"
    local fota_config_M42="${BUILD_DIR}/fota/fota_config_M42.ini"
    local fota_config_M22="${BUILD_DIR}/fota/fota_config_M22.ini"

    if [ "${isM42binsEmpty}" -eq 0 ] || [ "${isM22binsEmpty}" -eq 0 ]; then
        log ERROR "M42bins_dir or M22bins_dir is empty."
        log ERROR "You Can Run: ./$(basename "$0") -p -t image"
        exit 1
    fi

    # Create FOTA Files
    log INFO "Create T106 FOTA Files."
    mkdir -p "${M42upgrade_dir}" "${M22upgrade_dir}"

    cd "${FOTA_TOOLS_DIR}" || exit
    if [ "${fota_system_flag}" -eq 1 ]; then
        log INFO "Selected all system file fota configs."
        cp -f "${FOTA_TOOLS_DIR}/config/7520V3SC_vehicle_dc_ov_all.ini" "${fota_config_M42}"
        cp -f "${FOTA_TOOLS_DIR}/config/7520V3SC_vehicle_dc_ov_all.ini" "${fota_config_M22}"
    else
        log INFO "Selected only oem file fota configs."
        cp -f "${FOTA_TOOLS_DIR}/config/7520V3SC_vehicle_dc_ov_oem.ini" "${fota_config_M42}"
        cp -f "${FOTA_TOOLS_DIR}/config/7520V3SC_vehicle_dc_ov_oem.ini" "${fota_config_M22}"
    fi

    # Update fota config files
    sed -i "s|{fota_dir}|${M42bins_dir}|g" "${fota_config_M42}"
    sed -i "s|{fota_dir}|${M22bins_dir}|g" "${fota_config_M22}"

    log INFO "Create FOTA Files Out."
    ./diff_all.sh "${fota_config_M42}" "${backup_dir}/M42out" || exit 1
    ./diff_all.sh "${fota_config_M22}" "${backup_dir}/M22out" || exit 1

    log INFO "Append SHA256 to upgrade.package."
    ./sha256append.sh "${backup_dir}/M42out/up/upgrade.package" "${M42upgrade_dir}/upgrade.package"
    ./sha256append.sh "${backup_dir}/M22out/up/upgrade.package" "${M22upgrade_dir}/upgrade.package"

    log INFO "Create upgrade config files."
    cp -f "${FOTA_TOOLS_DIR}/iot.ini" "${M42upgrade_dir}"
    cp -f "${FOTA_TOOLS_DIR}/iot.ini" "${M22upgrade_dir}"

    log INFO "Check And Create MCU App upgrade files."
    if [ -f "${MCU_APP_BIN_FILE}" ]; then
        ./sha256append.sh "${MCU_APP_BIN_FILE}" "${M42upgrade_dir}/UserApp_src.bin"
        ./sha256append.sh "${MCU_APP_BIN_FILE}" "${M22upgrade_dir}/UserApp_src.bin"
        sed -i 's/MCUEnable = 0;/MCUEnable = 1;/g' "${M42upgrade_dir}/iot.ini"
        sed -i 's/MCUEnable = 0;/MCUEnable = 1;/g' "${M22upgrade_dir}/iot.ini"
    fi

    log INFO "Create M42 sysupgrade.zip or upgrade.zip."
    cd "${M42upgrade_dir}" || exit
    if [ "${fota_system_flag}" -eq 1 ]; then
        rm -f "${M42upgrade_dir}/../sysupgrade.zip"
        zip -r "${M42upgrade_dir}/../sysupgrade.zip" .
    else
        rm -f "${M42upgrade_dir}/../upgrade.zip"
        zip -r "${M42upgrade_dir}/../upgrade.zip" .
    fi
    log INFO "Finished Create M42 sysupgrade.zip or upgrade.zip."

    log INFO "Create M22 sysupgrade.zip or upgrade.zip."
    cd "${M22upgrade_dir}" || exit
    if [ "${fota_system_flag}" -eq 1 ]; then
        rm -f "${M22upgrade_dir}/../sysupgrade.zip"
        zip -r "${M22upgrade_dir}/../sysupgrade.zip" .
    else
        rm -f "${M22upgrade_dir}/../upgrade.zip"
        zip -r "${M22upgrade_dir}/../upgrade.zip" .
    fi
    log INFO "Finished Create M22 sysupgrade.zip or upgrade.zip."

    log INFO "Create MCU Secure Hex File."
    if [ -f "${MCU_APP_HEX_FILE}" ] && [ -f "${MCU_BOOT_HEX_FILE}" ]; then
        log INFO "Merge Mcu_App.hex and Mcu_Boot.hex to ${MCU_SECURE_HEX_FILE}."
        srec_cat "${MCU_APP_HEX_FILE}" -Intel "${MCU_BOOT_HEX_FILE}" -Intel -o "${backup_dir}/${MCU_SECURE_HEX_FILE}" -Intel
    fi

    log INFO "Clear Midware Files."
    rm -rf "${backup_dir}/M42out" "${backup_dir}/M22out" "${fota_config_M42}" "${fota_config_M22}" "${M42upgrade_dir}" "${M22upgrade_dir}"

    log INFO "Create FOTA Files Success."
}

# 函数：打包固件
package_image() {
    log INFO "开始打包合并固件 image..."
    # 签名工具检查
    if [ ! -d "${SIGN_TOOL_DIR}" ]; then
        cp "${SDK_PATH}/upstream/tools" "${PACKAGE_TOOL_ROOT}/" -r
    fi
    # 编译产物
    cp -f "${ALL_BIN_SOURCE}/"* "${ALL_BIN_DEST_M42}"
    cp -f "${ALL_BIN_SOURCE}/"* "${ALL_BIN_DEST_M22}"
    # 编译工具更新-key、ap侧补丁、重签名脚本
    cp "${CUR_DIR}/os_customized/do_package_tool" "${SDK_PATH}/" -r
    cd "${BIN_PACKAGE_DIR}" || exit 1
    if [ -d "${BUILD_DIR}/image" ]; then
        rm -rf "${BUILD_DIR}/image"
    fi
    mkdir -p "${BUILD_DIR}/image"
    bash merge.sh
    cp "${BIN_PACKAGE_DIR}/7520V3SC_VEC_DC_REF.bin" "${BUILD_DIR}/image/M42_IMAGE.bin"
    if [ -e "${BUILD_DIR}/image/M42_IMAGE.bin" ]; then
        log INFO "打包合并 image M42_IMAGE.bin 成功, 保存位置: ${BUILD_DIR}/image."
    else
        log ERROR "打包合并 image M42_IMAGE.bin 失败."
    fi
    bash merge_M22.sh
    cp "${BIN_PACKAGE_DIR}/7520V3SC_VEC_DC_REF_M22.bin" "${BUILD_DIR}/image/M22_IMAGE.bin"
    if [ -e "${BUILD_DIR}/image/M22_IMAGE.bin" ]; then
        log INFO "打包合并 image M22_IMAGE.bin 成功, 保存位置: ${BUILD_DIR}/image."
    else
        log ERROR "打包合并 image M22_IMAGE.bin 失败."
    fi

    if [ ${release_flag} -eq 0 ] && [ ${debug_mode_flag} -eq 0 ]; then
        read_opt INFO "do you want to collect fota bins file. Y/N? (default:N) :" "N" save_opt
        if [[ "${save_opt}" == "Y" || "${save_opt}" == "y" ]]; then
            collect_fota_bins
        fi
    else
        collect_fota_bins
    fi
}

# 函数：打包T-Box
package_tbox() {
    log INFO "打包T-Box..."
    local package_name="tbox_v${VERSION}.tar.gz"
    local package_dir="$BUILD_DIR/tbox"
    if [ -f "${package_name}" ]; then
        rm -rf "${package_name}"
    fi
    find "${package_dir}/" -type d -exec chmod 755 {} \;
    find "${package_dir}/bin" -type f -exec chmod 755 {} \;
    find "${package_dir}/etc_ro" -type f -exec chmod 644 {} \;
    find "${package_dir}/lib" -type f -exec chmod 755 {} \;
    find "${package_dir}/sbin" -type f -exec chmod 755 {} \;
    find "${package_dir}/test" -type f -exec chmod 755 {} \;
    find "${package_dir}/www" -type f -exec chmod 644 {} \;
    find "${package_dir}/www" -type f -name *.cgi -exec chmod 755 {} \;
    # todo 暂时不替换，使用默认的DEV环境
    # sed -i "s/ENVIRONMENT=\"DEV\"/ENVIRONMENT=\"${environment_mode}\"/" "${package_dir}/sbin/tbox_firewall.sh"
    cd "${package_dir}"
    tar -cf - * | pv -s $(du -sb "${package_dir}/" | awk '{print $1}') | gzip >"${BUILD_DIR}/${package_name}"
    log INFO "打包T-Box 成功: ${BUILD_DIR}/${package_name}"
    # 计算MD5值并打印
    md5sum=$(md5sum "${BUILD_DIR}/${package_name}" | awk '{print $1}')
    log INFO "文件MD5校验值: ${md5sum}"

    if [ ${release_flag} -eq 1 ] || [ ${debug_mode_flag} -eq 1 ]; then
        if [ -d "${OEM_ROOT_DIR}/tbox" ]; then
            rm -rf "${OEM_ROOT_DIR}/tbox/"*
        else
            mkdir -p "${OEM_ROOT_DIR}/tbox"
        fi
        cp -arfp "${package_dir}/"* "${OEM_ROOT_DIR}/tbox/"
    fi
}

# 函数：安装SDK
install_sdk() {
    log INFO "开始安装 T106_SDK..."
    local sdk_file
    sdk_file=$(readlink -f "${SDK_FILE}")
    if [ ! -d "${SDK_SOURCES}" ]; then
        mkdir -p "${SDK_SOURCES}"
        log INFO "解压 SDK_FILE: ${SDK_FILE}..."
        if [[ "$sdk_file" == *.tar.gz ]]; then
            gunzip -c "$sdk_file" | tar -xvf - -C "${SDK_SOURCES}/"
        elif [[ "$sdk_file" == *.tar ]]; then
            pv "$sdk_file" | tar -xvf - -C "${SDK_SOURCES}/"
        else
            log ERROR "不支持的文件格式: ${sdk_file}"
            exit 1
        fi
    else
        read_opt WARN "已经存在 T106_SDK , 是否重置? Y/N (default:Y): " "Y" reset_opt
        if [[ "${reset_opt}" == "Y" || "${reset_opt}" == "y" ]]; then
            log WARN "重置 SDK_SOURCES: ${SDK_SOURCES}..."
            rm -rf "${SDK_SOURCES:?}/"*
            if [[ "$sdk_file" == *.tar.gz ]]; then
                gunzip -c "$sdk_file" | tar -xvf - -C "${SDK_SOURCES}/"
            elif [[ "$sdk_file" == *.tar ]]; then
                pv "$sdk_file" | tar -xvf - -C "${SDK_SOURCES}/"
            else
                log ERROR "不支持的文件格式: ${sdk_file}"
                exit 1
            fi
        fi
    fi

    log INFO "开始安装 Uboot..."
    if [ ! -d "${UBOOT_SOURCE_DIR}" ]; then
        log INFO "解压 Uboot.tar.gz: ${UBOOT_SOURCE_DIR}..."
        pv "${SDK_SOURCES}/Uboot.tar.gz" | tar -zx -C "${SDK_SOURCES}/"
    else
        read_opt WARN "已经存在 Uboot , 是否重置? Y/N (default:Y): " "Y" reset_opt
        if [[ "${reset_opt}" == "Y" || "${reset_opt}" == "y" ]]; then
            log WARN "重置 Uboot: ${UBOOT_SOURCE_DIR}..."
            rm -rf "${UBOOT_SOURCE_DIR}"
            pv "${SDK_SOURCES}/Uboot.tar.gz" | tar -zx -C "${SDK_SOURCES}/"
        fi
    fi

    log INFO "开始安装 esdk..."
    cd "${SDK_SOURCES}" || exit 1
    sed -i "s/mkdir \${dest_dir_relative}/mkdir -p \${dest_dir_relative}/g" "mk_esdk.sh"
    if [ ! -d "${ESDK_DIR}" ]; then
        log INFO "解压 esdk 到: ${ESDK_DIR}..."
        # shellcheck disable=SC1090
        source "mk_esdk.sh" "$SDK_PATH"
    else
        read_opt WARN "已经存在 esdk , 是否重置? Y/N (default:Y): " "Y" reset_opt
        if [[ "${reset_opt}" == "Y" || "${reset_opt}" == "y" ]]; then
            log WARN "重置 esdk: ${ESDK_DIR}..."
            rm -rf "${ESDK_DIR}"
            # shellcheck disable=SC1090
            source "mk_esdk.sh" "$SDK_PATH"
        fi
    fi

    log INFO "开始安装 do_package_tool..."
    if [ ! -d "${PACKAGE_TOOL_ROOT}" ]; then
        log INFO "解压 do_package_tool.tar.gz 到: ${PACKAGE_TOOL_ROOT}..."
        pv "${SDK_SOURCES}/do_package_tool.tar.gz" | tar -zx -C "${SDK_PATH}/"
    else
        read_opt WARN "已经存在 do_package_tool, 是否重置? Y/N (default:Y): " "Y" reset_opt
        if [[ "${reset_opt}" == "Y" || "${reset_opt}" == "y" ]]; then
            log WARN "重置 do_package_tool: ${PACKAGE_TOOL_ROOT}..."
            rm -rf "${PACKAGE_TOOL_ROOT}"
            pv "${SDK_SOURCES}/do_package_tool.tar.gz" | tar -zx -C "${SDK_PATH}/"
        fi
    fi

    log INFO "开始安装 sign_tool..."
    if [ ! -d "${SIGN_TOOL_DIR}" ]; then
        cp "${SDK_PATH}/upstream/tools" "${PACKAGE_TOOL_ROOT}/" -r
    fi

    read_opt INFO "安装已完成, 是否进行第一次编译? Y/N (default:Y): " "Y" build_opt
    if [[ "${build_opt}" == "Y" || "${build_opt}" == "y" ]]; then
        cd "${ESDK_DIR}" || exit 1
        log INFO "开始第一次编译..."
        DISTRO=vehicle_dc_ref MACHINE=zx297520v3
        activate_environment
        devtool reset -a
        rm -rf "${ESDK_DIR}/workspace/sources/"*
        firstbuild=1
        # esdk_patch里有部分kernel编译配置，需要先更新进去
        do_esdk_patch_update
        # 第一次编译，会将kernel移至workspace目录进行
        build_kernel
        # kernel定制化都是放到workspace里的，所以更新后要再次编译kernel
        do_kernel_patch_update
        firstbuild=1
        build_kernel
    fi
    log INFO "成功安装 T106_SDK 到: ${SDK_PATH}"
    exit 0
}

# 函数：显示帮助信息
show_help() {
    cat <<EOF
Usage: $(basename "$0") [command] [options]
Commands:
  -b, --build             执行构建任务 (默认)
                          可选参数: -t, --target TARGET 指定构建 CMake 目标 / SDK目标
  -c, --clean             删除构建目录
  -h, --help              显示帮助信息
  -i, --install           安装 T106_SDK
                          必选参数: -f, --file SDK_FILE
                          可选参数: -D, --Dir SDK_PATH 指定安装路径
  -l, --list              列出可构建的 CMake 目标
  -r, --release           构建tbox程序、合并到oem分区、重编rootfs、打包image
                          可选参数：-s, --system 生成FOTA升级包含系统部分
  -d, --debug             构建tbox程序、合并到oem分区、重编rootfs、打包image、带debug标志、程序在可写分区
                          可选参数：-s, --system 生成FOTA升级包含系统部分
  -p, --package           打包程序(默认tbox)
                          可选参数: -v, --version 软件版本; -t, --target 打包类型(默认tbox)
Options:
  -D, --Dir SDK_PATH      指定 SDK 安装路径 (默认: ${HOME}${DEFAULT_SDK_PATH})
  -f, --file SDK_FILE     指定安装的 SDK 文件 (安装时必选)
  -v, --version           指定打包软件版本
  -t, --target TARGET     指定构建 CMake 目标 或 SDK 目标 或 打包目标
                          对于构建:
                          Cmake:
                            all       构建所有目标 (默认)
                            daemon_iot_log 构建 daemon_iot_log
                            test      运行所有测试 (需要先构建所有目标)
                            coverage  运行所有测试并生成覆盖率报告
                          SDK:
                            kernel    构建 Kernel
                            rootfs    构建 Rootfs
                            uboot     构建 Uboot
                          Package:
                            tbox      打包 tbox
                            image     打包 image
Examples:
  $(basename "$0") -b
  $(basename "$0") -c
  $(basename "$0") -t daemon_iot_log
  $(basename "$0") --install --file path/to/sdk.tar.gz --Dir /desired/sdk/path
  $(basename "$0") --package --target image -v 1.0.0

EOF
}

# 函数：列出可构建的 CMake 目标
list_targets() {
    log INFO "列出可构建的 CMake 目标..."
    cd "$BUILD_DIR" || exit 1
    cmake --build . --target help || {
        log ERROR "列出 CMake 目标失败."
        exit 1
    }
    cd - >/dev/null || exit 1
}

# 函数：创建 build 目录
create_build_dir() {
    if [ ! -d "$BUILD_DIR" ]; then
        log INFO "创建 build 目录..."
        mkdir -p "$BUILD_DIR"
    else
        log INFO "build 目录已存在。"
    fi
}

# 函数：读取 SDK_PATH
# SDK_PATH 为空且 .env 文件为空：使用默认的并写入 .env 文件。
# SDK_PATH 为空但 .env 文件不为空：使用 .env 文件中的 SDK_PATH。
# SDK_PATH 不为空但 .env 文件为空：将 SDK_PATH 更新到 .env 文件。
# SDK_PATH 不为空且 .env 文件不为空：更新 .env 文件中的 SDK_PATH。
read_sdk_path() {
    log INFO "读取 SDK_PATH..."
    local sdk_path_read=""

    if [ ! -f "$ENV_FILE" ]; then
        # .env 文件不存在，查看是否配置 SDK_PATH
        if [ -z "$SDK_PATH" ]; then
            # SDK_PATH 为空且 .env 也为空
            echo "SDK_PATH=$DEFAULT_SDK_PATH" >>"$ENV_FILE"
            log INFO "无 .env 文件且未指定 SDK_PATH, 写入默认的 SDK_PATH: $DEFAULT_SDK_PATH。"
            SDK_PATH="$DEFAULT_SDK_PATH"
        else
            # SDK_PATH 不为空，更新到 .env 文件
            echo "SDK_PATH=$SDK_PATH" >"$ENV_FILE"
            log INFO "无 .env 文件, 已设置 SDK_PATH: $SDK_PATH 到 .env 文件"
        fi
    else
        # 从 .env 文件中读取 SDK_PATH，并去除空格
        sdk_path_read=$(grep "^SDK_PATH=" "$ENV_FILE" | cut -d '=' -f 2 | tr -d '[:space:]')
        log INFO "读取到 SDK_PATH: $sdk_path_read"

        if [ -z "$sdk_path_read" ]; then
            # .env 文件中没有 SDK_PATH
            if [ -z "$SDK_PATH" ]; then
                # SDK_PATH 为空且 .env 也为空
                echo "SDK_PATH=$DEFAULT_SDK_PATH" >>"$ENV_FILE"
                log INFO "未在 .env 文件中找到 SDK_PATH, 写入默认的 SDK_PATH: $DEFAULT_SDK_PATH。"
                SDK_PATH="$DEFAULT_SDK_PATH"
            else
                # SDK_PATH 不为空，更新到 .env 文件
                echo "SDK_PATH=$SDK_PATH" >"$ENV_FILE"
                log INFO "已将 SDK_PATH: $SDK_PATH 保存到 .env 文件"
            fi
        else
            # .env 文件中有 SDK_PATH
            if [ -z "$SDK_PATH" ]; then
                # SDK_PATH 为空，使用 .env 中的值
                SDK_PATH="$sdk_path_read"
                log INFO "使用 .env 文件中的 SDK_PATH: $SDK_PATH"
            else
                # SDK_PATH 不为空，更新 .env 文件
                echo "SDK_PATH=$SDK_PATH" >"$ENV_FILE"
                log INFO "已将 SDK_PATH: $SDK_PATH 更新到 .env 文件"
            fi
        fi
    fi
}

# 函数：激活环境变量
activate_environment() {
    log INFO "激活环境变量..."

    if [ ! -f "$SDK_ENV_PATH" ]; then
        log ERROR "SDK 环境文件不存在: $SDK_ENV_PATH"
        exit 1
    fi
    # shellcheck disable=SC1090
    source "$SDK_ENV_PATH" || {
        log ERROR "无法 source $SDK_ENV_PATH"
        exit 1
    }

    log INFO "环境变量已激活。"
}

# 函数：运行实际的 CMake 配置
run_cmake_configure() {
    log INFO "运行实际的 CMake 配置..."

    cd "$BUILD_DIR" || exit 1

    # 自动判断是否安装了 ninja，如果有则使用 Ninja 生成器
    if command -v ninja >/dev/null 2>&1; then
        log INFO "检测到 Ninja，使用 Ninja 生成器进行 CMake 配置..."
        cmake -G Ninja .. || {
            log ERROR "实际 CMake 配置失败."
            exit 1
        }
    else
        log INFO "未检测到 Ninja，使用默认生成器进行 CMake 配置..."
        cmake .. || {
            log ERROR "实际 CMake 配置失败."
            exit 1
        }
    fi

    cd - >/dev/null || exit 1

    log INFO "CMake 配置完成。"
}

# 函数：构建项目
build_project() {
    log INFO "开始构建项目..."

    cd "$BUILD_DIR" || exit 1
    # Ninja 自动处理并行化，因此不需要手动指定 -j 14
    if command -v ninja >/dev/null 2>&1; then
        log INFO "使用 Ninja 进行项目构建..."
        cmake --build . --target "$TARGET" || {
            log ERROR "编译失败."
            exit 1
        }
    else
        log INFO "使用默认生成器进行项目构建..."
        cmake --build . --target "$TARGET" -- -j 14 || {
            log ERROR "编译失败."
            exit 1
        }
    fi

    cd - >/dev/null || exit 1

    log INFO "项目构建完成。"
}

# 函数：清除构建缓存
clean_build_cache() {
    log INFO "清除构建缓存..."

    rm -rf "$BUILD_DIR" "${MCU_APP_DIR}/build" "${MCU_BOOT_DIR}/build"

    log INFO "构建缓存已清除。"
}

# 解析参数
parse_args() {
    # 解析命令行参数
    ARGS=$(getopt -o bcdFhilprsD:E:f:t:v: --long build,clean,debug,FOTA,help,install,list,package,rebuild,release,system,Dir:,ENV:,file:,target:,version: -- "$@")

    # 如果 getopt 解析失败，退出脚本
    if [ $? -ne 0 ]; then
        show_help
        log ERROR "参数/选项错误, 请检查。"
        exit 1
    fi

    # 重新设置命令行参数
    eval set -- "$ARGS"

    # 解析参数
    while true; do
        case "$1" in
        -s | --system)
            fota_system_flag=1
            shift
            ;;
        -b | --build)
            COMMAND="build"
            shift
            ;;
        -c | --clean)
            clean_build_cache
            exit 0
            ;;
        -d | --debug)
            debug_mode_flag=1
            release_flag=0
            shift
            ;;
        -h | --help)
            show_help
            exit 0
            ;;
        -i | --install)
            COMMAND="install"
            shift
            ;;
        -l | --list)
            list_targets
            exit 0
            ;;
        -p | --package)
            COMMAND="package"
            shift
            ;;
        -E | --ENV)
            environment_mode="$2"
            shift 2
            ;;
        -r | --release)
            release_flag=1
            debug_mode_flag=0
            shift
            ;;
        --rebuild)
            rebuild_flag=1
            shift
            ;;
        -v | --version)
            VERSION="$2"
            shift 2
            ;;
        -D | --Dir)
            SDK_PATH="$2"
            shift 2
            ;;
        -f | --file)
            SDK_FILE="$2"
            shift 2
            ;;
        -F | --FOTA)
            COMMAND="package"
            TARGET="fota"
            shift 1
            ;;
        -t | --target)
            TARGET="$2"
            shift 2
            ;;
        --)
            shift
            break
            ;;
        *)
            log WARN "未知选项: $1"
            show_help
            exit 1
            ;;
        esac
    done
}

buildhandler() {
    read_sdk_path
    init_sdk_path

    # uboot 不使用工具链
    if [ "$TARGET" != "uboot" ]; then
        activate_environment
    fi

    case "$TARGET" in
    kernel)
        if [ ${rebuild_flag} -eq 1 ]; then
            rebuild_kernel
        else
            build_kernel
        fi
        ;;
    rootfs)
        build_rootfs
        ;;
    uboot)
        build_uboot
        ;;
    mcu_app)
        build_mcu "${MCU_APP_DIR}"
        ;;
    mcu_boot)
        build_mcu "${MCU_BOOT_DIR}"
        ;;
    *)
        create_build_dir
        run_cmake_configure
        build_project
        if [ ${release_flag} -eq 1 ] || [ ${debug_mode_flag} -eq 1 ]; then
            package_tbox
            build_rootfs
            package_image
        else
            package_tbox
        fi
        ;;
    esac
}

installhandler() {
    # 检查有没有设置-f|--file
    log INFO "开始安装 SDK: $SDK_FILE"
    if [ -z "$SDK_FILE" ]; then
        log ERROR "安装失败, 必须指定SDK文件"
        exit 1
    fi
    check_yocto_env
    read_sdk_path
    init_sdk_path
    install_sdk
}

packagehandler() {
    local package="$TARGET"
    if [ "$TARGET" = "all" ]; then
        package="tbox"
    fi
    log INFO "开始打包 $package"
    case "$package" in
    image)
        read_sdk_path
        init_sdk_path
        package_image
        ;;
    tbox)
        package_tbox
        ;;
    fota)
        create_fota_package
        ;;
    *)
        log ERROR "无效类型: $package"
        ;;
    esac
}

commandhandler() {
    case "$COMMAND" in
    build)
        buildhandler
        ;;
    install)
        installhandler
        ;;
    package)
        packagehandler
        ;;
    *)
        log ERROR "无效命令: $COMMAND"
        ;;
    esac
}

main() {
    parse_args "$@"
    set -e          # 当任何命令返回非零状态时，脚本会立即退出
    set -o pipefail # 当管道中的任何命令失败时，整个管道返回失败

    commandhandler
}

main "$@"

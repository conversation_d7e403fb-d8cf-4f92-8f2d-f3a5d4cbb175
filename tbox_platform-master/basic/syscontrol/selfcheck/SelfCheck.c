#include <unistd.h>
#include <stdlib.h>
#include <sys/file.h>
#include <net/if.h>
#include <string.h>
#include <stdio.h>
#include <sys/statvfs.h>

#include "SelfCheck.h"
#include "PowerManager.h"
#include "SystemStatus.h"
#include "SystemApi.h"
#include "iot_rilc.h"
#include "iot_logc.h"
#include "iot_ipcc.h"
#include "iot_dbus.h"
#include "iot_gpsc.h"
#include "tbox_error.h"
#include "property_api.h"

/*******全局变量***********/
static SelfCheckInfo_s g_selfCheckInfo = {0};


int ProcessModuleRssi(uint8_t moduleRssi) {
    int signalStrength = 0;

    // 如果 moduleRssi 为 99，表示没有信号
    if (moduleRssi == 99) {
        signalStrength = 0;
    }
        // 根据 moduleRssi 值分段
    else if (moduleRssi > 25) {
        signalStrength = 5;
    }
    else if (moduleRssi > 19) {
        signalStrength = 4;
    }
    else if (moduleRssi > 13) {
        signalStrength = 3;
    }
    else if (moduleRssi > 6) {
        signalStrength = 2;
    }
    else if (moduleRssi > 0) {
        signalStrength = 1;
    }
    return signalStrength;
}

/*************************************************
函数名称: GpsModulePositionSelfCheck
函数功能: 检查GPS定位状态信息
输入参数: gpsInfo
输出参数: 无
函数返回类型值：0:正常 1:故障
编写者: zxl
编写日期 :2020/01/06
*************************************************/
static int GpsModulePositionSelfCheck(GpsInfo *gpsInfo)
{

    if(DTC_DEVICE_NORMAL_STATUS == g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].status)
    {
        if((gpsInfo->lat != 0) || (gpsInfo->lon != 0))
        {
            g_selfCheckInfo.deviceStatus.gpsFixStatus = GNSS_MODULE_FIX;
        }
        else
        {
            g_selfCheckInfo.deviceStatus.gpsFixStatus = GNSS_MODULE_NOT_FIX;
        }

        g_selfCheckInfo.deviceStatus.gpsStarCount = gpsInfo->satinfo.bdinview + gpsInfo->satinfo.gpsinview + gpsInfo->satinfo.gainview + gpsInfo->satinfo.glinview;
        g_selfCheckInfo.deviceStatus.gpsDirectionH = ((uint16_t)gpsInfo->direction)>>8;
        g_selfCheckInfo.deviceStatus.gpsDirectionL = ((uint16_t)gpsInfo->direction)&0xFF;
        g_selfCheckInfo.deviceStatus.gnssSignal = gpsInfo->rssi;

        if (0 == gpsInfo->supportType)
        {
            g_selfCheckInfo.deviceStatus.gpsStarCount = (uint8_t)gpsInfo->satinfo.inuse;
        }

        if (0 == gpsInfo->fix)
        {
            g_selfCheckInfo.deviceStatus.gpsFixStatus = GNSS_MODULE_NOT_FIX;
        }
        else
        {
            g_selfCheckInfo.deviceStatus.gpsFixStatus = GNSS_MODULE_FIX;
            if ('E' == gpsInfo->ew)
            {
                g_selfCheckInfo.deviceStatus.longitude = (uint32_t)(gpsInfo->lon * (double)1000000);
            }
            else if ('W' == gpsInfo->ew)
            {
                g_selfCheckInfo.deviceStatus.longitude = (uint32_t)((gpsInfo->lon + (double)180) * (double)1000000);
            }
            if ('N' == gpsInfo->ns)
            {
                g_selfCheckInfo.deviceStatus.latitude = (uint32_t)(gpsInfo->lat * (double)1000000);
            }
            else if ('S' == gpsInfo->ns)
            {
                g_selfCheckInfo.deviceStatus.latitude = (uint32_t)((gpsInfo->lat + (double)90) * (double)1000000);
            }
            g_selfCheckInfo.deviceStatus.speed = (uint32_t)(gpsInfo->speed * (double)1000);
        }
    }

    return TBOX_OK;
}

/*************************************************
函数名称: simCardModuleSelfCheck
函数功能: simCard 模块周期自检
输入参数:
输出参数: 无
函数返回类型值：0:正常 1:故障
编写者: zxl
编写日期 :2020/01/06
*************************************************/
static int SimCardModuleSelfCheck(void)
{
    int ret = 0x00;
    ModuleInitStatus st;
    char iccid[GENERAL_ICCID_LENGTH + 1];

    //TODO 开沃有增加SIM异常时尝试复位恢复的功能，这里暂时不用，后面讨论是否需要
    ret = GetModuleSimStatus(&st);
    if(MODULE_INIT_SIM_READY == st)
    {
        g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].errorStatusCnt = 0x00;
        g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].normalStatusCnt++;
        if(g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].normalStatusCnt >= 6)
        {
            g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].normalStatusCnt = 0x00;

            if(DTC_DEVICE_NORMAL_STATUS != g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].status)
            {
                g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
                IpcReportArmDtc(DTC_SIM_CARD_FAULT_INDEX , DTC_STATUS_NO_FAULT);
                GetModuleIccid(GENERAL_ICCID_LENGTH + 1, iccid);
                LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_INFO, "Fault Api Check SimCard Success, ICCID:%s",iccid);
            }
        }
        g_selfCheckInfo.deviceStatus.simCardStatus = 0;
    }
    else
    {
        g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].errorStatusCnt++;
        g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].normalStatusCnt = 0x00;
        if(g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].errorStatusCnt >= 6)
        {
            g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].errorStatusCnt = 0x00;

            if(DTC_DEVICE_ERROR_STATUS != g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].status)
            {
                g_selfCheckInfo.dtcDeviceTable[DTC_SIM_CARD_FAULT_INDEX].status = DTC_DEVICE_ERROR_STATUS;
                IpcReportArmDtc(DTC_SIM_CARD_FAULT_INDEX , DTC_STATUS_FAULT);
                LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR, "Fault Api Check SimCard fail");
            }
            g_selfCheckInfo.deviceStatus.simCardStatus = 1;
        }
    }

    return TBOX_OK;
}

// 信号强度等级映射
int GetSignalStrengthLevel(uint32_t sig) {
    if (sig >= 40) {
        return 5;
    } else if (sig >= 33) {
        return 4;
    } else if (sig >= 27) {
        return 3;
    } else if (sig >= 20) {
        return 2;
    } else if (sig >= 13) {
        return 1;
    } else {
        return 0;
    }
}

// 比较函数，用于排序信号强度（从大到小）
int CompareSignalStrength(const void *a, const void *b) {
    return ((SingleSatelliteInfo*)b)->sig - ((SingleSatelliteInfo*)a)->sig;
}

// 计算GNSS信号强度等级
int CalculateGNSSSignalStrength(SatelliteInfo *satInfo) {
    // 创建一个临时数组用于存放所有卫星的信号强度信息
    SingleSatelliteInfo allSatellites[NMEA_MAXSAT * 4];  // 4种系统的卫星
    int totalSatellites = 0;

    // 将所有卫星的信号强度数据合并到一个数组中
    for (int i = 0; i < satInfo->gpsinview; i++)
    {
        allSatellites[totalSatellites++] = satInfo->gpsat[i];
    }
    for (int i = 0; i < satInfo->bdinview; i++)
    {
        allSatellites[totalSatellites++] = satInfo->bdsat[i];
    }
    for (int i = 0; i < satInfo->glinview; i++)
    {
        allSatellites[totalSatellites++] = satInfo->glsat[i];
    }
    for (int i = 0; i < satInfo->gainview; i++)
    {
        allSatellites[totalSatellites++] = satInfo->gasat[i];
    }

    // 如果少于6个卫星信息，返回等级 0
    if ( totalSatellites < 6)
    {
        return 0;
    }

    // 按照信号强度（sig）从大到小排序
    qsort(allSatellites, totalSatellites, sizeof(SingleSatelliteInfo), CompareSignalStrength);

    // 检查前6颗卫星的信号强度是否都满足某个等级
    for (int level = 5; level >= 0; level--)
    {
        int allMatch = 1;
        for (int i = 0; i < 6; i++)
        {
            if (GetSignalStrengthLevel(allSatellites[i].sig) < level)
            {
                allMatch = 0;
                break;
            }
        }
        if (allMatch) {
            return level;  // 如果全部卫星的信号强度满足某个等级，返回该等级
        }
    }

    return 0;  // 如果没有一个等级符合，返回 0
}
/*************************************************
函数名称: GpsModuleHardSelfCheck
函数功能: gnss 模块周期自检
输入参数:
输出参数: 无
函数返回类型值：0:正常 1:故障
编写者: zxl
编写日期 :2020/01/06
*************************************************/
static int GpsModuleHardSelfCheck(void)
{
    GpsInfo gpsInfo;

    GetGpsLocInfo(&gpsInfo);
    if(0x01 == gpsInfo.gnssModuleStatus)//有数据
    {
        g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].errorStatusCnt = 0x00;
        g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].normalStatusCnt++;
        if(g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].normalStatusCnt >= HARDWARE_FAULT_CONFIRM_CNT)
        {
            g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].normalStatusCnt = 0x00;

            if(DTC_DEVICE_NORMAL_STATUS != g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].status)
            {
                g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
                IpcReportArmDtc(DTC_GNSS_MODULE_FAULT_INDEX , DTC_STATUS_NO_FAULT);
                LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_INFO, "Fault Api GNSS ok");
                
            }
        }
        GpsModulePositionSelfCheck(&gpsInfo);
    }
    else if(0x02 == gpsInfo.gnssModuleStatus)
    {
        g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].errorStatusCnt++;
        g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].normalStatusCnt = 0x00;
        if(g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].errorStatusCnt >= HARDWARE_FAULT_CONFIRM_CNT)
        {
            g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].errorStatusCnt = 0x00;
            //TODO考虑是否需要再激活一次通道 PowerManagerActiveExternalGpsPort();
            if(DTC_DEVICE_ERROR_STATUS != g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].status)
            {
                g_selfCheckInfo.dtcDeviceTable[DTC_GNSS_MODULE_FAULT_INDEX].status = DTC_DEVICE_ERROR_STATUS;
                IpcReportArmDtc(DTC_GNSS_MODULE_FAULT_INDEX , DTC_STATUS_FAULT);
                LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR, "Fault Api GNSS fault");
                //eGpsMemsetLocInfo();
                g_selfCheckInfo.deviceStatus.gpsFixStatus = GNSS_MODULE_SERIAL_FAULT;
            }
        }
    }

    return TBOX_OK;
}

/*************************************************
函数名称: EMMCStatusNormal
函数功能: EMMC读写状态检测
输入参数: 无
输出参数: 无
函数返回类型值：TRUE:EMMC状态正常 FALSE:无EMMC挂载或读写失败
编写者: zhengyong
编写日期 :2022/07/19
*************************************************/
static boolean EMMCStatusNormal(void)
{
    int fd = 0;
    const char *emmcWriteData = "This is only for emmc test!";
    char emmcReadData[32] = {0};
    const char *emmcMountCheckCmd = "df -h | grep "EMMC_PARTITION_1_PATH" | wc -l";

    SystemPopenCommand(emmcMountCheckCmd, emmcReadData, sizeof(emmcReadData), 0);
    if(strncmp(emmcReadData, "1", 1) != 0)
    {
        LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR, "No emmc mount to media card path,result:%s", emmcReadData);
        return FALSE;
    }
    for (int i = 0; i < EMMC_PARTITION_NUM; ++i)
    {
        char path[100] = {0};
        switch (i)
        {
            case 0:
                snprintf(path, sizeof(path), "%s%s", EMMC_PARTITION_1_PATH, EMMC_TEST_FILE_NAME);
                break;
            case 1:
                snprintf(path, sizeof(path), "%s%s", EMMC_PARTITION_2_PATH, EMMC_TEST_FILE_NAME);
                break;
            case 2:
                snprintf(path, sizeof(path), "%s%s", EMMC_PARTITION_3_PATH, EMMC_TEST_FILE_NAME);
                break;
            default:
                break;
        }

        if(0 == access(path, F_OK))
        {
            if (unlink(path) != 0)
            {
                LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR, "Failed to unlink test file");
                return FALSE;
            }
        }

        fd = open(path, O_CREAT | O_WRONLY, 0644);
        if(fd < 0)
        {
            LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR, "Failed to create test file, fd = %d", fd);
            return FALSE;
        }

        if (write(fd, emmcWriteData, strlen(emmcWriteData)) < 0)
        {
            LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR, "Failed to write test data to emmc");
            close(fd);
            return FALSE;
        }
        close(fd);

        fd = open(path, O_RDONLY);
        if(fd < 0)
        {
            LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR, "Failed to open test file for reading, fd = %d", fd);
            return FALSE;
        }

        memset(emmcReadData, 0, sizeof(emmcReadData));
        if (read(fd, emmcReadData, sizeof(emmcReadData)) < 0)
        {
            LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR, "Failed to read test file from emmc");
            close(fd);
            return FALSE;
        }
        close(fd);

        if(strcmp(emmcWriteData, emmcReadData) != 0)
        {
            LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR, "Data mismatch, read: %s", emmcReadData);
            return FALSE;
        }
        remove(path);
    }
    LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_INFO, "Emmc is normal!!");
    return TRUE;
}

/*************************************************
函数名称: EMMCStatusCheck
函数功能: EMMC读写状态检测
输入参数: 无
输出参数: 无
函数返回类型值：0:正常 -1:故障
编写者: zhengyong
编写日期 :2022/07/19
*************************************************/
static int EMMCStatusCheck(void)
{
    int ret = TBOX_OK;

    if (g_selfCheckInfo.emmcCheckFlag)
    {
        return TBOX_OK;
    }

    if (EMMCStatusNormal())
    {
        g_selfCheckInfo.deviceStatus.emmcStatus = 1;
        IpcReportArmDtc(DTC_EMMC_STORAGE_CHIP_FAULT_INDEX, DTC_STATUS_NO_FAULT);
    }
    else
    {
        g_selfCheckInfo.deviceStatus.emmcStatus = 0;
        IpcReportArmDtc(DTC_EMMC_STORAGE_CHIP_FAULT_INDEX, DTC_STATUS_FAULT);
        ret = TBOX_ERROR;
    }

    g_selfCheckInfo.emmcCheckFlag = TRUE;
    return ret;
}

// Tsp登录状态监测
static void TspLoginStatusCheck(void)
{
    int ret = TBOX_OK;
    int loginStatus = 0;

    loginStatus = GetIntProperty(FIRST_LINK_LOGIN_STATUS_KEY, 0);
    if (loginStatus)
    {
        g_selfCheckInfo.deviceStatus.tspConnectStatus = TSP_STATUS_LOGINED;
    }
    else
    {
        g_selfCheckInfo.deviceStatus.tspConnectStatus = TSP_STATUS_UNLOGINED;
    }
}


/*************************************************
函数名称: CheckEMMCPartitions
函数功能: 检测EMMC所有分区
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2025/01/14
*************************************************/
void CheckEMMCPartitions(void)
{
    boolean flag = TRUE;
    // 检测分区1（10MB）
    if (GetPathFreeSpace(EMMC_PARTITION_1_PATH) < THRESHOLD_PARTITION_1_MB)
    {
        LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR,"Partition 1 %s has less than %uMB of free space.\n", EMMC_PARTITION_1_PATH,THRESHOLD_PARTITION_1_MB);
        flag = FALSE;
    }
    if (GetPathFreeSpace(EMMC_PARTITION_2_PATH) < THRESHOLD_PARTITION_2_MB)
    {
        LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR,"Partition 2 %s has less than %uMB of free space.\n", EMMC_PARTITION_2_PATH,THRESHOLD_PARTITION_2_MB);
        flag = FALSE;
    }
    if (GetPathFreeSpace(EMMC_PARTITION_3_PATH) < THRESHOLD_PARTITION_3_MB)
    {
        LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR,"Partition 3 %s has less than %uMB of free space.\n", EMMC_PARTITION_3_PATH,THRESHOLD_PARTITION_3_MB);
        flag = FALSE;
    }

    if(flag)
    {
        IpcReportArmDtc(DTC_EMMC_STORAGE_FULL_INDEX , DTC_STATUS_NO_FAULT);
    }
    else
    {
        IpcReportArmDtc(DTC_EMMC_STORAGE_FULL_INDEX , DTC_STATUS_FAULT);
    }

}
/*************************************************
函数名称: CheckEMMCPartitions
函数功能: 检测模组tbox使用的目录
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2025/01/14
*************************************************/
void CheckModulePartitions(void)
{
    boolean flag = TRUE;
    // 检测模组存储
    if (GetPathFreeSpace(TBOX_ROOT_PATH) < THRESHOLD_MODULES_MB)
    {
        printf("Partition 1 %s has less than %uMB of free space.\n", TBOX_ROOT_PATH,THRESHOLD_MODULES_MB);
        flag = FALSE;
    }

    if(flag)
    {
        IpcReportArmDtc(DTC_NETWORK_MODULE_FLASH_FULL_INDEX , DTC_STATUS_NO_FAULT);
    }
    else
    {
        IpcReportArmDtc(DTC_NETWORK_MODULE_FLASH_FULL_INDEX , DTC_STATUS_FAULT);
    }
}

/*************************************************
函数名称: CheckModuleNetwork
函数功能: 检测模组网络
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2025/01/14
*************************************************/
void CheckModuleNetwork(void)
{
    ModuleNetStatus netStatus = MODULE_NET_NOT_REG;
    char addr[33] = {0};

    if (TBOX_OK == GetModuleNetStatus(&netStatus) && MODULE_NET_REGISTERED == netStatus)
    {
        g_selfCheckInfo.deviceStatus.moduleStatus = G4_GSM_REGISTERED;
        GetStringProperty(LINK1_ADDR_KEY, addr, sizeof(addr),FIRST_LINK_SERVER_DOMIN);
    }
    else
    {
        g_selfCheckInfo.deviceStatus.moduleStatus = G4_GSM_NOT_REGISTERED;
    }

    if(g_selfCheckInfo.deviceStatus.moduleStatus == G4_GSM_REGISTERED && TBOX_OK == SystemApiPing(TBOX_SYSTEM_STATUS,addr))//有数据
    {
        g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].errorStatusCnt = 0x00;
        g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].normalStatusCnt++;
        if(g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].normalStatusCnt >= 0x01)
        {
            g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].normalStatusCnt = 0x00;
            if(DTC_DEVICE_NORMAL_STATUS != g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].status)
            {
                g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
                IpcReportArmDtc(DTC_REMOTE_COM_FAULT_INDEX , DTC_STATUS_NO_FAULT);
                // 通知GPS检测AGPS星历
                StartAgpsNavigat();
                LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_INFO, "Module Network ok");
            }
            g_selfCheckInfo.deviceStatus.moduleStatus = G4_CONNECT_NETWORK;
        }
    }
    else
    {
        g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].errorStatusCnt++;
        g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].normalStatusCnt = 0x00;
        if(g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].errorStatusCnt >= 0x06)
        {
            g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].errorStatusCnt = 0x00;
            if(DTC_DEVICE_ERROR_STATUS != g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].status)
            {
                g_selfCheckInfo.dtcDeviceTable[DTC_REMOTE_COM_FAULT_INDEX].status = DTC_DEVICE_ERROR_STATUS;
                IpcReportArmDtc(DTC_REMOTE_COM_FAULT_INDEX , DTC_STATUS_FAULT);
                LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_INFO, "Module Network fault");
            }
            g_selfCheckInfo.deviceStatus.moduleStatus = G4_GSM_NOT_REGISTERED;
        }
    }
}
/*************************************************
函数名称: PeriodTxDeviceStatus
函数功能: 电源管理发送ARM设备信息状态
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/12
*************************************************/
void PeriodTxDeviceStatus(void)
{
    IpcReportDeviceStatus(&g_selfCheckInfo.deviceStatus);
}
/*************************************************
函数名称: PeriodComputeSignal
函数功能: 周期计算信号量
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2025/01/08
*************************************************/
static void PeriodComputeSignal(void)
{
    int ret = 0;
    GpsInfo gpsInfo = {0};
    uint8_t moduleRssi = 0x00;
    uint8_t moduleBer = 0x00;

    GetGpsLocInfo(&gpsInfo);

    int gnssSignal = CalculateGNSSSignalStrength(&gpsInfo.satinfo);
    SetIntProperty(GNSS_SIGNAL_STRENGTH_KEY, gnssSignal);

    ret = GetModuleRssi(&moduleRssi, &moduleBer);
    if (TBOX_OK == ret)
    {
        g_selfCheckInfo.deviceStatus.moduleSignal = moduleRssi;
        int moduleSignal = ProcessModuleRssi(moduleRssi);
        SetIntProperty(CELLULAR_SIGNAL_STRENGTH_KEY, moduleSignal);
    }
    else
    {
        g_selfCheckInfo.deviceStatus.moduleSignal = 0x00;
        SetIntProperty(CELLULAR_SIGNAL_STRENGTH_KEY, 0);
    }


}

/*************************************************
函数名称: SelfCheckDtcInfoCb
函数功能: 系统通知DTC信息接收回调函数
输入参数: dtcData：DTC数据；dataLen数据长度
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期 :2022/07/19
*************************************************/
void SelfCheckDtcInfoCb(uint8_t *dtcData, uint16_t dataLen)
{
    DTCReqType reqType  = dtcData[0];
    DtcReqResultStatus reqResultStatus = dtcData[1];
    uint8_t *dtcStatusData = &dtcData[2];

    /*ARM Rrfresh ALL devices Status*/
    if(DTC_REQ_CLEAR == reqType)
    {
        memset(g_selfCheckInfo.dtcDeviceTable, 0x00, sizeof(g_selfCheckInfo.dtcDeviceTable));
        for(int i = 0x00; i < DTC_MAX_INDEX; i++)
        {
            g_selfCheckInfo.dtcDeviceTable[i].status = DTC_DEVICE_IDLE_STATUS;
        }
        memset(g_selfCheckInfo.allDtcStatus, 0x00, DTC_MAX_INDEX);
        g_selfCheckInfo.emmcCheckFlag = FALSE; //同时清除emmc标志，重新检测
        g_selfCheckInfo.lastCheckTime_10s = 0;
        g_selfCheckInfo.lastCheckTime_600s = 0;
        SetUint8ArrayProperty(TBOX_DTC_STATUS_KEY, g_selfCheckInfo.allDtcStatus,DTC_MAX_INDEX);
        LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_INFO, "MCU notify ARM clear DTC");
    }
    else if(DTC_REQ_RESTART_CHECK == reqType)
    {
        memset(g_selfCheckInfo.dtcDeviceTable, 0x00, sizeof(g_selfCheckInfo.dtcDeviceTable));
        for(int i = 0x00; i < DTC_MAX_INDEX; i++)
        {
            g_selfCheckInfo.dtcDeviceTable[i].status = DTC_DEVICE_IDLE_STATUS;
        }
        g_selfCheckInfo.dtcCheckStartFlag = DEVICES_AVILABLE;
//        g_selfCheckInfo.emmcCheckFlag = FALSE; //同时清除emmc标志，重新检测
        g_selfCheckInfo.lastCheckTime_10s = 0;
        g_selfCheckInfo.lastCheckTime_600s = 0;
        LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_INFO, "MCU notify ARM start DTC");
    }
    else if(DTC_REQ_STOP_CHECK == reqType)
    {
        g_selfCheckInfo.dtcCheckStartFlag = DEVICES_UNAVILABLE;
        LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_INFO, "MCU notify ARM stop DTC");
    }
    else if(DTC_REQ_SYNC == reqType)
    {
       if(dataLen - 2 == DTC_MAX_INDEX)
       {
           memcpy(g_selfCheckInfo.allDtcStatus, dtcStatusData, DTC_MAX_INDEX);
           SetUint8ArrayProperty(TBOX_DTC_STATUS_KEY, dtcStatusData,DTC_MAX_INDEX);
           SystemPrintHexData(TBOX_SYSTEM_STATUS, TBOX_LOG_INFO, dtcStatusData,DTC_MAX_INDEX,"MCU notify ARM sync DTC : ");
       }
       else
       {
           LogServiceBuffer(TBOX_SYSTEM_STATUS, TBOX_LOG_ERROR, "MCU notify ARM ,The DTC INDEX definitions are not the same");
       }
    }
}

/*************************************************
函数名称: SelfCheckThread
函数功能: 通过故障码，更新DeviceStatus
输入参数: len:数据长度，buf :IPC 读取故障码返回数据指针
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/09
*************************************************/
static void *SelfCheckThread(void *arg)
{
    time_t currentTime = 0;
    static uint8_t count = 0;

    while(1)
    {
        sleep(1);

        if (NORMAL_STATUS != GetSystemPowerStatus())
        {
            continue;
        }
        currentTime = time(NULL);

        PeriodComputeSignal(); // 计算信号量

        if (++count >= 2)
        {
            PeriodTxDeviceStatus(); // 周期同步 ARM 信息给 MCU
            count = 0;
        }

        // if(DEVICES_AVILABLE == g_selfCheckInfo.dtcCheckStartFlag)
        {

            GpsModuleHardSelfCheck(); // GPS 模块检测
            EMMCStatusCheck();        // EMMC 挂载检测
            TspLoginStatusCheck();    // TSP 登录状态检测

                // 10秒周期检测
            if (currentTime - g_selfCheckInfo.lastCheckTime_10s >= 10) // 如果已经过了 10秒
                {
                    SimCardModuleSelfCheck();   // SIM 卡检测
                    CheckModuleNetwork();       // 模组网络检测
                    g_selfCheckInfo.lastCheckTime_10s = currentTime; // 更新 10秒周期的时间戳
                }

                // 600秒周期检测
            if (currentTime - g_selfCheckInfo.lastCheckTime_600s >= 600) // 如果已经过了 10分钟
            {
                CheckEMMCPartitions();      // EMMC 剩余空间检测
                CheckModulePartitions();    // 模组剩余空间检测
                g_selfCheckInfo.lastCheckTime_600s = currentTime; // 更新 600秒周期的时间戳
            }

        }


    }
}

/*************************************************
函数名称: SelfCheckFlagReset
函数功能: 休眠唤醒时清除标志位重新检测
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/06/20
*************************************************/
void SelfCheckFlagReset(void)
{
    g_selfCheckInfo.emmcCheckFlag = FALSE;
}

/*************************************************
函数名称: SelfCheckInit
函数功能: 开机ARM侧故障检测线程
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/06/20
*************************************************/
void SelfCheckInit(void)
{
    int ret = 0;
    pthread_t faultCheckThread;
    memset((void *)&g_selfCheckInfo, 0, sizeof(g_selfCheckInfo));
    ret = pthread_create(&faultCheckThread, NULL, SelfCheckThread, NULL);
    LogServiceBuffer(TBOX_MODULE_SYSCTRL, TBOX_LOG_INFO, "SelfCheckInit ret = %u", ret);
}





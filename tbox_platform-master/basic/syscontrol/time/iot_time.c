/*
      iot_time.c
描述：此文件Timer Server主要是提供LINUX时间同步功能，包括MCU、Modem、GPS
      的时间同步，时间校准优先级是Modem-> GPS->MCU。Modem发现时区发生变化需要
	  同步时间。
作者：derek
时间：2018.03.27
*/
#include <arpa/inet.h>
#include <netdb.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/select.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <errno.h>
#include <time.h>

#include "iot_log_common.h"
#include "tbox_typedef.h"
#include "tbox_paraconfig.h"
#include "iot_gpsc.h"
#include "iot_time.h"
#include "iot_logc.h"
#include "iot_dbus.h"
#include "iot_ipcc.h"
#include "property_api.h"

#define MCUBUFLEN 64
#define NTP_VERSION 		0xe3
#define NTP_DEFAULT_PORT	"123"
#define SEC_IN_YEAR 		31556926
#define UNIX_OFFSET 		2208988800UL
#define VN_BITMASK(byte) 	((byte & 0x3f) >> 3)
#define LI_BITMASK(byte) 	(byte >> 6)
#define MODE_BITMASK(byte) 	(byte & 0x7)
#define ENDIAN_SWAP32(data)  	((data >> 24) | /* right shift 3 bytes */ \
				((data & 0x00ff0000) >> 8) | /* right shift 1 byte */ \
			        ((data & 0x0000ff00) << 8) | /* left shift 1 byte */ \
				((data & 0x000000ff) << 24)) /* left shift 3 bytes */

struct ntpPacket
{
    uint8_t flags;
    uint8_t stratum;
    uint8_t poll;
    uint8_t precision;
    uint32_t root_delay;
    uint32_t root_dispersion;
    uint8_t referenceID[4];
    uint32_t ref_ts_sec;
    uint32_t ref_ts_frac;
    uint32_t origin_ts_sec;
    uint32_t origin_ts_frac;
    uint32_t recv_ts_sec;
    uint32_t recv_ts_frac;
    uint32_t trans_ts_sec;
    uint32_t trans_ts_frac;
} __attribute__((__packed__));

//上海交大，阿里云123
static int trigger_cb = 0; //用于触发回调

//用于存储回调函数
static time_srv_cb ntp_cb;
static time_srv_cb mcu_cb;

static uint8_t notify_time_changed = 0;
static int notify_time_running = 0;
static int mcu_time_calibrated = 0;

static TimeConfigInfo g_timeConfigInfo = {0};


/*************************************************
函数名称: TimeGetConfigPara
函数功能: 获取time的配置项
输入参数: 无
输出参数: timeConfig - 配置项
函数返回类型值：无
编写者: zhengyong
编写日期 :2020/01/13
*************************************************/
void TimeGetConfigPara(TimeConfigInfo *timeConfig)
{
    TBOX_CHECK_NULL_NON_RETURN(TBOX_MODULE_TIME, timeConfig);

    memcpy(timeConfig, &g_timeConfigInfo, sizeof(TimeConfigInfo));
}


/*************************************************
函数名称: TimeSetConfigPara
函数功能: 设置Time相关配置项
输入参数: pTboxConfigInfo - 时间信息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2019/01/13
*************************************************/
int TimeSetConfigPara(TimeConfigInfo *timeConfig)
{
    uint8_t  buf[TBOX_MAX_CONFIG_PARA_LEN];

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_TIME, timeConfig, TBOX_ERROR_NULL_POINTER);

    if (timeConfig->zoneTime < TIME_ZONE_MIN_VAL || timeConfig->zoneTime > TIME_ZONE_MAX_VAL)
    {
        LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_ERROR, "Time zone(%d) over range.", timeConfig->zoneTime);
        return TBOX_ERROR_PARA_OVER_RANGE;
    }

    if (timeConfig->zoneTime != g_timeConfigInfo.zoneTime)
    {
        g_timeConfigInfo.zoneTime = timeConfig->zoneTime;
    }

    if (0 != strcmp(timeConfig->ntpServer[0].domin, g_timeConfigInfo.ntpServer[0].domin))
    {
        snprintf(g_timeConfigInfo.ntpServer[0].domin, sizeof(g_timeConfigInfo.ntpServer[0].domin), "%s", timeConfig->ntpServer[0].domin);
    }

    if (0 != strcmp(timeConfig->ntpServer[1].domin, g_timeConfigInfo.ntpServer[1].domin))
    {
        snprintf(g_timeConfigInfo.ntpServer[1].domin, sizeof(g_timeConfigInfo.ntpServer[1]), "%s", timeConfig->ntpServer[1].domin);
    }

    if (0 != strcmp(timeConfig->ntpServer[2].domin, g_timeConfigInfo.ntpServer[2].domin))
    {
        snprintf(g_timeConfigInfo.ntpServer[2].domin, sizeof(g_timeConfigInfo.ntpServer[2].domin), "%s", timeConfig->ntpServer[2].domin);
    }

    LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "Change time config success: time_zone=%d, "
                     "NTP addr1=%s, NTP addr2=%s, NTP addr3=%s",  g_timeConfigInfo.zoneTime,
                     g_timeConfigInfo.ntpServer[0].domin, g_timeConfigInfo.ntpServer[1].domin, g_timeConfigInfo.ntpServer[2].domin);

    return TBOX_OK;
}


/*
 * @brief 请求MCU的RTC时钟，由于MCU通信是异步的，故需要注册回调函数
 * @param ev 回调函数指针
 * @return 该IPC命令是否成功发送
 * 0，发送失败；1，成功发送
 */
static int TimeGetMcuTime(time_srv_cb ev)
{
    int ret;

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_TIME, ev, TBOX_ERROR_NULL_POINTER);

    mcu_cb = ev;

    ret = IpcRequestRtcTime();
    TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_TIME, ret, "Request rtc time failed", ret);

    return TBOX_OK;
}

/*
 * @brief 请求设置MCU的RTC时钟
 * @param void
 * @return 该IPC命令是否成功发送
 * 0，发送失败；1，成功发送
 */
static int TimeSetMcuTime(TimeInfo *pTimeInfo)
{
    if (mcu_time_calibrated)
    {
        LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "SKIP CAL MCU TIME");
        return 0;
    }

    LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "CAL MCU TIME %02u-%02u-%02u %02u:%02u:%02u", pTimeInfo->year, \
                     pTimeInfo->month, pTimeInfo->day, pTimeInfo->hour, pTimeInfo->minute, pTimeInfo->second);

    IpcSetRtcTime(pTimeInfo);

    mcu_time_calibrated = !mcu_time_calibrated; //MCU时间只校准一次

    return 0;
}

/*
 * @brief 获取GPS的时间，当gps定位有效的情况下返回正常
 * @param pTimeInfo
 * @return 0，成功，其他- 失败
 */
static int TimeGetGPSTime(TimeInfo *pTimeInfo)
{
    GpsInfo gpsInfo;
    int ret = 0;

    memset(&gpsInfo, 0, sizeof(gpsInfo));
    ret = GetGpsLocInfo(&gpsInfo);
    TBOX_CHECK_ERROR_RETURN(TBOX_MODULE_TIME, ret, "Get gps time fail", ret);

    // 使用RMC的fixStatus来判断是否可以校时，因为RMC包含完整的时间信息
    // 同时确保接收到了RMC数据且年份合理
    if ((gpsInfo.fixStatus == 'A') && (gpsInfo.smask & RMC) && ((gpsInfo.utc.year + 2000) >= 2019))
    {
        pTimeInfo->year = gpsInfo.utc.year;
        pTimeInfo->month = gpsInfo.utc.mon;
        pTimeInfo->day = gpsInfo.utc.day;
        pTimeInfo->hour = gpsInfo.utc.hour;
        pTimeInfo->minute = gpsInfo.utc.min;
        pTimeInfo->second = gpsInfo.utc.sec;
        pTimeInfo->zoneTime = 0xff;
        return TBOX_OK;
    }

    return TBOX_ERROR;
}

static void *ntp_get_thread(void *argv)
{
    TimeInfo tInfo;
    fd_set fds;
    struct timeval tv;

    NtpServerInfo_s *server; /* no default server */
    char *port = NTP_DEFAULT_PORT;
    char ipStr[INET6_ADDRSTRLEN];
    void *addr;
    struct addrinfo hints, *res, *ap; /* address info structs */
    socklen_t addrlen = sizeof(struct sockaddr_storage);

    struct ntpPacket packet;
    uint8_t *ptr = (uint8_t *)(&packet); /* to read raw bytes */

    int server_sock; /* send through this socket */
    int error; /* error checking */
    int i;
    unsigned int recv_secs;

    time_t total_secs;
    struct tm *now;

    if (NULL == (char *)argv)
    {
        return NULL;
    }

    usleep(1000);

    server = (NtpServerInfo_s *)argv;

    memset(&packet, 0, sizeof(struct ntpPacket));
    packet.flags = NTP_VERSION;

    memset(&hints, 0, sizeof(hints));
    hints.ai_socktype = SOCK_DGRAM;

    /* fill our address structs for ntp server */
    error = getaddrinfo(server->domin, port, &hints, &res);
    /* error checking */
    if (error != 0)
    {
        LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_ERROR, "getaddrinfo() error: %s", gai_strerror(error));
        return NULL;
    }
    for(ap = res; ap != NULL; ap = ap->ai_next) {
        if (ap->ai_family == AF_INET) { // IPv4
            struct sockaddr_in *ipv4 = (struct sockaddr_in *)ap->ai_addr;
            addr = &(ipv4->sin_addr);
        } else { // IPv6
            struct sockaddr_in6 *ipv6 = (struct sockaddr_in6 *)ap->ai_addr;
            addr = &(ipv6->sin6_addr);
        }
        // Convert the IP to a string
        inet_ntop(ap->ai_family, addr, ipStr, sizeof ipStr);
    }

    /* loop through results */
    for (ap = res; ap != NULL; ap = ap->ai_next)
    {
        server_sock = socket(ap->ai_family, ap->ai_socktype, ap->ai_protocol);
        if (server_sock == -1)
        {
            continue;
        }
        break;
    }
    if (ap == NULL)
    {
        LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_ERROR, "getaddrinfo() error");
        goto error_exit;
    }

    error = sendto(server_sock, &packet, sizeof(struct ntpPacket), 0, ap->ai_addr, addrlen);
    if (error == -1)
    {
        LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_ERROR, "sendto() error");
        goto error_exit;
    }

    //wait for reading
    FD_ZERO(&fds);
    FD_SET(server_sock, &fds);
    tv.tv_sec = 3;
    tv.tv_usec = 0;
    error = select(server_sock + 1, &fds, NULL, NULL, &tv);
    if (error > 0)
    {
        error = recvfrom(server_sock, &packet, sizeof(struct ntpPacket), 0, ap->ai_addr, &addrlen);
        if (error == -1)
        {
            LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_ERROR, "recvfrom() error");
            goto error_exit;
        }
    }
    else if (error <= 0)
    {
        LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "NTP %s server not rsponse...", server);
        goto error_exit;
    }

#ifdef _DEBUG_
    /* print raw bytes */
    for (i = 0; i < sizeof(struct ntpPacket); i++)
    {
        if (i != 0 && i % 8 == 0)
        {
            printf("\n");
        }
        printf("0x%2x ", ptr[i]);
    }
    printf("\n");
#endif
    /* correct for right endianess */
    packet.root_delay = ENDIAN_SWAP32(packet.root_delay);
    packet.root_dispersion = ENDIAN_SWAP32(packet.root_dispersion);
    packet.ref_ts_sec = ENDIAN_SWAP32(packet.ref_ts_sec);
    packet.ref_ts_frac = ENDIAN_SWAP32(packet.ref_ts_frac);
    packet.origin_ts_sec = ENDIAN_SWAP32(packet.origin_ts_sec);
    packet.origin_ts_frac = ENDIAN_SWAP32(packet.origin_ts_frac);
    packet.recv_ts_sec = ENDIAN_SWAP32(packet.recv_ts_sec);
    packet.recv_ts_frac = ENDIAN_SWAP32(packet.recv_ts_frac);
    packet.trans_ts_sec = ENDIAN_SWAP32(packet.trans_ts_sec);
    packet.trans_ts_frac = ENDIAN_SWAP32(packet.trans_ts_frac);
#ifdef _DEBUG_
    /* print raw data */
    printf("LI: %d\n", LI_BITMASK(packet.flags));
    printf("VN: %d\n", VN_BITMASK(packet.flags));
    printf("Mode: %d\n", MODE_BITMASK(packet.flags));
    printf("stratum: %u\n", packet.stratum);
    printf("poll: %u\n", packet.poll);
    printf("precision: %u\n", packet.precision);
    printf("root delay: %u\n", packet.root_delay);
    printf("root dispersion: %u\n", packet.root_dispersion);
    printf("reference ID: %u.", packet.referenceID[0]);
    printf("%u.", packet.referenceID[1]);
    printf("%u.", packet.referenceID[2]);
    printf("%u\n", packet.referenceID[3]);
    printf("reference timestamp: %u.", packet.ref_ts_sec);
    printf("%u\n", packet.ref_ts_frac);
    printf("origin timestamp: %u.", packet.origin_ts_sec);
    printf("%u\n", packet.origin_ts_frac);
    printf("receive timestamp: %u.", packet.recv_ts_sec);
    printf("%u\n", packet.recv_ts_frac);
    printf("transmit timestamp: %u.", packet.trans_ts_sec);
    printf("%u\n", packet.trans_ts_frac);
#endif
    /* print date with receive timestamp */
    recv_secs = packet.recv_ts_sec - UNIX_OFFSET; /* convert to unix time */
    total_secs = recv_secs;
    //printf("Unix time: %u\n", (unsigned int)total_secs);
    now = localtime(&total_secs);
    LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "%02d/%02d/%d %02d:%02d:%02d", now->tm_mday, now->tm_mon + 1, \
                    now->tm_year + 1900, now->tm_hour, now->tm_min, now->tm_sec);

    tInfo.year = now->tm_year + 1900 - 2000;
    tInfo.month = now->tm_mon + 1;
    tInfo.day = now->tm_mday;
    tInfo.hour = now->tm_hour;
    tInfo.minute = now->tm_min;
    tInfo.second = now->tm_sec;

    //UTCToPERKGTime(&tInfo);

    LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "NTP_RECV_(%02u-%02u-%02u %02u:%02u:%02u)", tInfo.year, tInfo.month, tInfo.day, \
                     tInfo.hour, tInfo.minute, tInfo.second);
    if ((NULL != ntp_cb) && trigger_cb)
    {
        trigger_cb = 0;
        ntp_cb(&tInfo, TIME_FLAG_MODEM_CALC);
    }
error_exit:
    freeaddrinfo(res); /* all done */
    close(server_sock);

    return NULL;
}

/*
 * @brief 获取NTP的时间
 * @param ev，异步事件的回调函数，主要是NTP时间获取
 * @return 0，成功，其他- 失败
 */
static int TimeGetNTPTime(time_srv_cb ev)
{
    int err;
    static int i = 0;
    pthread_t tid1, tid2, tid3;
    pthread_attr_t attr;


    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_TIME, ev, TBOX_ERROR_NULL_POINTER);
    trigger_cb = 1;
    ntp_cb     = ev;
    i          = i % 3;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    err = pthread_create(&tid1, &attr, ntp_get_thread, (void *)&g_timeConfigInfo.ntpServer[i++]);
    if (err)
    {
        LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_ERROR, "ntp threads %s", strerror(errno));
    }

    return TBOX_OK;
}

/*
 * @brief MCU校时回调用函数，供IPC回调使用
 * @param timeInfo 校准时间
 * @return void
 */
void TimeMcuCalCb(TimeInfo *timeInfo)
{
    TBOX_CHECK_NULL_NON_RETURN(TBOX_MODULE_TIME, timeInfo);

    LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "MCU_RECV_(%02u-%02u-%02u %02u:%02u:%02u)", \
                     timeInfo->year, timeInfo->month, timeInfo->day, \
                     timeInfo->hour, timeInfo->minute, timeInfo->second);

    if (NULL != mcu_cb)
    {
        mcu_cb(timeInfo, TIME_FLAG_MCU_CALC);
    }
}

/*************************************************
函数名称: TimeGpsCalcTime
函数功能: 获取到模块网络时间后同步时间
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/01/11
*************************************************/
void TimeGetCurrentTime(TimeInfo *pTimeInfo)
{
    time_t currentTime;
    struct tm *pCurrentTime;

    if (NULL == pTimeInfo)
    {
        return;
    }

    time(&currentTime);
    pCurrentTime = localtime(&currentTime);

    pTimeInfo->year   = (uint8_t)((pCurrentTime->tm_year + 1900) - 2000);
    pTimeInfo->month  = (uint8_t)pCurrentTime->tm_mon + 1;
    pTimeInfo->day    = pCurrentTime->tm_mday;
    pTimeInfo->hour   = pCurrentTime->tm_hour;
    pTimeInfo->minute = pCurrentTime->tm_min;
    pTimeInfo->second = pCurrentTime->tm_sec;
}

/*************************************************
函数功能: 触发时间上报消息
输入参数: pTimeInfo - 时间信息，type - 时间校准源
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期 :2020/01/14
*************************************************/
static void TimeNotifyTimeInfo(TimeInfo *pTimeInfo, TimeCalcFlag type)
{
    int ret;
    DBusMessageIter iter;
    DBusMessage *dbusMsg;
    DBusMsgParaInfo_s paraInfo;
    DBusMsgSendInfo_s msgSendInfo;
    const char module[] = DBUS_MODULE_SYSCTRL_NOTIFY;
    const char action[] = DBUS_ACTION_NOTIFY_CALITIME;

    TBOX_CHECK_NULL_NON_RETURN(TBOX_MODULE_TIME, pTimeInfo);

    msgSendInfo.isSignal = TRUE;
    msgSendInfo.module = module;
    msgSendInfo.action = action;
    msgSendInfo.paraInfo = &paraInfo;
    paraInfo.type = TBOX_DBUS_TYPE_UINT8;
    paraInfo.typeInfo.u8 = (uint8_t)type;

    //消息ID
    ret = DBusSendMessageBegin(&msgSendInfo, &dbusMsg, &iter);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_ERROR, "Broadcast time type failed, type=%d, ret=%d.", type, ret);
        return;
    }

    paraInfo.type = TBOX_DBUS_TYPE_ARRAY;
    paraInfo.arrayType = TBOX_DBUS_TYPE_UINT8;
    paraInfo.arrayLen = sizeof(TimeInfo);
    paraInfo.typeInfo.array = pTimeInfo;
    ret = DBusSetMessage(&iter, &paraInfo);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_ERROR, "Broadcast time info faild, ret=%d.", ret);
        DBusFreeMessage(dbusMsg);
        return;
    }

    ret = DBusSendMessageEnd(TRUE, dbusMsg, NULL, -1);
    LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "Broadcast time success, ret=%d.", ret);

    return;
}

/*************************************************
函数功能: 获取时间属性，包括时区和NTP服务器地址
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期 :2020/05/16
*************************************************/
static void TimeConfigInit(void)
{
    int ret = TBOX_OK;

    g_timeConfigInfo.zoneTime = GetRangeIntProperty(TIME_ZONE_TIME_KEY, TIME_ZONE_MIN_VAL,
                                                    TIME_ZONE_MAX_VAL, TIME_ZONE_DFT_VAL);

    ret = GetStringProperty(TIME_NTP_SERVER_KEY1, g_timeConfigInfo.ntpServer[0].domin,
                      sizeof(g_timeConfigInfo.ntpServer[0].domin), TIME_NTP_SERVER_DFT_VAL1);
    if(PROP_ERROR_BUFFER_OVERFLOW == ret)
    {
        strncpy(g_timeConfigInfo.ntpServer[0].domin, TIME_NTP_SERVER_DFT_VAL1, sizeof(g_timeConfigInfo.ntpServer[0].domin));
    }
    g_timeConfigInfo.ntpServer[0].id = TIME_NTP_SERVER_ID_1;

    ret = GetStringProperty(TIME_NTP_SERVER_KEY2, g_timeConfigInfo.ntpServer[1].domin,
                      sizeof(g_timeConfigInfo.ntpServer[1].domin), TIME_NTP_SERVER_DFT_VAL2);
    if(PROP_ERROR_BUFFER_OVERFLOW == ret)
    {
        strncpy(g_timeConfigInfo.ntpServer[1].domin, TIME_NTP_SERVER_DFT_VAL2, sizeof(g_timeConfigInfo.ntpServer[1]).domin);
    }
    g_timeConfigInfo.ntpServer[1].id = TIME_NTP_SERVER_ID_2;

    ret = GetStringProperty(TIME_NTP_SERVER_KEY3, g_timeConfigInfo.ntpServer[2].domin,
                      sizeof(g_timeConfigInfo.ntpServer[2].domin), TIME_NTP_SERVER_DFT_VAL3);
    if(PROP_ERROR_BUFFER_OVERFLOW == ret)
    {
        strncpy(g_timeConfigInfo.ntpServer[2].domin, TIME_NTP_SERVER_DFT_VAL3, sizeof(g_timeConfigInfo.ntpServer[2].domin));
    }
    g_timeConfigInfo.ntpServer[2].id = TIME_NTP_SERVER_ID_3;

    LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "Init time config finish, time zone=%d, ntp servers=%s/%s/%s.",
        g_timeConfigInfo.zoneTime, g_timeConfigInfo.ntpServer[0].domin, g_timeConfigInfo.ntpServer[1].domin, g_timeConfigInfo.ntpServer[2].domin);
}

static void Oneshotcb(TimeInfo *pTimeInfo, TimeCalcFlag type)
{
    char cmd[128];

    int syncTimeFlag = GetIntProperty(FIRST_LINK_SYNC_TIME_FINISHED, 0);
    if (syncTimeFlag == 1) // 如果国标校时了就不使用系统校时
    {
        notify_time_changed = 0;
        notify_time_running = 0;
        return;
    }

    if (notify_time_changed)
    {
        switch (type)
        {
            case TIME_FLAG_MCU_CALC:
            {
                LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "MCU_(%02u-%02u-%02u %02u:%02u:%02u)", pTimeInfo->year, pTimeInfo->month, pTimeInfo->day, \
                                 pTimeInfo->hour, pTimeInfo->minute, pTimeInfo->second);
                if (24 > pTimeInfo->year || 0xFF == pTimeInfo->year) //MCU未经过校准
                {
                    LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "MCU TIME INVALID");
                }
                else
                {
                    snprintf(cmd, sizeof(cmd), "date -s \"%04u-%02u-%02u %02u:%02u:%02u\"", pTimeInfo->year + 2000, pTimeInfo->month, pTimeInfo->day, \
                             pTimeInfo->hour, pTimeInfo->minute, pTimeInfo->second);
                    if(system(cmd)){} //设置系统时间

                    TimeNotifyTimeInfo(pTimeInfo, type);
                    LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "MCU NOTIFY TIME CHANGE CB");
                }
                break;
            }
            case TIME_FLAG_GPS_CALC:
            {
                LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "GPS_(%02u-%02u-%02u %02u:%02u:%02u)", pTimeInfo->year, pTimeInfo->month, pTimeInfo->day, \
                                 pTimeInfo->hour, pTimeInfo->minute, pTimeInfo->second);
                notify_time_changed = 0;
                notify_time_running = 0;

                snprintf(cmd, sizeof(cmd), "date -s \"%04u-%02u-%02u %02u:%02u:%02u\"", pTimeInfo->year + 2000, pTimeInfo->month, pTimeInfo->day, \
                         pTimeInfo->hour, pTimeInfo->minute, pTimeInfo->second);
                if(system(cmd)){} //设置系统时间

                TimeNotifyTimeInfo(pTimeInfo, type);
                LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "GPS NOTIFY TIME CHANGE CB");
                TimeSetMcuTime(pTimeInfo);
                break;
            }
            case TIME_FLAG_MODEM_CALC:
            {
                LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "NTP_(%02u-%02u-%02u %02u:%02u:%02u)", pTimeInfo->year, pTimeInfo->month, pTimeInfo->day, \
                                 pTimeInfo->hour, pTimeInfo->minute, pTimeInfo->second);
                notify_time_changed = 0;
                notify_time_running = 0;

                snprintf(cmd, sizeof(cmd), "date -s \"%04u-%02u-%02u %02u:%02u:%02u\"", pTimeInfo->year + 2000, pTimeInfo->month, pTimeInfo->day, \
                         pTimeInfo->hour, pTimeInfo->minute, pTimeInfo->second);
                if(system(cmd)){} //设置系统时间

                TimeNotifyTimeInfo(pTimeInfo, type);
                LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "NTP NOTIFY TIME CHANGE CB");
                TimeSetMcuTime(pTimeInfo);
                break;
            }
            default:
                break;
        }
    }
}

static void *Oneshot_thread(void *argv)
{
    TimeInfo tinfo;

    TimeGetMcuTime(Oneshotcb);
    TimeConfigInit();
    int gpsRes = 0;

    while (notify_time_changed)
    {
        sleep(5);
        gpsRes = TimeGetGPSTime(&tinfo);
        if (TBOX_OK == gpsRes)
        {
            Oneshotcb(&tinfo, TIME_FLAG_GPS_CALC);
        }
        else
        {
            LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_ERROR, "Get GPS time failed, res=%d", gpsRes);
        }
        TimeGetNTPTime(Oneshotcb);
    }
    return NULL;
}

/*
 * 供握手后快速创建时间服务程序
 */
void TimeCreateOneshotD(void)
{
    int err;
    pthread_t tid;
    pthread_attr_t attr;

    if (!notify_time_running)
    {
        pthread_attr_init(&attr);
        pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
        notify_time_changed = 1;
        mcu_time_calibrated = 0;
        err = pthread_create(&tid, &attr, Oneshot_thread, NULL);
        if (err)
        {
            LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_ERROR, "Time Cal Event %s", strerror(errno));
        }
        else
        {
            LogServiceBuffer(TBOX_MODULE_TIME, TBOX_LOG_INFO, "Time Cal Event Running");
            notify_time_running = 1;
        }
    }
}


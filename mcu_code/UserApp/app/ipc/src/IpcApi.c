/*
      IpcApi.c
描述：此文件主要是IPC任务(MCU与ARM核间通信)具体实现的业务功能，主要是接收事件的解析，分发和处理
     以及发送事件的判断，组包和发送和重发机制
作者：廖勇刚
时间：2016.7.20
*/
#include <stdio.h>
#include <string.h>
#include "LogApi.h"
#include "IpcApi.h"
#include "CanApi.h"
#include "NvApi.h"
#include "UpdateApi.h"
#include "LedApi.h"
#include "CanMsgApi.h" 
#include "CanFtm.h" 
#include "PmApi.h"
#include "event.h"
#include "rlin30.h"
#include "CanClient.h"
#include "BtApi.h"
#include "SystemApi.h"
#include "DiagApi.h"
#include "BLELin.h"
#include "CanFtm.h"
#include "BtTask.h"
#include "printf.h"

/************************外部全局变量****************************/
extern RxQueue              g_taskRxQueue[QUEUE_ID_MAX];
extern TboxSelfConfigPara   g_nvTboxSelfConfigData;
extern CommonInfo           g_commonInfo;
extern GpioInfo             g_gpioPowerOnInfoList[];
extern DtcInfo              g_dtcInfo;
extern StaticDidInfo        g_staticDid;
extern uint32_t             g_osCurrentTickTime;
extern CarInfo              g_carInfo;
extern uint8_t              g_ipcRxData[];
extern uint8_t              g_clientTxInfo[];
extern PmInfo               g_pmInfo;
// extern LinInfo              g_linInfo;
extern NvItemInfo           g_nvInfoMap[NV_MAX_NUMBER];
extern BackupRamInfo        g_backupRamInfo;

/************************全局变量****************************/
static uint8_t  g4ModuleStatusBak = 0xff;
static uint8_t  gnssFixStatusBak = 0xff;
IpcInfo         g_ipcInfo;
bool            g_armresetdisable=false;
static uint8_t g_ServerRequst = 0; 
/************************函数接口声明****************************/
static void IpcMsgRxHeartBeat(uint8_t *para, uint16_t len);
static void IpcMsgRxHandshake(uint8_t *para, uint16_t len);
static void IpcMsgRxArmPmRequest(uint8_t *para, uint16_t len);
static void IpcMsgRxBtCmdRequest(uint8_t *para, uint16_t len);
static void IpcMsgRxArmDtc(uint8_t *para, uint16_t len);
static void IpcMsgRxArmRequestDtc(uint8_t *para, uint16_t len);
static void IpcMsgRxArmSetGpio(uint8_t *para, uint16_t len);
static void IpcMsgRxArmClientSta(uint8_t *para, uint16_t len);
static void IpcMsgRxRemoteControl(uint8_t *para, uint16_t len);
static void IpcMsgRxIdInfo(uint8_t *para, uint16_t len);
static void IpcMsgRxSetRtcAlarm(uint8_t *para, uint16_t len);
static void IpcMsgRxArmDidInfo(uint8_t *para, uint16_t len);
static void IpcMsgRxArmStatus(uint8_t *para, uint16_t len);
static void IpcMsgRxParaConfigCommand(uint8_t *para, uint16_t len);
static void IpcMsgRxPeriodNadStatus(uint8_t *para, uint16_t len);
static void IpcMsgRxCanLogControlCmd(uint8_t *para, uint16_t len);
static void IpcMsgRxSetRtcWakeupTime(uint8_t *para, uint16_t len);
static void IpcMsgRxArmFtmCmd(uint8_t *para, uint16_t len);
static void IpcMsgRxArmPcCmd(uint8_t *para, uint16_t len);
static void IpcMsgRxArmDutCmd(uint8_t *para, uint16_t len);
static void IpcMsgRxRemoteEcuUpgrade(uint8_t *para, uint16_t len);
static void IpcMsgRxVehicleRemoteDiagnosis(uint8_t *para, uint16_t len);
static void IpcMsgRxRemoteUpgradeDownload(uint8_t *para, uint16_t len);
static void IpcMsgRxCheckMicFault(uint8_t *para, uint16_t len);
static void IpcMsgBTMacAddr(uint8_t *para, uint16_t len);
static void IpcMsgRxBleCalibration(uint8_t *para, uint16_t len);
static void IpcMsgRxTspRegisterStatus(uint8_t *para, uint16_t len);
static void IpcMsgRxAppRssiResponse(uint8_t *para, uint16_t len);
static void IpcMsgRxSTBUpdateRequest(uint8_t *para, uint16_t len);
static void IpcMsgRxArmControlResetEnable(uint8_t *para, uint16_t len);
void IpcDataCallback(void *driverState, uart_event_t event, void *userData);
void IpcErrorCallback(void *driverState, uart_event_t event, void *userData);

MessageRxInfo  g_rxMessageInfoMap[MESSAGE_ID_RX_MAX] = 
{
    {MESSAGE_ID_RX_HEARTBEAT,                   MESSAGE_RX_HEARTBEAT,                   FLAG_FIXED_LEN,     {0x0000},                        IpcMsgRxHeartBeat},
    {MESSAGE_ID_RX_HANDSHAKE,                   MESSAGE_RX_HANDSHAKE,                   FLAG_FIXED_LEN,     {0x0000},                        IpcMsgRxHandshake},
    {MESSAGE_ID_RX_REPORT_ARM_DTC,              MESSAGE_RX_REPORT_ARM_DTC,              FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x02}, IpcMsgRxArmDtc},
    {MESSAGE_ID_RX_REPORT_ARM_STATUS,           MESSAGE_RX_REPORT_ARM_STATUS,           FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x03}, IpcMsgRxArmStatus},
    {MESSAGE_ID_RX_ARM_PM_REQUESET,             MESSAGE_RX_ARM_PM_REQUESET,             FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x01}, IpcMsgRxArmPmRequest},
    {MESSAGE_ID_RX_BT_REQUESET,                 MESSAGE_RX_BT_REQUESET,                 FLAG_NOT_FIXED_LEN, {MSG_ATTR_ACK_FLAG_MASK},        IpcMsgRxBtCmdRequest},
    {MESSAGE_ID_RX_TIME_CALIBRATION,            MESSAGE_RX_TIME_CALIBRATION,            FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x07}, IpcMsgRxTimeCalibration},
    {MESSAGE_ID_RX_ID_INFO,                     MESSAGE_RX_ID_INFO,                     FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x71}, IpcMsgRxIdInfo},
    {MESSAGE_ID_RX_SET_RTC_ALARM,               MESSAGE_RX_SET_RTC_ALARM,               FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x05}, IpcMsgRxSetRtcAlarm},
    {MESSAGE_ID_RX_ARM_DID_INFO,                MESSAGE_RX_ARM_DID_INFO,                FLAG_NOT_FIXED_LEN, {MSG_ATTR_ACK_FLAG_MASK},        IpcMsgRxArmDidInfo},
    {MESSAGE_ID_RX_ARM_REQUEST_DTC,             MESSAGE_RX_ARM_REQUEST_DTC,             FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x03}, IpcMsgRxArmRequestDtc},
    {MESSAGE_ID_RX_ARM_SET_GPIO,                MESSAGE_RX_ARM_SET_GPIO,                FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x04}, IpcMsgRxArmSetGpio},
    {MESSAGE_ID_RX_ARM_CLIENTSTA,               MESSAGE_RX_ARM_CLIENTSTA,               FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x01}, IpcMsgRxArmClientSta},
    {MESSAGE_ID_RX_GB_REMOTE_CONTROL,           MESSAGE_RX_REMOTE_CONTROL,              FLAG_NOT_FIXED_LEN, {MSG_ATTR_ACK_FLAG_MASK},        IpcMsgRxRemoteControl},
    {MESSAGE_ID_RX_PARA_CONFIG_CMD,             MESSAGE_RX_PARA_CONFIG_CMD,             FLAG_NOT_FIXED_LEN, {MSG_ATTR_ACK_FLAG_MASK},        IpcMsgRxParaConfigCommand}, 
    {MESSAGE_ID_RX_PERIOD_VEHICLE_REPORT,       MESSAGE_RX_PERIOD_VEHICLE_REPORT,       FLAG_NOT_FIXED_LEN, {MSG_ATTR_ACK_FLAG_MASK | 0x0a}, IpcMsgRxPeriodNadStatus},
    {MESSAGE_ID_RX_CHECK_MIC_FAULT,             MESSAGE_RX_CHECK_MIC_FAULT,             FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x01}, IpcMsgRxCheckMicFault},
    {MESSAGE_ID_RX_VEHICLE_REMOTE_DIAGNOSIS,    MESSAGE_RX_VEHICLE_REMOTE_DIAGNOSIS,    FLAG_NOT_FIXED_LEN, {MSG_ATTR_ACK_FLAG_MASK},        IpcMsgRxVehicleRemoteDiagnosis},
    {MESSAGE_ID_REMOTE_UPGRADE_DOWNLOAD,        MESSAGE_RX_REMOTE_UPGRADE_DOWNLOAD,     FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x06}, IpcMsgRxRemoteUpgradeDownload}, 
    {MESSAGE_ID_CAN_LOG_CONTROL_COMMAND,        MESSAGE_RX_CAN_LOG_CONTROL_COMMAND,     FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x01}, IpcMsgRxCanLogControlCmd},
    {MESSAGE_ID_SET_WAKEUP_TIME,                MESSAGE_RX_SET_WAKEUP_TIME,             FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x05}, IpcMsgRxSetRtcWakeupTime},
    {MESSAGE_ID_RX_ARM_FTM_COMMAND,             MESSAGE_RX_ARM_FTM_COMMAND,             FLAG_NOT_FIXED_LEN, {MSG_ATTR_ACK_FLAG_MASK | 0x01}, IpcMsgRxArmFtmCmd},
    {MESSAGE_ID_RX_ARM_FTM_PC_COMMAND,          MESSAGE_RX_ARM_FTM_PC_COMMAND,          FLAG_NOT_FIXED_LEN, {MSG_ATTR_ACK_FLAG_MASK | 0x01}, IpcMsgRxArmPcCmd},
    {MESSAGE_ID_RX_ARM_FTM_DUT_COMMAND,         MESSAGE_RX_ARM_FTM_DUT_COMMAND,         FLAG_NOT_FIXED_LEN, {MSG_ATTR_ACK_FLAG_MASK | 0x01}, IpcMsgRxArmDutCmd}, 
    {MESSAGE_ID_RX_ARM_CONTROL_ARM_RESET,       MESSAGE_RX_ARM_CONTROL_ARM_RESET,       FLAG_FIXED_LEN,     {MSG_ATTR_ACK_FLAG_MASK | 0x01}, IpcMsgRxArmControlResetEnable},
};

MessageTxInfo g_txMessageInfoMap[MESSAGE_ID_TX_MAX] = 
{
    {MESSAGE_ID_TX_MCU_STATUS,                 MESSAGE_TX_MCU_STATUS,                FLAG_NOT_WATI_ACK, FLAG_NOT_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x0F},
    {MESSAGE_ID_TX_PM_REQUEST,                 MESSAGE_TX_PM_REQUEST,                FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_ECALL_REQUEST,              MESSAGE_TX_ECALL_REQUEST,             FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_POWER_ON_REPORT,            MESSAGE_TX_POWER_ON_REPORT,           FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x07},
    {MESSAGE_ID_TX_BT_RX_DATA,                 MESSAGE_TX_BT_RX_DATA,                FLAG_WAIT_ACK,     FLAG_NOT_FIXED_LEN,     NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_WAKEUP_INFO,                MESSAGE_TX_WAKEUP_INFO,               FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x03},
    {MESSAGE_ID_TX_TIME_CALIBRATION,           MESSAGE_TX_TIME_CALIBRATION,          FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x07},
    {MESSAGE_ID_TX_CAR_INFO,                   MESSAGE_TX_CAR_INFO,                  FLAG_WAIT_ACK,     FLAG_NOT_FIXED_LEN,     NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x78},
    {MESSAGE_ID_TX_TBOX_HARD_CONFIG,           MESSAGE_TX_TBOX_HARD_CONFIG,          FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_DID_INFO,                   MESSAGE_TX_DID_INFO,                  FLAG_WAIT_ACK,     FLAG_NOT_FIXED_LEN,     NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_TBOX_DTC_RESPONSE,          MESSAGE_TX_TBOX_DTC_RESPONSE,         FLAG_WAIT_ACK,     FLAG_NOT_FIXED_LEN,     NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_ARM_CAN_NET_NOTIFY,         MESSAGE_TX_ARM_CAN_NET_NOTIFY,        FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_REMOTE_CONTROL_RESPONSE,    MESSAGE_TX_REMOTE_CONTROL_RESPONSE,   FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x02},
    {MESSAGE_ID_TX_MCU_LOG,                    MESSAGE_TX_MCU_LOG,                   FLAG_NOT_WATI_ACK, FLAG_NOT_FIXED_LEN,     NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_MCU_SECURITY_EVENT,         MESSAGE_TX_MCU_SECURITY_EVENT,        FLAG_WAIT_ACK,     FLAG_NOT_FIXED_LEN,     NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_PARA_CONFIG_RESPONSE,       MESSAGE_TX_PARA_CONFIG_RESPONSE,      FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_CAN_PARA_REPORT,            MESSAGE_TX_CAN_PARA_REPORT,           FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, CAN_MSG_RT_INFO_LENGTH},
    {MESSAGE_ID_TX_MCU_FAULT_INFO,             MESSAGE_TX_MCU_FAULT_INFO,            FLAG_WAIT_ACK,     FLAG_NOT_FIXED_LEN,     NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_VEHICLE_REMOTE_DIAGNOSIS,   MESSAGE_TX_VEHICLE_REMOTE_DIAGNOSIS,  FLAG_WAIT_ACK,     FLAG_NOT_FIXED_LEN,     NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
    {MESSAGE_ID_TX_REMOTE_UPGRADE_RESPONSE,    MESSAGE_TX_REMOTE_UPGRADE_RESPONSE,   FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x04},
    {MESSAGE_ID_TX_UPDATE_CODE_RESPONSE,       MESSAGE_TX_UPDATE_CODE_RESPONSE,      FLAG_WAIT_ACK,     FLAG_FIXED_LEN,         NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x03},
    {MESSAGE_ID_TX_ARM_FTM_CMD,                MESSAGE_TX_ARM_FTM_CMD,               FLAG_WAIT_ACK,     FLAG_NOT_FIXED_LEN,     NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
};

uint8_t CheckSleepRequestFromServer(void)
{
    return g_ServerRequst ;
}

void CleanServerSleepRequest(void)
{
    g_ServerRequst = 0;
}

void SetServerSleepRequset(uint8_t sleepLevel)
{
    g_ServerRequst = sleepLevel;
    if(sleepLevel == 2)
    {

    }
}


/*************************************************
函数名称: IpcInitRamData
函数功能: 初始化IPC任务全局变量
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/27
*************************************************/
void IpcInitRamData(void)
{
    g_commonInfo.handshakeStatus = HANDSHAKE_FAIL;
    g_commonInfo.gnssIsOnline = GNSS_IS_OFFLINE;
    g_commonInfo.armClinetStatus = ARM_CLINET_OFFLINE;
    g_commonInfo.heartFailCount  = 0;
    g_commonInfo.armFailCount    = 0;
    g_commonInfo.powerOnReportStatus = POWER_ON_REPORT_IDLE;
    g_commonInfo.intoInterrupt = NOT_IN;
    g_commonInfo.canSendOutStat = CAN_SEND_OUT_ENABLE;
    g_commonInfo.udsCtrlNmPduStatus = 0x00;
    g_commonInfo.udsCtrlNormPduStatus = 0x00;
    g_commonInfo.bleKeyAuthStatus = 0x00;

    g_commonInfo.RemoteUpgradeFlag = Remote_UpGrade_End;

    g_ipcInfo.ackFlag = FLAG_NOT_WATI_ACK;
    g_ipcInfo.txAck = FLAG_NOT_WATI_ACK;
    g_ipcInfo.retransmissionTimeout = 0;
    g_ipcInfo.retransmissionCount = 0;
    g_ipcInfo.heartbeatFlag = FLAG_NOT_RX_HEARTBEAT;
    g_ipcInfo.heartbeatTimeout = 0;
    g_ipcInfo.handshakeTimeout = 0;
    g_ipcInfo.rxStatus = STATUS_RX_IDLE;
    g_ipcInfo.txTid = 1;
    g_ipcInfo.rxTid = 1;
    g_ipcInfo.ipcTx.len = 0;
    memset(g_ipcInfo.ipcTx.buffer, 0x00, sizeof(g_ipcInfo.ipcTx.buffer));
    g_ipcInfo.uartLen = 0;
    memset(g_ipcInfo.uartBuf, 0x00, sizeof(g_ipcInfo.uartBuf));
    g_commonInfo.ecallStatus = ECALL_STATUS_INACTIVE;
    McuUartIPCInit(IpcDataCallback, IpcErrorCallback);
}

/*************************************************
函数名称: IpcTxWakeupStatus
函数功能: 发送MCU唤醒相关信息
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/07/06
*************************************************/
void IpcTxWakeupStatus(void)
{
    uint8_t tempBuffer[3];
    Msg msg;

    tempBuffer[0] = g_backupRamInfo.mcuWakeupSource;
    tempBuffer[1] = g_commonInfo.heartFailCount;
    tempBuffer[2] = g_commonInfo.armFailCount;

    msg.event  = MESSAGE_TX_WAKEUP_INFO;
    msg.len    = 3;
    msg.lparam = (uint32_t)&tempBuffer[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: IpcMsgRxSTBUpdateRequest
函数功能: 接收到STB升级请求
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: ben
编写日期 :2019/03/21
*************************************************/
// static void IpcMsgRxSTBUpdateRequest(uint8_t *para, uint16_t len)
// {
//     LinIpcRxUpdateHandle(para, len);
// }
/*************************************************
函数名称: IpcMsgRxSTBCalibrationRequest
函数功能: 接收到STB标定请求
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: ben
编写日期 :2019/02/01
*************************************************/
// static void IpcMsgRxSTBCalibrationRequest(uint8_t *para, uint16_t len)
// {
//     LinIpcRxCalibrationHandle(para, len);
// }

/*************************************************
函数名称: IpcTxCarInfo
函数功能: 发送MCU汽车信息
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/07/06
*************************************************/
static void IpcTxCarInfo(void)
{
    uint8_t tempBuffer[700] = {0};
    uint16_t len = 0x00;
    Msg msg;

    len = EncodeTotalDid(tempBuffer);  //五菱项目用来上电后同步全部DID

    msg.event  = MESSAGE_TX_CAR_INFO;
    msg.len    = len;
    msg.lparam = (uint32_t)&tempBuffer[0];
    int ret = SystemSendMessage(TASK_ID_IPC, msg);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu sync did to arm. len = %u,ret = %u\r\n",len,ret);
}

/*************************************************
函数名称: IpcMsgRxBleCalibration
函数功能: 接收到ble标定信息
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: benyulong
编写日期 :2019/05/23
*************************************************/
static void IpcMsgRxBleCalibration(uint8_t *para, uint16_t len)
{
}

/*************************************************
函数名称: IpcTboxHardConfigInfo
函数功能: 发送IpcTboxHardConfigInfo 硬件配置信息
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/07/06
*************************************************/
static void IpcTboxHardConfigInfo(void)
{
    uint8_t tempBuffer[4];
    Msg msg;

    /*发送给ARM端当前是否支持外置惯导的配置，不关系SIM卡的套餐配置 带惯导传0 不带惯导传1 ARM会根据这个选择GPS类型*/
    //tempBuffer[0] = (g_carInfo.tboxCfgType & TBOX_CFG_WITH_NAV) ? 0x00 : 0x01;
    TboxSelfConfigPara *tboxSelfConfigPara = GetTboxSelfConfigData();
    tempBuffer[0] = (tboxSelfConfigPara->tboxCfgType);

    msg.event  = MESSAGE_TX_TBOX_HARD_CONFIG;
    msg.len    = 0x01;
    msg.lparam = (uint32_t)&tempBuffer[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}


/*************************************************
函数名称: IpcTboxNotifArmEnterFtm
函数功能: 通知ARM进入装备模式
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/08/14
*************************************************/
static void IpcTboxNotifArmEnterFtm(void)
{
    uint8_t tempBuffer[4];
    Msg msg;

    tempBuffer[0] = FTM_RX_MCU_MODE_INDEX;
    tempBuffer[1] = FTM_MODE_ENTER;
    msg.event  = MESSAGE_TX_ARM_FTM_CMD;
    msg.len    = 2;
    msg.lparam = (uint32_t)&tempBuffer[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: IpcMsgRxHandshake
函数功能: MCU接收到ARM握手信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/02
*************************************************/
static void IpcMsgRxHandshake(uint8_t *para, uint16_t len)
{    
    uint8_t tempBuffer[7];
    Msg msg;

    g_commonInfo.armClinetStatus = ARM_CLINET_OFFLINE;
    g_commonInfo.gnssIsOnline = GNSS_IS_OFFLINE;
    g_commonInfo.handshakeStatus = HANDSHAKE_SUCESS;
    g_ipcInfo.heartbeatFlag = FLAG_RX_HEARTBEAT;
    g_ipcInfo.heartbeatTimeout = 0;
    g_ipcInfo.handshakeTimeout = 0;

    uint32_t bootVersion = ReadValueOfFlashAddress(BOOT_VERSION_ADDR);

    tempBuffer[0] = (uint8_t)MCU_HR_MAIN_VERSION;
    tempBuffer[1] = (uint8_t)MCU_HR_SUB_VERSION;
    tempBuffer[2] = (uint8_t)MCU_SOFT_MAIN_VERSION;
    tempBuffer[3] = (uint8_t)MCU_SOFT_SUB_VERSION;
    tempBuffer[4] = (uint8_t)MCU_SOFT_PATCH_VERSION;

    if ((bootVersion & 0xFFFF) != 0xFFFF)
    {
        tempBuffer[5] = (bootVersion >> 8) & 0xff;
        tempBuffer[6] = bootVersion & 0xff;
    }
    else
    {
        tempBuffer[5] = 0;
        tempBuffer[6] = 0;
    }

    msg.event  = MESSAGE_TX_POWER_ON_REPORT;
    msg.len    = 7;
    msg.lparam = (uint32_t)&tempBuffer[0];
    SystemSendMessage(TASK_ID_IPC, msg);
    g_commonInfo.powerOnReportStatus = POWER_ON_REPORT_WAKEUP_STATUS;


    g_pmInfo.workStatus = PM_STATUS_NORAML;

    if (GetCheckDtcEnable(MASK_VOL | MASK_ACC | MASK_BUS_OFF))
    {
        DiagModuleFailureCheck(DTC_DEVICE_NORMAL_STATUS);
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ipc handshake.\r\n");

}

/*************************************************
函数名称: IpcMsgRxHeartBeat
函数功能: 接收到心跳报文处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
static void IpcMsgRxHeartBeat(uint8_t *para, uint16_t len)
{
    static uint8_t count = 0;
    if (g_commonInfo.handshakeStatus != HANDSHAKE_SUCESS)
    {
        count++;
        if (count >= 3)
        {
            count = 0;
            IpcMsgRxHandshake(para,len);
        }
    }
    else
    {
        count = 0;
    }

    if(FLAG_NOT_RX_HEARTBEAT == g_ipcInfo.heartbeatFlag)
    {
        g_ipcInfo.heartbeatFlag = FLAG_RX_HEARTBEAT;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "ipc heartbeat resume\r\n");
    }

    g_ipcInfo.heartbeatTimeout = 0;

    if (GetCheckDtcEnable(MASK_VOL | MASK_ACC | MASK_BUS_OFF))
    {
        DiagModuleFailureCheck(DTC_DEVICE_NORMAL_STATUS);
    }
}

/*************************************************
函数名称: IpcMsgRxArmDtc
函数功能: MCU接收到ARM的故障信息
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/04
*************************************************/
static void IpcMsgRxArmDtc(uint8_t *para, uint16_t len)
{
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Ipc Msg Rx Arm Dtc id 0x%X status:0x%02x\r\n",para[0],para[1]);
    DiagWriteDevicesStatus(para[0], para[1]);

    if(DTC_GNSS_MODULE_FAULT_INDEX == para[0])
    {
        g_commonInfo.gnssIsOnline = (GnssOnlineStatus)para[1];
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "GNSS module status:0x%02x\r\n", g_commonInfo.gnssIsOnline);
    }
}

/*************************************************
函数名称: IpcMsgRxArmStatus
函数功能: 接收到ARM状态请求消息处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/26
*************************************************/
static void IpcMsgRxArmStatus(uint8_t *para, uint16_t len)
{
    Msg msg;
    
    msg.event = EVENT_ID_ARM_STATUS;
    msg.len   = 2;
    msg.lparam = (uint32_t)para;
    SystemSendMessage(TASK_ID_PM, msg);
}

/*************************************************
函数名称: IpcMsgRxArmPmRequest
函数功能: 接收到ARM状态请求消息处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/26
*************************************************/
static void IpcMsgRxArmPmRequest(uint8_t *para, uint16_t len)
{
    Msg msg;
    PmStatus pmStatus = PM_STATUS_ENTER_NORAML_SLEEP;

    /*表示重新下电和上电ARM和4G模块的电源*/
    if(0x00 == para[0])
    {
        msg.event = MESSAGE_TX_PM_REQUEST;
        msg.len   = 1;
        msg.lparam = (uint32_t)&pmStatus;
        SystemSendMessage(TASK_ID_IPC, msg);

        msg.event  = EVENT_ID_HEARTBEAT_TIMEOUT;
        msg.len    = 0;
        msg.lparam = 0;
        SystemSendMessage(TASK_ID_PM, msg);

        g_ipcInfo.heartbeatFlag = FLAG_NOT_RX_HEARTBEAT;

        #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "arm request mcu restart\r\n"); 
        #endif
        g_commonInfo.armFailCount++;
    }
    else if(0x01 == para[0])      /*表示通知MCU进入浅度睡眠*/
    {
        SetServerSleepRequset(1);
    }
    else                          /*表示通知MCU进入深度睡眠*/
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "arm request mcu enter deep sleep\r\n"); 
        SetServerSleepRequset(2);
    }
}

/*************************************************
函数名称: IpcMsgRxBtCmdRequest
函数功能: 接收到BT请求消息处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/26
*************************************************/
static void IpcMsgRxBtCmdRequest(uint8_t *para, uint16_t len)
{
    FtmInfo     *pFtmInfo = FtmInitRead();

    if(FTM_MODE_EXIT == pFtmInfo->ftmMode)
    {
#if (1 == BT_TASK_ENABLED)
        BtModulePassReqTxEvent(para, len);
#endif
    }
}

/*************************************************
函数名称: IpcMsgRxTimeCalibration
函数功能: 接收ARM 时间校准
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/02/13
*************************************************/
void IpcMsgRxTimeCalibration(uint8_t *para, uint16_t len)
{
    Msg msg;
    RtcTime rtcTime;
    uint8_t tempBuffer[7] = {0xFF};
    TboxSystemTimeStruct *pTimeStruct = NULL;
    
    pTimeStruct = TboxSystemTimeInfoRead();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "arm sync time para[0-7] %02d-%02d-%02d %02d:%02d:%02d %02d \r\n",para[0],para[1],para[2],para[3],para[4],para[5],para[6]);
    if(para[0] != 0xff)
    {
        TboxSystemTimeSet(para[6], para[5], para[4], para[3], para[2], para[1], para[0], NET_TYPE_TIME);
    }
    else
    {
        if(true == pTimeStruct->currentTime.timeIsValid)
        {
            RtcGetDateTime(&rtcTime);
            tempBuffer[0]  = rtcTime.second;
            tempBuffer[1]  = rtcTime.minute;
            tempBuffer[2]  = rtcTime.hour;
            tempBuffer[3]  = rtcTime.day;
            tempBuffer[4]  = rtcTime.weekday;
            tempBuffer[5]  = rtcTime.month;
            tempBuffer[6]  = (uint8_t)rtcTime.year - 2000;// 云途MCU的RTC时间年是2字节, 存的是完整的年
        }
        else
        {
            memset(tempBuffer, 0xFF, sizeof(tempBuffer));
        }

        msg.event  = MESSAGE_TX_TIME_CALIBRATION;
        msg.len    = sizeof(tempBuffer);
        msg.lparam = (uint32_t)&tempBuffer[0];
        SystemSendMessage(TASK_ID_IPC, msg);
    }
}

/*************************************************
函数名称: IpcMsgRxIdInfo
函数功能: 接收ARM 4G和GNSS相关ID信息
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/03/28
*************************************************/
static void IpcMsgRxIdInfo(uint8_t *para, uint16_t len)
{
    uint8_t count = 0;

    if(NULL == para)
    {
        return;
    }

    memcpy(g_commonInfo.simNo, para, SIM_NUNBER_LEN);
    count = SIM_NUNBER_LEN;
    memcpy(g_commonInfo.simImsi, para + count, SIM_IMSI_LEN);
    count = count + SIM_IMSI_LEN;
    memcpy(g_commonInfo.simIccid, para + count, SIM_ICCID_LEN);
    count = count + SIM_ICCID_LEN;
    memcpy(g_commonInfo.g4Id, para + count, G4_MODULE_ID_LEN);
    count = count + G4_MODULE_ID_LEN;
    memcpy(g_commonInfo.gnssId, para + count, GNSS_MODULE_ID_LEN);
}


/*************************************************
函数名称: IpcMsgRxSetRtcAlarm
函数功能: 接收ARM 设置RTC定时时间
输入参数: 参数指针和长度
          时间参数为BCD码格式
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/03/15
*************************************************/
static void IpcMsgRxSetRtcAlarm(uint8_t *para, uint16_t len)
{

}

/*************************************************
函数名称: IpcMsgRxArmDidInfo
函数功能: 接收ARM DID相关信息
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/06/22
*************************************************/
static void IpcMsgRxArmDidInfo(uint8_t *para, uint16_t len)
{
    if(NULL == para)
    {
        return;
    }

    bool writeCarFlag = false;
    bool writeStaticFlag = false;
    bool writeStatic2Flag = false;
    DidRwReult result = DID_RW_RESULT_FAIL;

    uint8_t didReadBuf[128] = {0x00};
    uint8_t didReadBufLen = 0x00;
    NvErrorCode errorCode = NV_NO_ERROR;

    Msg msg;

    DidRwType type = para[0];
    uint16_t did = para[1] << 8 | para[2];

    uint8_t *readBuf = &didReadBuf[4];
    uint8_t *writeBuf = &para[3];

    StaticDidInfo staticDid = {0};
    StaticDid2Info staticDid2 = {0};
    CarInfo carInfo = {0};
    memcpy((uint8_t*)&staticDid, (uint8_t*)GetStaticDid(), sizeof(StaticDidInfo));
    memcpy((uint8_t*)&staticDid2, (uint8_t*)GetStaticDid2(), sizeof(StaticDid2Info));
    memcpy((uint8_t*)&carInfo, (uint8_t*)GetCanInfo(), sizeof(CarInfo));

    DynamicDidInfo *dynamicDidInfo = GetDynamicDid();

    switch(did)
    {
        case DID_CTRL_MCU_GPIO:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] < MCU_MAX_NUM && writeBuf[1] < GPIO_DEFAULT_LEVEL)
                {
                    GpioSetOutputLevel(&g_gpioPowerOnInfoList[writeBuf[0]], writeBuf[1]);
                    result = DID_RW_RESULT_SUCCESS;
                }
                else
                {
                    result = DID_RW_RESULT_FAIL;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                result = DID_RW_RESULT_FAIL;
            }
            break;
        }
        case DID_UDS_STMIN:
        {
            if(DID_WRITE_TYPE == type)
            {
                result = SetUdsSTmin(writeBuf[0]) == MCU_OK ? DID_RW_RESULT_SUCCESS : DID_RW_RESULT_FAIL;
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = g_nvTboxSelfConfigData.STmin;
                didReadBufLen = 1;
                result = DID_RW_RESULT_SUCCESS;
            }
            break;
        }
        case DID_CAR_INFO_DID:
        {
            if(DID_WRITE_TYPE == type)
            {
                DecodeAndSetCarDid(&carInfo,writeBuf);
                result = DID_RW_RESULT_SUCCESS;
                writeCarFlag = true;
            }
            else if(DID_READ_TYPE == type)
            {
                result = DID_RW_RESULT_FAIL;
            }
            break;
        }
        case DID_STATIC_DID:
        {
            if(DID_WRITE_TYPE == type)
            {
                DecodeAndSetStaticDid(&staticDid,writeBuf);
                result = DID_RW_RESULT_SUCCESS;
                writeStaticFlag = true;
            }
            else if(DID_READ_TYPE == type)
            {
                result = DID_RW_RESULT_FAIL;
            }
            break;
        }
        case DID_STATIC2_DID:
        {
            if(DID_WRITE_TYPE == type)
            {
                DecodeAndSetStaticDid2(&staticDid2,writeBuf);
                result = DID_RW_RESULT_SUCCESS;
                writeStatic2Flag = true;
            }
            else if(DID_READ_TYPE == type)
            {
                result = DID_RW_RESULT_FAIL;
            }
            break;
        }
        case DID_DYNAMIC_DID:
        {
            if(DID_WRITE_TYPE == type)
            {
                DcodeAndSetDynamicDid(writeBuf);
                result = DID_RW_RESULT_SUCCESS;
                return; //动态数据不需要响应
            }
            else if(DID_READ_TYPE == type)
            {
                result = DID_RW_RESULT_FAIL;
            }
            break;
        }




        case DID_TBOX_SPARE_PART_NUMBER:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(carInfo.tboxSparePartNumber, writeBuf, TBOX_SPARE_PARTNUMBER_LEN) != 0)
                {
                    memcpy(carInfo.tboxSparePartNumber, writeBuf, TBOX_SPARE_PARTNUMBER_LEN);
                    writeCarFlag = true;
                }
                result = DID_RW_RESULT_SUCCESS;
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf, carInfo.tboxSparePartNumber, TBOX_SPARE_PARTNUMBER_LEN);
                didReadBufLen = TBOX_SPARE_PARTNUMBER_LEN;
                result = DID_RW_RESULT_SUCCESS;
            }
            break;
        }

        /* VIN码 */
        case DID_VIN_CODE:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(carInfo.vinCode, writeBuf, TBOX_VIN_LEN) != 0)
                {
                    memcpy(carInfo.vinCode, writeBuf, TBOX_VIN_LEN);
                    writeCarFlag = true;
                }
                result = DID_RW_RESULT_SUCCESS;
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf, carInfo.vinCode, TBOX_VIN_LEN);
                didReadBufLen = TBOX_VIN_LEN;
                result = DID_RW_RESULT_SUCCESS;
            }
            break;
        }
            /* 软件更新日期 */
        case DID_SOFT_UPDATE_DATE:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(carInfo.softUpdateDate, writeBuf, TBOX_SOFTWARE_UPDATE_DATE_LEN) != 0)
                {
                    memcpy(carInfo.softUpdateDate, writeBuf, TBOX_SOFTWARE_UPDATE_DATE_LEN);
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,carInfo.softUpdateDate, TBOX_SOFTWARE_UPDATE_DATE_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_SOFTWARE_UPDATE_DATE_LEN;
            }
            break;
        }
        case DID_TBOX_ID:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(carInfo.tboxId, writeBuf, TBOX_ID_LEN) != 0)
                {
                    memcpy(carInfo.tboxId, writeBuf, TBOX_ID_LEN);
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,carInfo.tboxId, TBOX_ID_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_ID_LEN;
            }
            break;
        }
        case DID_TUID:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(carInfo.tuid, writeBuf, TUID_LEN) != 0)
                {
                    memcpy(carInfo.tuid, writeBuf, TUID_LEN);
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,carInfo.tuid, TUID_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TUID_LEN;
            }
            break;
        }
            /* TBOX 登签认证状态 */
        case DID_TBOX_AUTH_STATUS:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (carInfo.tboxAuthStatus != writeBuf[0])
                {
                    carInfo.tboxAuthStatus = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.tboxAuthStatus;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }

        case DID_SIM_NUMBER:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(carInfo.simNumber, writeBuf, TBOX_SIM_NUMBER_LEN) != 0)
                {
                    memcpy(carInfo.simNumber, writeBuf, TBOX_SIM_NUMBER_LEN);
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf, carInfo.simNumber, TBOX_SIM_NUMBER_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_SIM_NUMBER_LEN;
            }
            break;
        }
        case DID_ICCID:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(carInfo.iccid, writeBuf, TBOX_ICCID_LEN) != 0)
                {
                    memcpy(carInfo.iccid, writeBuf, TBOX_ICCID_LEN);
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf, carInfo.iccid, TBOX_ICCID_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_ICCID_LEN;
            }
            break;
        }
        case DID_IMEI:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(carInfo.imei, writeBuf, TBOX_IMEI_LEN) != 0)
                {
                    memcpy(carInfo.imei, writeBuf, TBOX_IMEI_LEN);
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf, carInfo.imei, TBOX_IMEI_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_IMEI_LEN;
            }
            break;
        }

        case DID_WAKEUP_TIME_INTERVAL:
        {
            if(DID_WRITE_TYPE == type)
            {
                uint32_t wakeupTimeInterval = writeBuf[0] << 24 | writeBuf[1] << 16 | writeBuf[2] << 8 | writeBuf[3];
                if (wakeupTimeInterval != carInfo.wakeupTimeInterval)
                {
                    carInfo.wakeupTimeInterval = wakeupTimeInterval;
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                uint32_t value = g_carInfo.wakeupTimeInterval;
                readBuf[0] = value >> 24;
                readBuf[1] = value >> 16;
                readBuf[2] = value >> 8;
                readBuf[3] = value ;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 4;
            }
            break;
        }
        case DID_AUTO_RECHARGE_UNDER_VOLTAGE:
        {
            if(DID_WRITE_TYPE == type)
            {
                uint32_t AutoRechargeUnderVoltage = writeBuf[0] << 24 | writeBuf[1] << 16 | writeBuf[2] << 8 | writeBuf[3];
                if (AutoRechargeUnderVoltage != carInfo.AutoRechargeUnderVoltage)
                {
                    carInfo.AutoRechargeUnderVoltage = AutoRechargeUnderVoltage;
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                uint32_t value = g_carInfo.AutoRechargeUnderVoltage;
                readBuf[0] = value >> 24;
                readBuf[1] = value >> 16;
                readBuf[2] = value >> 8;
                readBuf[3] = value ;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 4;
            }
            break;
        }
        case DID_LINK1_SSL_ENABLE:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.link1SSLEnable)
                {
                    carInfo.link1SSLEnable = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.link1SSLEnable;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        case DID_LINK2_SSL_ENABLE:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.link2SSLEnable)
                {
                    carInfo.link2SSLEnable = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.link2SSLEnable;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        case DID_OFFLINE_CHECK_FLAG:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.OfflineCheckFlag)
                {
                    carInfo.OfflineCheckFlag = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.OfflineCheckFlag;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        case DID_AC_TIMEOUT_MINUTES:
        {
            if(DID_WRITE_TYPE == type)
            {
                uint32_t acTimeoutMinutes = writeBuf[0] << 24 | writeBuf[1] << 16 | writeBuf[2] << 8 | writeBuf[3];
                if (acTimeoutMinutes != carInfo.acTimeoutMinutes)
                {
                    carInfo.acTimeoutMinutes = acTimeoutMinutes;
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                uint32_t value = g_carInfo.acTimeoutMinutes;
                readBuf[0] = value >> 24;
                readBuf[1] = value >> 16;
                readBuf[2] = value >> 8;
                readBuf[3] = value ;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 4;
            }
            break;
        }
        case DID_TIMED_WAKEUP_ENABLE:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.timedWakeupEnable)
                {
                    carInfo.timedWakeupEnable = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.timedWakeupEnable;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        case DID_NETWORK_WAKEUP_ENABLE:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.networkWakeupEnable)
                {
                    carInfo.networkWakeupEnable = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.networkWakeupEnable;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        case DID_GLOBAL_LOG_ENABLE:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.globalLogEnable)
                {
                    carInfo.globalLogEnable = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.globalLogEnable;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        case DID_LINK1_ENABLE:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.link1Enable)
                {
                    carInfo.link1Enable = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.link1Enable;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        case DID_LINK2_ENABLE:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.link2Enable)
                {
                    carInfo.link2Enable = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.link2Enable;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        case DID_LINK3_ENABLE:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.link3Enable)
                {
                    carInfo.link3Enable = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.link3Enable;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        case DID_THIRD_PARTY_LINK_ENABLE:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.thirdPartyLinkEnable)
                {
                    carInfo.thirdPartyLinkEnable = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.thirdPartyLinkEnable;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        case DID_CERT_UPDATE_TIME:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(carInfo.certUpdateTime, writeBuf, CERT_UPDATE_TIME_LEN) != 0)
                {
                    memcpy(carInfo.certUpdateTime, writeBuf, CERT_UPDATE_TIME_LEN);
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf, carInfo.certUpdateTime, CERT_UPDATE_TIME_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = CERT_UPDATE_TIME_LEN;
            }
            break;
        }
        case DID_DATA_RESEND_TEST_TIME:
        {
            if(DID_WRITE_TYPE == type)
            {
                uint16_t dataResendTestTime = (writeBuf[0] << 8 | writeBuf[1]);
                if (carInfo.dataResendTestTime != dataResendTestTime)
                {
                    carInfo.dataResendTestTime = dataResendTestTime;
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.dataResendTestTime >> 8;
                readBuf[1] = carInfo.dataResendTestTime;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = sizeof(uint16_t);
            }
            break;
        }
        case DID_LEVEL3_ALARM_TEST_TIME:
        {
            if(DID_WRITE_TYPE == type)
            {
                uint16_t level3AlarmTestTime = (writeBuf[0] << 8 | writeBuf[1]);
                if (carInfo.level3AlarmTestTime != level3AlarmTestTime)
                {
                    carInfo.level3AlarmTestTime = level3AlarmTestTime;
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.level3AlarmTestTime >> 8;
                readBuf[1] = carInfo.level3AlarmTestTime;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = sizeof(uint16_t);
            }
            break;
        }
        // 锁车状态
        case DID_LOCK_CAR_STATUS:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.lockCarStatus)
                {
                    carInfo.lockCarStatus = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.lockCarStatus;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        // 限速状态
        case DID_SPEED_LIMIT_STATUS:
        {
            if (DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.speedLimitStatus)
                {
                    carInfo.speedLimitStatus = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if (DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.speedLimitStatus;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }
        // 限速值
        case DID_SPEED_LIMIT_VALUE:
        {
            if (DID_WRITE_TYPE == type)
            {
                if (writeBuf[0] != carInfo.speedLimitValue)
                {
                    carInfo.speedLimitValue = writeBuf[0];
                    writeCarFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if (DID_READ_TYPE == type)
            {
                readBuf[0] = carInfo.speedLimitValue;
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = 1;
            }
            break;
        }








            /* 证书编号 */
        case DID_TBOX_CERTIFICATE_ID:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid.tboxCertificateID, writeBuf, TBOX_CERT_LEN) != 0)
                {
                    memcpy(staticDid.tboxCertificateID, writeBuf, TBOX_CERT_LEN);
                    writeStaticFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf, staticDid.tboxCertificateID, TBOX_CERT_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_CERT_LEN;
            }
            break;
        }

            /* TBOX 第 1 链路地址 */
        case DID_LINK1_ADDR:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid.link1Addr, writeBuf, TBOX_LINK_LEN) != 0)
                {
                    memcpy(staticDid.link1Addr, writeBuf, TBOX_LINK_LEN);
                    writeStaticFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,staticDid.link1Addr, TBOX_LINK_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_LINK_LEN;
            }
            break;
        }
            /* TBOX 第 1 链路地址端口 */
        case DID_LINK1_PORT:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid.link1Port, writeBuf, TBOX_LINK_PORT_LEN) != 0)
                {
                    memcpy(staticDid.link1Port, writeBuf, TBOX_LINK_PORT_LEN);
                    writeStaticFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,staticDid.link1Port, TBOX_LINK_PORT_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_LINK_PORT_LEN;
            }
            break;
        }
            /* TBOX 第 2 链路地址 */
        case DID_LINK2_ADDR:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid.link2Addr, writeBuf, TBOX_LINK_LEN) != 0)
                {
                    memcpy(staticDid.link2Addr, writeBuf, TBOX_LINK_LEN);
                    writeStaticFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,staticDid.link2Addr, TBOX_LINK_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_LINK_LEN;
            }
            break;
        }
            /* TBOX 第 2 链路地址端口 */
        case DID_LINK2_PORT:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid.link2Port, writeBuf, TBOX_LINK_PORT_LEN) != 0)
                {
                    memcpy(staticDid.link2Port, writeBuf, TBOX_LINK_PORT_LEN);
                    writeStaticFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,staticDid.link2Port, TBOX_LINK_PORT_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_LINK_PORT_LEN;
            }
            break;
        }
            /* TBOX 第 3 链路地址 */
        case DID_LINK3_ADDR:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid.link3Addr, writeBuf, TBOX_LINK_LEN) != 0)
                {
                    memcpy(staticDid.link3Addr, writeBuf, TBOX_LINK_LEN);
                    writeStaticFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,staticDid.link3Addr, TBOX_LINK_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_LINK_LEN;
            }
            break;
        }
            /* TBOX 第 3 链路地址端口 */
        case DID_LINK3_PORT:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid.link3Port, writeBuf, TBOX_LINK_PORT_LEN) != 0)
                {
                    memcpy(staticDid.link3Port, writeBuf, TBOX_LINK_PORT_LEN);
                    writeStaticFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,staticDid.link3Port, TBOX_LINK_PORT_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_LINK_PORT_LEN;
            }
            break;
        }
        case DID_LINK3_BI_AUTH_PORT:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid.link3BiAuthPort, writeBuf, TBOX_LINK_PORT_LEN) != 0)
                {
                    memcpy(staticDid.link3BiAuthPort, writeBuf, TBOX_LINK_PORT_LEN);
                    writeStaticFlag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,staticDid.link3BiAuthPort, TBOX_LINK_PORT_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_LINK_PORT_LEN;
            }
            break;
        }





        case DID_THIRD_PARTY_LINK_ADDR:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid2.thirdPartyLinkAddr, writeBuf, TBOX_LINK_LEN) != 0)
                {
                    memcpy(staticDid2.thirdPartyLinkAddr, writeBuf, TBOX_LINK_LEN);
                    writeStatic2Flag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,staticDid2.thirdPartyLinkAddr, TBOX_LINK_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_LINK_LEN;
            }
            break;
        }
        case DID_THIRD_PARTY_LINK_PORT:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid2.thirdPartyLinkPort, writeBuf, TBOX_LINK_PORT_LEN) != 0)
                {
                    memcpy(staticDid2.thirdPartyLinkPort, writeBuf, TBOX_LINK_PORT_LEN);
                    writeStatic2Flag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,staticDid2.thirdPartyLinkPort, TBOX_LINK_PORT_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_LINK_PORT_LEN;
            }
            break;
        }
        case DID_LINK3_ADDR_USERNAME:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid2.link3Username, writeBuf, TBOX_LINK_USERNAME_LEN) != 0)
                {
                    memcpy(staticDid2.link3Username, writeBuf, TBOX_LINK_USERNAME_LEN);
                    writeStatic2Flag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,staticDid2.link3Username, TBOX_LINK_USERNAME_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_LINK_USERNAME_LEN;
            }
            break;
        }
        case DID_LINK3_PORT_PASSWORD:
        {
            if(DID_WRITE_TYPE == type)
            {
                if (memcmp(staticDid2.link3Password, writeBuf, TBOX_LINK_PASSWORD_LEN) != 0)
                {
                    memcpy(staticDid2.link3Password, writeBuf, TBOX_LINK_PASSWORD_LEN);
                    writeStatic2Flag = true;
                    result = DID_RW_RESULT_SUCCESS;
                }
            }
            else if(DID_READ_TYPE == type)
            {
                memcpy(readBuf,staticDid2.link3Password, TBOX_LINK_PASSWORD_LEN);
                result = DID_RW_RESULT_SUCCESS;
                didReadBufLen = TBOX_LINK_PASSWORD_LEN;
            }
            break;
        }





        case DID_RDS_SOFTWARE_IN:
        {
            if(DID_WRITE_TYPE == type)
            {
                memcpy(dynamicDidInfo->rdsSoftwareIn,writeBuf,RDS_SOFTWARE_IN_LEN);
                result = DID_RW_RESULT_SUCCESS;
            }
            else if(DID_READ_TYPE == type)
            {
                result = DID_RW_RESULT_FAIL;
            }
            break;
        }
        case DID_RDS_SOFTWARE_OUT:
        {
            if(DID_WRITE_TYPE == type)
            {
                memcpy(dynamicDidInfo->rdsSoftwareOut,writeBuf,RDS_SOFTWARE_OUT_LEN);
                result = DID_RW_RESULT_SUCCESS;
            }
            else if(DID_READ_TYPE == type)
            {
                result = DID_RW_RESULT_FAIL;
            }
            break;
        }
        case DID_RDS_SUPPIER_IDENTIFIER:
        {
            if(DID_WRITE_TYPE == type)
            {
                memcpy(dynamicDidInfo->rdsSoftwareSupplierIdentifier,writeBuf,RDS_SOFTWARE_SUPPLIER_IDENTIFIER_LEN);
                result = DID_RW_RESULT_SUCCESS;
            }
            else if(DID_READ_TYPE == type)
            {
                result = DID_RW_RESULT_FAIL;
            }
            break;
        }
        default:
        {
            result = DID_RW_RESULT_FAIL;
            break;
        }
    }

    didReadBuf[0] = type;
    didReadBuf[1] = result;
    didReadBuf[2] = (did>>8)&0xff;
    didReadBuf[3] = (did)&0xff;
    didReadBufLen = didReadBufLen + 4;
    msg.event  = MESSAGE_TX_DID_INFO;
    msg.len    = didReadBufLen;
    msg.lparam = (uint32_t)&didReadBuf[0];
    SystemSendMessage(TASK_ID_IPC, msg);

    if(writeCarFlag)
    {
        errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8_t*)&carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
        if(NV_NO_ERROR != errorCode)
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Did:0x%04x, Car Info error ret:%d\r\n", did, errorCode);
        }
    }

    if(writeStaticFlag)
    {
        errorCode = NvApiWriteData(NV_ID_STATIC_DID, (uint8_t*)&staticDid, g_nvInfoMap[NV_ID_STATIC_DID].nvValidLen);
        if(NV_NO_ERROR != errorCode)
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Did:0x%04x, Did Write error ret:%d\r\n", did, errorCode);
        }
    }

    if(writeStatic2Flag)
    {
        errorCode = NvApiWriteData(NV_ID_STATIC_DID2, (uint8_t*)&staticDid2, g_nvInfoMap[NV_ID_STATIC_DID2].nvValidLen);
        if(NV_NO_ERROR != errorCode)
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Did:0x%04x, Did Write error ret:%d\r\n", did, errorCode);
        }
    }

    if (did != DID_DYNAMIC_DID && did != DID_DATA_RESEND_TEST_TIME && did != DID_LEVEL3_ALARM_TEST_TIME) //过滤周期的
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Did:0x%04x, rw:%d, len:%d ret:%d\r\n", did, type,didReadBufLen,errorCode);
    }

}

/*************************************************
函数名称: IpcMsgRxArmRequestDtc
函数功能: 接收ARM请求故障码
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/06/
*************************************************/
static void IpcMsgRxArmRequestDtc(uint8_t *para, uint16_t len)
{
    Msg msg;

    msg.event = EVENT_ID_DIAG_DTC_EVENT;
    msg.len = len;
    msg.lparam = (uint32_t)para;
    SystemSendMessage(TASK_ID_DIAG, msg);
}

/*************************************************
函数名称: IpcMsgRxArmSetGpio
函数功能: 接收ARM控制GPIO
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/06/
*************************************************/
static void IpcMsgRxArmSetGpio(uint8_t *para, uint16_t len)
{
    GpioCtrlTypeEnum gpioNum;
    GpioStatusTypeEnum gpioMode;
    uint8_t lowTime = 0;
    uint8_t highTime = 0;

    gpioNum  = (GpioCtrlTypeEnum)para[0];
    gpioMode = (GpioStatusTypeEnum)para[1];
    lowTime  = para[2];
    highTime = para[3];

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "arm set gpio:%x,%x,%x,%x\r\n",gpioNum,gpioMode,lowTime,highTime);
    // todo 原arm设置led状态的函数，目前arm未使用 留空 后续有需要改为设置mcu Gpio的通用函数

}

/*************************************************
函数名称: IpcMsgRxArmClientSta
函数功能: 接收ARM客户端运行状态
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/06/
*************************************************/
static void IpcMsgRxArmClientSta(uint8_t *para, uint16_t len)
{
    uint8_t ConnectStatus[2][15] = {{"DisConnected"}, {"Connected"}};
    // NEW_TODO
    // ArmClinetStatus status = (ArmClinetStatus)para[0];

    // 由于存在多个客户端，所以需要统计客户端数量
    // if(ARM_CLINET_OFFLINE == status)
    // {
    //     g_commonInfo.armClientCount--;
    // }
    // else if(ARM_CLINET_ONLINE == status)
    // {
    //     g_commonInfo.armClientCount++;
    // }
    // g_commonInfo.armClinetStatus = g_commonInfo.armClientCount > 0 ? ARM_CLINET_ONLINE : ARM_CLINET_OFFLINE;
    g_commonInfo.armClinetStatus = para[0];
    // SystemApiLogPrintf(LOG_INFO_OUTPUT, "arm client %d %s\r\n", g_commonInfo.armClientCount, &ConnectStatus[status][0]);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "arm client %s\r\n", &ConnectStatus[g_commonInfo.armClinetStatus][0]);
    PmVehicleIgStatusNotify(g_commonInfo.accStatus);

    // if(1 == g_commonInfo.armClientCount)
    // {
    //     BtModuleHandShakeSend(HANDSHAKE_SUCESS, BT_HARD_RESET_CLOSE_ADV);
    //     RemoteVehicleBleKeyNotify(VEHICLE_KEY_INIT);
    // }
    // else if(0 == g_commonInfo.armClientCount)
    // {
    //     BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_HARD_RESET_CLOSE_ADV);
    // }
}

/*************************************************
函数名称: IpcMsgRxGBremoteControl
函数功能: 接收到国标远程控制
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: ZXL
编写日期 :2019/10/21
*************************************************/
static void IpcMsgRxRemoteControl(uint8_t *para, uint16_t len)
{
    Msg msg;

    msg.event  = EVENT_ID_CAN_RX_IPC_REMOTE_CONTROL;
    msg.len    = len;
    msg.lparam = (uint32_t)para;
    SystemSendMessage(TASK_ID_CAN, msg);
}

/*************************************************
函数名称: IpcMsgRxParaConfigCommand
函数功能: 接收到车辆参数配置命令处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
static void IpcMsgRxParaConfigCommand(uint8_t *para, uint16_t len)
{
    return;
}

/*************************************************
函数名称: IpcMsgRxPeriodVehicleReport
函数功能: 接收到NAD状态信息周期下发命令处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
static void IpcMsgRxPeriodNadStatus(uint8_t *para, uint16_t len)
{
    uint8_t *tempbuf = NULL;
    FtmInfo  *pFtmInfo = FtmInitRead();

    if(NULL == para)
    {  
        return;
    }

    if(pFtmInfo->ftmLedTestEvent == true)
    {
        gnssFixStatusBak = 0xFF;
        g4ModuleStatusBak = 0xFF;
        return;
    }

    tempbuf = (uint8_t *)para;

    if(len ==26)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "IPC Rx Period Nad Status\r\n");
        g_commonInfo.networkSignalStrength  = tempbuf[0];
        g_commonInfo.gpsSatelliteCount      = tempbuf[1];
        g_commonInfo.gpsDirection           = (uint16_t)(tempbuf[2]<<8) + tempbuf[3];
        g_commonInfo.nadWorkStatus          = (NadWorkStatus)tempbuf[4];
        g_commonInfo.wifiSignalStrength     = (NetworkSignalStrength)tempbuf[5];
        g_commonInfo.g4ModuleStatus         = (G4ModuleStatus)tempbuf[6];
        g_commonInfo.gnssFixStatus          = (GnssFixStatus)tempbuf[7];
        g_commonInfo.simStatus              = (SimStatus)tempbuf[8]; 
        g_commonInfo.gnssSignalStrength     = tempbuf[9];
        g_commonInfo.emmcStatus             = tempbuf[10];
        g_commonInfo.ethStatus              = tempbuf[11];
        g_commonInfo.gnssLatitude           = (uint32_t)(tempbuf[12]<<24) + (uint32_t)(tempbuf[13]<<16) + (uint32_t)(tempbuf[14]<<8) + tempbuf[15];
        g_commonInfo.gnssLongitude          = (uint32_t)(tempbuf[16]<<24) + (uint32_t)(tempbuf[17]<<16) + (uint32_t)(tempbuf[18]<<8) + tempbuf[19];
        g_commonInfo.gnssSpeed              = (uint32_t)(tempbuf[20]<<24) + (uint32_t)(tempbuf[21]<<16) + (uint32_t)(tempbuf[22]<<8) + tempbuf[23];
        g_commonInfo.tspConnectStatus       = tempbuf[24];
        g_commonInfo.wifiStatus             = tempbuf[25];
    }

}


/*************************************************
函数名称: IpcMsgRxPeriodVehicleReport
函数功能: 接收到ARM通知检测Mic故障消息
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/6
*************************************************/
static void IpcMsgRxCheckMicFault(uint8_t *para, uint16_t len)
{

}

/*************************************************
函数名称: IpcMsgRxVehicleRemoteDiagnosis
函数功能: 汽车车辆远程诊断或升级
输入参数: 参数指针和长度 
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/06/07  
*************************************************/
static void IpcMsgRxVehicleRemoteDiagnosis(uint8_t *para, uint16_t len)
{

    uint8_t channel = 0;
    uint32_t id = 0;
    uint32_t datalen = 0;
    static uint8_t buf[IPC_MAX_SIZE_LEN] = {0};
    uint8_t i= 0;

    channel = para[0];
    id = ((uint32_t)para[1] << 24) |
        ((uint32_t)para[2] << 16)  |
        ((uint32_t)para[3] << 8)   |
        ((uint32_t)para[4]);
    datalen = ((uint32_t)para[5] << 8) | (uint32_t)para[6];
    memcpy(buf,para+7,datalen);
    //SystemApiLogPrintf(LOG_ERROR_OUTPUT, " datalen:%d id:%04x buf[0--3]:%02x %02x %02x %02x para[8]:%02x\r\n",datalen,id,buf[0],buf[1],buf[2],buf[3],para[8]);
    PRINTF("channel:%d\n",channel);
    PRINTF("CanID:%x\n",id);
    PRINTF("para is:");
    memcpy(buf,para+7,datalen);
    for(i = 0; i<datalen ;i++)
    {
        PRINTF(" %x",buf[i]);
    }
    PRINTF("\n");
    CanProtocolSdkTpDataSend(channel,id,datalen,buf);
}

/*************************************************
函数名称: IpcMsgRxRemoteUpgradeDownload
函数功能: 接收到远程升级包下发消息处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
修改日期 :2016/08/30   增加远程升级的处理 
修改日期 :2016/12/05   增加ECU升级流程
*************************************************/
static void IpcMsgRxRemoteUpgradeDownload(uint8_t *para, uint16_t len)
{        
    Msg msg;
    RemoteUpdateMessage *remoteUpdateMessage = NULL;
    
    if(NULL == para)
    {
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc rx RemoteUpgradeDownload error\r\n");
        #endif
        
        return;
    }

    remoteUpdateMessage = (RemoteUpdateMessage *)para;
    if(UPDATE_TYPE_MCU == remoteUpdateMessage->updateType)
    {
        msg.event = EVENT_ID_REMOTE_UPDATE_NOFIFY;
        msg.len   = len;
        msg.lparam = (uint32_t)para;
        SystemSendMessage(TASK_ID_UPDATE, msg);
    }
    else if(UPDATE_TYPE_BT == remoteUpdateMessage->updateType)
    {
        msg.event = EVENT_ID_BT_UPGRADE_CMD;
        msg.len   = len;
        msg.lparam = (uint32_t)para;
        SystemSendMessage(TASK_ID_BT, msg);
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "bt upgrade %d, status is %d\r\n",remoteUpdateMessage->updateType, remoteUpdateMessage->updateStatus);
    }
    else if(UPDATE_TYPE_ECU == remoteUpdateMessage->updateType)
    {

    }
    else
    {
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "updateType error is %d\r\n",remoteUpdateMessage->updateType);
        #endif
    }
    return;
}

/*************************************************
函数名称: IpcMsgRxCanLogControlCmd
函数功能: 接收到ARM发送控制CAN底层传输命令
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/08/22
*************************************************/
static void IpcMsgRxCanLogControlCmd(uint8_t *para, uint16_t len)
{    
    if(NULL == para)
    {
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc rx can log control  error\r\n");
        #endif
        return;
    }

    if(0 == para[0])
    {
        g_commonInfo.canReportInit = FALSE;
    }
    else if(1 == para[0])
    {
        g_commonInfo.canReportInit = TRUE;
    }
    else
    {
        /* NULL */
    }
}


/*************************************************
函数名称: IpcMsgRxSetRtcWakeupTime
函数功能: 接收到ARM发送设置唤醒时间
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/08/23 
*************************************************/
static void IpcMsgRxSetRtcWakeupTime(uint8_t *para, uint16_t len)
{
    RtcWakeUpTypeEnum rtcWakeUpType;
    FtmInfo     *pFtmInfo = FtmInitRead();

    if(NULL == para)
    {
        return;
    }

    if(FTM_MODE_EXIT == pFtmInfo->ftmMode)
    {
        rtcWakeUpType = (RtcWakeUpTypeEnum)para[0];
        TboxSystemRtcWakeupSet(para[1], para[2], para[3], para[4], rtcWakeUpType);

        SystemApiLogPrintf(LOG_INFO_OUTPUT, "IPC set Wakeup time with day=%d, hour=%d, minute=%d, second=%d, wakeupType=%d\r\n",
                           para[1], para[2], para[3], para[4], rtcWakeUpType);
    }
}

/*************************************************
函数名称: IpcMsgRxArmFtmCmd
函数功能: 接收到ARM装备命令
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/01
*************************************************/
static void IpcMsgRxArmFtmCmd(uint8_t *para, uint16_t len)
{
    uint8_t *tempbuf = NULL;
    uint8_t tempBuffer[10];
    Msg msg;
    
    if(NULL == para)
    {
        return;
    }

    tempbuf = (uint8_t *)para;
    #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "arm->mcu cmd 0x%x, 0x%x\r\n", *tempbuf, *(tempbuf+1));
    #endif

    switch(*tempbuf)
    {
        case ARM_FTM_RX_SPI:
        {
            tempBuffer[0] = 0x1a;
            tempBuffer[1] = 0x01;
            msg.event  = MESSAGE_TX_PC_CMD;
            msg.len    = 2;
            msg.lparam = (uint32_t)&tempBuffer[0];
            FtmCanTxPackageFrameData(msg);
            break; 
        }
        case 0x34:
        {
            FtmRxMcuReadModuleWakeUpPin(&tempbuf[1], 1);
            break;
        }
        case 0x29:
        {
            FtmNavigateTestModeSet(tempbuf[1]);
            break;
        }
        default:
        {
            break;
        }
    }
}

/*************************************************
函数名称: IpcMsgRxArmPcCmd
函数功能: 接收到ARM发送到PC的命令
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/01
*************************************************/
static void IpcMsgRxArmPcCmd(uint8_t *para, uint16_t len)
{
    FtmTxMsgArmToPc(para, len);
}

/*************************************************
函数名称: IpcMsgRxArmDutCmd
函数功能: 接收到ARM发送到Dut的命令
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/01
*************************************************/
static void IpcMsgRxArmDutCmd(uint8_t *para, uint16_t len)
{
    Msg msg;
    
    if(NULL == para)
    {
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc rx arm pc cmd error\r\n");
        #endif
        return;
    }

    msg.event = MESSAGE_TX_DUT_CMD;
    msg.len   = len;
    msg.lparam = (uint32_t)para;
    FtmCanTxPackageFrameData(msg);
}

/*************************************************
函数名称: IpcMsgRxArmControlResetEnable
函数功能: 控制mcu使能ARM的重启 1：能 0：不能
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: pc
编写日期 :2022/07/01
*************************************************/
static void IpcMsgRxArmControlResetEnable(uint8_t *para, uint16_t len)
{
    if(NULL == para)
    {
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc rx arm para error\r\n");
        #endif
        return;
    }
    if(para[0]==0)
    {
        g_armresetdisable = true;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "disable ARM reset\r\n");
    }
    else
    {
        g_armresetdisable = false;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "enable ARM reset\r\n");
    }
}

/*************************************************
函数名称: IpcMsgRxRemoteEcuUpgrade
函数功能: 远程ECU升级
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无 
编写者: liaoyonggang
编写日期 :2016/12/27
*************************************************/
static void IpcMsgRxRemoteEcuUpgrade(uint8_t *para, uint16_t len)
{
    if(NULL == para)
    {
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc rx Remote Ecu Upgrade error\r\n");
        #endif
        return;
    }
}

/*************************************************
函数名称: IpcRxMsgTxAckMsg
函数功能: MCU回ACK消息
输入参数: 无
输出参数: 无
函数返回类型值：无 
编写者: liaoyonggang
编写日期 :2016/07/21
*************************************************/
static void IpcRxMsgTxAckMsg(void)
{
    uint8_t g_mcuAckData[5] = {0xfc,0x02,0x00,0x02,0xfc};
    IpcSendData(sizeof(g_mcuAckData), g_mcuAckData);
    //Uart33SendData(5, (uint8_t *)g_mcuAckData);
}

/*************************************************
函数名称: IpcDecodeEscapeData
函数功能: Ipc解码转义字节 0xFB 0X02--->0XFC  
                          0XFB 0X01--->0XFB
输入参数: 转义前参数指针和长度  转义后参数指针和长度
输出参数: 转义结果
函数返回类型值： 
           IPC_NO_ERROR --- 转义执行正确
           IPC_INPUT_PARA_ERROR--- 参数输入错误
           IPC_FRAME_LEN_TOO_LONG ---消息报文长度过长
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
static ipcErrorCode IpcDecodeEscapeData(uint8_t *pEscapeData, uint16_t len, uint8_t* pOutData, uint16_t *outLen)
{
    ipcErrorCode errorCode = IPC_NO_ERROR;
    EscapeFlag  escapeFlag = FLAG_NOT_ESCAPE;
    uint16_t i = 0;
    uint16_t tempLen = 0;
    
    //输入参数合法性检查
    if((NULL == pEscapeData) ||(IPC_MAX_SIZE_LEN < len) ||(NULL == pOutData) ||(NULL == outLen))
    {
       errorCode = IPC_INPUT_PARA_ERROR;
       return errorCode;
    }

    memset(pOutData, 0x00, IPC_MAX_SIZE_LEN);
    for(i = 0; i < len; i++)
    {
        //检测到转义字节0xfb
        if(IPC_ESCAPE_DATA == pEscapeData[i])
        {
            escapeFlag = FLAG_ESCAPE;
        }
        else if((0x02 == pEscapeData[i])||(0x01 == pEscapeData[i]))
        {
            if(escapeFlag == FLAG_ESCAPE)
            {
                if(0x02 == pEscapeData[i])
                {
                    pOutData[tempLen] = IPC_MESSAGE_IDENTIFIER;
                }
                else
                {
                    pOutData[tempLen] = IPC_ESCAPE_DATA;
                }
                escapeFlag = FLAG_NOT_ESCAPE;
                tempLen++;
            }
            else
            {
                pOutData[tempLen] = pEscapeData[i];
                tempLen++;
            }
        }
        else
        {
            pOutData[tempLen] = pEscapeData[i];
            tempLen++;
        }
    }

    *outLen = tempLen;
    
    if(IPC_MAX_SIZE_LEN < *outLen)
    {
        errorCode = IPC_FRAME_LEN_TOO_LONG;
    }

    return errorCode;
}

/*************************************************
函数名称: IpcEncodeEscapeData
函数功能: Ipc编码转义字节 0XFC--->0xFB 0X02
                          0XFB--->0XFB 0X01
输入参数: 转义前参数指针和长度  转义后参数指针和长度
输出参数: 转义结果
函数返回类型值： 
           IPC_NO_ERROR --- 转义执行正确
           IPC_INPUT_PARA_ERROR--- 参数输入错误
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
static ipcErrorCode IpcEncodeEscapeData(uint8_t* pEscapeData, uint16_t len, uint8_t *pOutData, uint16_t *outLen)
{
    ipcErrorCode errorCode = IPC_NO_ERROR;
    uint16_t i = 0;
    uint16_t tempLen = 0;

    //输入参数合法性检查
    if((NULL == pEscapeData) ||(IPC_MAX_SIZE_LEN < len) ||(NULL == pOutData) ||(NULL == outLen))
    {
       errorCode = IPC_INPUT_PARA_ERROR;
       return errorCode;
    }

    memset(pOutData, 0x00, IPC_MAX_SIZE_LEN - 1);
    tempLen = 0;

    for(i = 0; i < len; i++)
    {
        if((IPC_MESSAGE_IDENTIFIER != pEscapeData[i])&&(IPC_ESCAPE_DATA != pEscapeData[i]))
        {
            pOutData[tempLen] = pEscapeData[i];
            tempLen++;
        }
        //检测到转义字节0xfc
        else if(IPC_MESSAGE_IDENTIFIER == pEscapeData[i])
        {
            pOutData[tempLen] = IPC_ESCAPE_DATA;
            tempLen++;
            pOutData[tempLen] = 0x02;
            tempLen++;
        }
        //检测到转义字节0xfb
        else
        {
            pOutData[tempLen] = IPC_ESCAPE_DATA;
            tempLen++;
            pOutData[tempLen] = 0x01;
            tempLen++;
        }
    }

    *outLen = tempLen;
    return errorCode;
}

/*************************************************
函数名称: IpcXorCalculation
函数功能: 数据异或计算
输入参数: 输入数据指针和长度  计算结果
输出参数: 执行结果
函数返回类型值： 
           IPC_NO_ERROR --- 转义执行正确
           IPC_INPUT_PARA_ERROR--- 参数输入错误
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
ipcErrorCode IpcXorCalculation(uint8_t *pData, uint16_t len, uint8_t *resultData)
{
    ipcErrorCode errorCode = IPC_NO_ERROR;
    uint16_t i = 0;

    if((NULL == pData)||(NULL == resultData))
    {
        errorCode = IPC_INPUT_PARA_ERROR;
        return errorCode;
    }

    *resultData = 0;
    
    for(i = 0; i < len; i++)
    {
        *resultData = (*resultData)^(pData[i]);
    }

    return errorCode;
}

/*************************************************
函数名称: IpcClearAckInfo
函数功能: IPC接收到应答相关消息后，清除ACK相关信息
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
static void IpcClearAckInfo(void)
{
    g_ipcInfo.ackFlag               = FLAG_NOT_WATI_ACK;
    g_ipcInfo.retransmissionCount   = 0;
    g_ipcInfo.retransmissionTimeout = 0;
    g_ipcInfo.ipcTx.len             = 0;
    memset(g_ipcInfo.ipcTx.buffer, 0x00, IPC_MAX_SIZE_LEN);
}

/*************************************************
函数名称: IpcSetupAckInfo
函数功能: IPC发送需要回应ACK消息后，置位相应ACK状态
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
static void IpcSetupAckInfo(void)
{
    g_ipcInfo.ackFlag               = FLAG_WAIT_ACK;
    g_ipcInfo.retransmissionCount   = 0;
    g_ipcInfo.retransmissionTimeout = 0;
}

/*************************************************
函数名称: IpcTxPackageFrameData
函数功能: 执行发送前的命令组包和发送过程
输入参数: 接收事件 长度 和具体参数
输出参数: ipcErrorCode
函数返回类型值：
              IPC_NO_ERROR --- 组包执行成功，并发送
              IPC_INPUT_PARA_ERROR ---输入参数错误
              IPC_ID_ERROR --- 消息报文ID错误
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
static ipcErrorCode IpcTxPackageFrameData(Msg msg)
{
    ipcErrorCode errorCode = IPC_NO_ERROR;
    MessageTxIndex txIndex = MESSAGE_ID_TX_MCU_STATUS;
    MessageAttribute txAttribute;
    uint8_t txBuffer[IPC_MAX_SIZE_LEN] = {0};
    uint8_t *pTempBuffer = NULL;
    uint16_t i = 0;
    uint16_t len = 0;
    uint8_t  checkByte = 0;

    for(txIndex = MESSAGE_ID_TX_MCU_STATUS; txIndex < MESSAGE_ID_TX_MAX; txIndex++)
    {
        if(g_txMessageInfoMap[txIndex].id == msg.event)
        {
            break;
        }
    }
    
    //未查询到对应的消息ID
    if(MESSAGE_ID_TX_MAX == txIndex)
    {
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc tx event error is 0x%x\r\n", msg.event);
        #endif
        
        errorCode = IPC_ID_ERROR;
        return errorCode;
    }

    if(FLAG_FIXED_LEN == g_txMessageInfoMap[txIndex].fixedFlag)
    {
        if(g_txMessageInfoMap[txIndex].len != msg.len)
        {
            #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc tx len error is 0x%x\r\n", msg.event);
            #endif
            errorCode = IPC_INPUT_PARA_ERROR;
            return errorCode;
        }
    }
    
    memset(txBuffer, 0x00, IPC_MAX_SIZE_LEN);
    memset(g_ipcInfo.ipcTx.buffer, 0x00, IPC_MAX_SIZE_LEN);
    g_ipcInfo.ipcTx.len = 0;
    
    //发送事件需要等待ACK
    if(FLAG_WAIT_ACK == g_txMessageInfoMap[txIndex].ackFlag)
    {
        IpcSetupAckInfo();
    }

    //发送属性赋值
    txAttribute.bit.len            = msg.len;
    txAttribute.bit.encryptionMode = g_txMessageInfoMap[txIndex].encryptMode;
    txAttribute.bit.type           = g_txMessageInfoMap[txIndex].type;
    txAttribute.bit.ackFlag        = g_txMessageInfoMap[txIndex].ackFlag;
    txAttribute.bit.reserved       = 0;

    txBuffer[MESSAGE_HEAD_ID_HIGH] = (uint8_t)(msg.event >> 8);
    txBuffer[MESSAGE_HEAD_ID_LOW]  = (uint8_t)msg.event;

    txBuffer[MESSAGE_HEAD_ATTRIBUTE_HIGH] = (uint8_t)(txAttribute.byte >> 24);
    txBuffer[MESSAGE_HEAD_ATTRIBUTE_LOW]  = (uint8_t)(txAttribute.byte >> 16);
    txBuffer[MESSAGE_HEAD_LEN_HIGH] = (uint8_t)(txAttribute.byte >> 8);
    txBuffer[MESSAGE_HEAD_LEN_LOW]  = (uint8_t)txAttribute.byte;

    if(MESSAGE_TX_POWER_ON_REPORT == msg.event)
    {
        g_ipcInfo.txTid = 1;
    }

    if(0xffff == g_ipcInfo.txTid)
    {
        g_ipcInfo.txTid = 0;
    }
    else if (FLAG_WAIT_ACK == txAttribute.bit.ackFlag)
    {
        g_ipcInfo.txTid = g_ipcInfo.txTid + 1;
    }
    
    txBuffer[MESSAGE_HEAD_TID_HIGH] = (uint8_t)(g_ipcInfo.txTid >> 8);
    txBuffer[MESSAGE_HEAD_TID_LOW]  = (uint8_t)g_ipcInfo.txTid;

    len = MESSAGE_HEAD_DATA_LEN;
    if(0 != msg.len)
    {
        if(0 == msg.lparam)
        {
            #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc tx para error\r\n");
            #endif
            
            errorCode = IPC_INPUT_PARA_ERROR;
            return errorCode;
        }
        else
        {
            pTempBuffer = (uint8_t *)msg.lparam;
            for(i = 0; i < msg.len; i++)
            {
                txBuffer[len++] = pTempBuffer[i];
            }
        }
    }

    //异或校验码计算
    errorCode = IpcXorCalculation(txBuffer, len, &checkByte);
    if(IPC_NO_ERROR != errorCode)
    {
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc xor check fail\r\n");
        #endif
        
        return errorCode;
    }
    txBuffer[len] = checkByte;
    len = len + 1;

    //发送数据进行转义
    errorCode = IpcEncodeEscapeData(txBuffer, len, &g_ipcInfo.ipcTx.buffer[1], &g_ipcInfo.ipcTx.len);
    if(IPC_NO_ERROR != errorCode)
    {
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc tx encode error\r\n");
        #endif
        return errorCode;
    }

    g_ipcInfo.ipcTx.buffer[0] = IPC_MESSAGE_IDENTIFIER;
    g_ipcInfo.ipcTx.buffer[g_ipcInfo.ipcTx.len + 1] = IPC_MESSAGE_IDENTIFIER;
    g_ipcInfo.ipcTx.len = g_ipcInfo.ipcTx.len + 2;

    //串口发送数据
    //Uart33SendData(g_ipcInfo.ipcTx.len, g_ipcInfo.ipcTx.buffer);
    IpcSendData(g_ipcInfo.ipcTx.len, g_ipcInfo.ipcTx.buffer);
    if(MESSAGE_TX_VEHICLE_REMOTE_DIAGNOSIS != msg.event
        && MESSAGE_TX_BT_RX_DATA != msg.event
        && MESSAGE_TX_MCU_LOG != msg.event
        && MESSAGE_TX_STB_CALIBRATION_RESPONSE != msg.event
        && MESSAGE_TX_MCU_STATUS != msg.event)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "ipc tx is 0x%x\r\n", msg.event);
    }
    return errorCode;
}

/*************************************************
函数名称: IpcUartRxNadAckFunction
函数功能: Ipc串口接收到应答帧处理函数
输入参数: 无
输出参数: 无
函数返回类型值： 无
编写者: liaoyonggang
编写日期 :2016/07/21
*************************************************/
static void IpcUartRxNadAckFunction(uint8_t *tempBuffer, uint16_t tempLen)
{
   if(NULL == tempBuffer)
   {
       return;
   }
   //如果是NAD ACK应答
   if((0xfc == tempBuffer[0])&&(0x08 == tempBuffer[1])&&(0xfc == tempBuffer[4]))
   {
       IpcClearAckInfo();
   }
}

/*************************************************
函数名称: IpcUartRxMsgFunction
函数功能: Ipc串口接收到消息帧处理函数
输入参数: 无
输出参数: 无
函数返回类型值： 无
编写者: liaoyonggang
编写日期 :2016/07/25
*************************************************/
static void IpcUartRxMsgFunction(uint8_t *tempBuffer, uint16_t *tempLen)
{
    uint8_t  buffer[IPC_MAX_SIZE_LEN] = {0};
    uint16_t len = 0;
    uint8_t  checkByte = 0;
    uint16_t msgId = 0;
    uint32_t msgAttribute = 0;
    uint16_t rxTid = 0;
    Msg msg;
    MessageRxIndex rxIndex = MESSAGE_ID_RX_HEARTBEAT;

    //消息帧解析失败
    if(IPC_NO_ERROR != IpcDecodeEscapeData(&tempBuffer[1], *tempLen - 2, buffer, &len))
    {
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc rx decode frame error\r\n");
        #endif
        *tempLen = 0;
        return;
    }

    IpcXorCalculation(buffer, len - 1, &checkByte);
    //如果校验成功
    if(checkByte == buffer[len - 1])
    {
        msgId        = (uint16_t)(buffer[MESSAGE_HEAD_ID_HIGH] << 8) + buffer[MESSAGE_HEAD_ID_LOW];
        msgAttribute = (uint32_t)(buffer[MESSAGE_HEAD_ATTRIBUTE_HIGH] << 24) + (uint32_t)(buffer[MESSAGE_HEAD_ATTRIBUTE_LOW] << 16) +
                       (uint32_t)(buffer[MESSAGE_HEAD_LEN_HIGH] << 8) + buffer[MESSAGE_HEAD_LEN_LOW];
        rxTid        = (uint16_t)(buffer[MESSAGE_HEAD_TID_HIGH] << 8) + buffer[MESSAGE_HEAD_TID_LOW];

        /*增加握手以后才能接收其他事件内容*/
        if(HANDSHAKE_FAIL == g_commonInfo.handshakeStatus)
        {
            if(MESSAGE_RX_HANDSHAKE != msgId && MESSAGE_RX_HEARTBEAT != msgId)
            {
                *tempLen = 0;
                SystemApiLogPrintf(LOG_WARING_OUTPUT, "Got message before hand shake:%x\r\n", msgId);
                return;
            }
        }
        if(MESSAGE_RX_HANDSHAKE == msgId)
        {
            g_ipcInfo.rxTid = 1;
        }
        
        for(rxIndex = MESSAGE_ID_RX_HEARTBEAT; rxIndex < MESSAGE_ID_RX_MAX; rxIndex++)
        {
            if(g_rxMessageInfoMap[rxIndex].id == msgId)
            {
                break;
            }
        }

        if(MESSAGE_ID_RX_MAX == rxIndex)
        {
            #if(LOG_SWITCH_CONFIG_DEBUG == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc rx error id is 0x%x, %d\r\n", msgId, len);
            #endif
            *tempLen = 0;
            return;
        }

        if(FLAG_FIXED_LEN == g_rxMessageInfoMap[rxIndex].fixedFlag)
        {
            if(g_rxMessageInfoMap[rxIndex].attribute.byte != msgAttribute)
            {
                #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc rx error attribute:0x%x\r\n", msgAttribute);
                #endif
                return;
            }
        }
        // 需要回复ACK的直接回复
        if(MSG_ATTR_ACK_FLAG_MASK == (msgAttribute & MSG_ATTR_ACK_FLAG_MASK))
        {
            IpcRxMsgTxAckMsg();
        }
        // 正常流水号
        if( ((0xffff == g_ipcInfo.rxTid) && (0 == rxTid)) ||
            (g_ipcInfo.rxTid == rxTid -1) ||
            (MSG_ATTR_ACK_FLAG_MASK != (msgAttribute & MSG_ATTR_ACK_FLAG_MASK)))
        {
            // 需要ACK的才增加流水号
            if(MSG_ATTR_ACK_FLAG_MASK == (msgAttribute & MSG_ATTR_ACK_FLAG_MASK))
            {
                g_ipcInfo.rxTid = rxTid;
            }
            msg.event  = msgId;
            msg.len    = msgAttribute & MSG_ATTR_LEN_MASK;
            msg.lparam = (uint32_t)&buffer[MESSAGE_HEAD_DATA_LEN];
            
            if(QUEUE_NO_ERROR != SystemApiSendMessage(&g_taskRxQueue[QUEUE_ID_IPC_RX_TASK], msg))
            {
                #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "send ipc ack fail\r\n");
                #endif
                return;
            }
        }
        // 重复流水号，不操作
        else if(g_ipcInfo.rxTid == rxTid)
        {
            #if(LOG_SWITCH_CONFIG_WARING == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "ipc rx event is 0x%x, repeat tid is 0x%x\r\n", msgId, rxTid);
            #endif
        }
        // 错误tid 直接修正，但不处理
        else
        {
            g_ipcInfo.rxTid = rxTid;
            #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc rx event is 0x%x, error tid is 0x%x\r\n", msgId, rxTid);
            #endif
        }
    }
    else
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Ipc message xor failed, %x!=%x\r\n", checkByte, buffer[len - 1]); 
    }
}

/*************************************************
函数名称: IpcErrorCallback
函数功能: IPC串口数据接收错误回调函数
输入参数: driverState、userData - 未使用，event-中断事件
输出参数: 无
函数返回类型值： 无
编写者: zhengyong
编写日期 :2024/03/19
*************************************************/
void IpcErrorCallback(void *driverState, uart_event_t event, void *userData)
{
    uint32_t RecvLen = UartGetRecvLen(UART_PORT_IPC);
    linflexd_uart_state_t * uartState = (linflexd_uart_state_t *)driverState;
    switch(event)
    {
        // 非正常值一般不会出现，仅预留
        default:
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Ipc data callback error, event=%d\r\n", event);
        }
        // Err回调只会出现以下值
        case UART_EVENT_ERROR:
        {
            g_ipcInfo.uartErrFlag = true;
            g_ipcInfo.uartErrCount++;
            if(RecvLen < IPC_MAX_SIZE_LEN)
            {
                g_ipcInfo.uartLostBytes += (IPC_MAX_SIZE_LEN - RecvLen);
            }
            g_ipcInfo.lastUartStatus = uartState->receiveStatus;
            break;
        }
        // 正常Err回调不会出现以下值，此处预留
        case UART_EVENT_RX_FULL:
        case UART_EVENT_END_TRANSFER:
        case UART_EVENT_TIMEOUT:
        {
            if(RecvLen < IPC_MAX_SIZE_LEN)
            {
                g_ipcInfo.uartRxFlag = true;
            }
            break;
        }
    }
}

/*************************************************
函数名称: IpcDataCallback
函数功能: IPC串口数据接收完成回调函数
输入参数: driverState、userData - 未使用，event-中断事件
输出参数: 无
函数返回类型值： 无
编写者: zhengyong
编写日期 :2024/03/19
*************************************************/
void IpcDataCallback(void *driverState, uart_event_t event, void *userData)
{
    uint32_t RecvLen = UartGetRecvLen(UART_PORT_IPC);
    linflexd_uart_state_t * uartState = (linflexd_uart_state_t *)driverState;
    switch(event)
    {
        // 非正常值一般不会出现，仅预留
        default:
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Ipc data callback error, event=%d\r\n", event);
        }
        // 正常接收回调只会出现以下值
        case UART_EVENT_RX_FULL:
        case UART_EVENT_END_TRANSFER:
        case UART_EVENT_TIMEOUT:
        {
            if(RecvLen < IPC_MAX_SIZE_LEN)
            {
                g_ipcInfo.uartRxFlag = true;
            }
            break;
        }
        // 正常接收回调不会出现以下值，此处预留
        case UART_EVENT_ERROR:
        {
            g_ipcInfo.uartErrFlag = true;
            g_ipcInfo.uartErrCount++;
            if(RecvLen < IPC_MAX_SIZE_LEN)
            {
                g_ipcInfo.uartLostBytes += (IPC_MAX_SIZE_LEN - RecvLen);
            }
            g_ipcInfo.lastUartStatus = uartState->receiveStatus;
            break;
        }
    }
}

void ResetSerialOnReceiveFailure(void)
{
    static uint16_t faultCount = 0;

    faultCount++;
    if(faultCount == 2)
    {
        faultCount = 0;
        IpcUartReInit();
    }
}


/*************************************************
函数名称: IpcUartMainFunction
函数功能: Ipc串口主执行函数
输入参数: 无
输出参数: 无
函数返回类型值： 无
编写者: liaoyonggang
编写日期 :2018/04/24
*************************************************/
void IpcUartMainFunction(void)
{
    uint16_t count = 0;
    uint16_t index = 0;
    IpcStatus ipcStatus = IPC_STATUS_HEAD;
    uint8_t  tempBuf[IPC_MAX_SIZE_LEN] = {0};
    uint8_t  uartBuf[IPC_MAX_SIZE_LEN] = {0};

    if(STATUS_RX_IDLE == g_ipcInfo.rxStatus)
    {
        return;
    }
    memcpy(uartBuf, g_ipcInfo.uartBuf, sizeof(g_ipcInfo.uartBuf));
    g_ipcInfo.uartLen = 0;
    g_ipcInfo.rxStatus = STATUS_RX_IDLE;

    for(count = 0; count < IPC_MAX_SIZE_LEN; count++)
    {
        if(IPC_STATUS_HEAD == ipcStatus)
        {
            if(IPC_MESSAGE_IDENTIFIER == uartBuf[count])
            {
                tempBuf[index++] = IPC_MESSAGE_IDENTIFIER;
                ipcStatus = IPC_STATUS_TAIL;
            }
        }
        else if(IPC_STATUS_TAIL == ipcStatus)
        {
            if(IPC_MESSAGE_IDENTIFIER != uartBuf[count])
            {
                tempBuf[index++] = uartBuf[count];
            }
            else
            {
                tempBuf[index++] = IPC_MESSAGE_IDENTIFIER;
                ipcStatus = IPC_STATUS_END;
                break;
            }
        }
    }
    
    if(IPC_STATUS_END == ipcStatus)
    {
        if(5 == index)
        {
            IpcUartRxNadAckFunction(tempBuf, 5);
        }
        else if(5 < index)
        {
            IpcUartRxMsgFunction(tempBuf, &index);
        }
    }
    else
    {
        SystemApiLogPrintf(LOG_WARING_OUTPUT, "handle ipc message fail\r\n");
        ResetSerialOnReceiveFailure();
    }
}

/*************************************************
函数名称: IpcUartRxFunction
函数功能: Ipc串口DMA接收处理函数
输入参数: 无
输出参数: 无
函数返回类型值： 无
编写者: liaoyonggang
编写日期 :2018/04/24
*************************************************/
void IpcUartRxFunction(void)
{
    static uint8_t ipcLostCnt = 0;

    if(STATUS_RX_IDLE == g_ipcInfo.rxStatus)
    {
        memcpy(g_ipcInfo.uartBuf, g_ipcRxData, IPC_MAX_SIZE_LEN);
        g_ipcInfo.rxStatus = STATUS_RX_ONGOING;
    }
    else
    {
        if(ipcLostCnt++ < IPC_CHECK_DMA_STATUS_TIME)
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Ipc lost data, count=%u\r\n", ipcLostCnt);
        }
    }
}
/*************************************************
函数名称: IpcMsgRxFunction
函数功能: 接收到具体时间进行分发和处理
输入参数: 输入数据指针和长度和参数
输出参数: 执行结果
函数返回类型值： 
           IPC_NO_ERROR --- 执行正确
           IPC_ID_ERROR---  消息ID错误
编写者: liaoyonggang
编写日期 :2016/07/21
*************************************************/
static ipcErrorCode IpcMsgRxFunction(Msg msg)
{
    ipcErrorCode errorCode = IPC_NO_ERROR;
    MessageRxIndex rxIndex = MESSAGE_ID_RX_HEARTBEAT;

    for(rxIndex = MESSAGE_ID_RX_HEARTBEAT; rxIndex < MESSAGE_ID_RX_MAX; rxIndex++)
    {      
        if(g_rxMessageInfoMap[rxIndex].id == msg.event)
        {
            if(NULL != g_rxMessageInfoMap[rxIndex].IpcRxCallBack)
            {
                g_rxMessageInfoMap[rxIndex].IpcRxCallBack((uint8_t *)msg.lparam, msg.len); 
            }
            break;
        }
    }

    if(MESSAGE_ID_RX_MAX == rxIndex)
    {
         #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
         SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc rx function event error is 0x%x\r\n", msg.event);
         #endif
         errorCode = IPC_ID_ERROR;
    }

    return errorCode;
}

/*************************************************
函数名称: IpcPowerOnTxArm
函数功能: 开机分步骤发送命令给ARM
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/07/06
*************************************************/
void IpcPowerOnTxArm(void)
{
    WorkStatus status;
    FtmInfo  *pFtmInfo = FtmInitRead();

    if(HANDSHAKE_FAIL == g_commonInfo.handshakeStatus)
    {
        return;
    }

    switch(g_commonInfo.powerOnReportStatus)
    {
        case POWER_ON_REPORT_WAKEUP_STATUS:
        {
            if (GetCheckDtcEnable(MASK_VOL | MASK_ACC))
            {
                DiagDtcNotifyArmStartCheck();
            }
            g_commonInfo.powerOnReportStatus = POWER_ON_REPORT_CAR_INFO;
            break;
        }
        case POWER_ON_REPORT_CAR_INFO:
        {
            IpcTxCarInfo();
            g_commonInfo.powerOnReportStatus = POWER_ON_REPORT_HARD_CONFIG;
            break;
        }
        case POWER_ON_REPORT_HARD_CONFIG:
        {
            IpcTboxHardConfigInfo();
            g_commonInfo.powerOnReportStatus = POWER_ON_CAN_NET_STATUS;
            break;
        }
        case POWER_ON_CAN_NET_STATUS:
        {
            status = (g_commonInfo.Nm_State <= NM_STATE_READY_SLEEP) ? WORK_STATUS_INACTIVE : WORK_STATUS_ACTIVE;
            CanNetStatusNotifyFunction(status);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "can net stat is %d\r\n",status);
            if(pFtmInfo->ftmMode == FTM_MODE_ENTER)
            {
                g_commonInfo.powerOnReportStatus = POWER_ON_REPORT_ENTER_FTM;
            }
            else
            {
                g_commonInfo.powerOnReportStatus = POWER_ON_REPORT_IDLE;
            }
            break;
        }
        case POWER_ON_REPORT_ENTER_FTM:
        {
            IpcTboxNotifArmEnterFtm();
            g_commonInfo.powerOnReportStatus = POWER_ON_REPORT_IDLE;
            break;
        }
        default:
            break;
    }
}

/*************************************************
函数名称: IpcPeriodCheckClientStatus
函数功能: 检测客户端是否在线
输入参数: 
输出参数: 执行结果
函数返回类型值： 
编写者: zxl
编写日期 :2020/12/01，函数运行周期100ms
*************************************************/
void IpcPeriodCheckClientStatus(void)
{
    static uint8_t ipcClientTickTime = 0x00;
    FtmInfo *pFtmInfo = NULL;

    pFtmInfo = FtmInitRead();

    /*每10s检测一次客户端连接状态，若客户端离线，则主动发送一次唤醒源状态给server*/
    if(++ipcClientTickTime == IPC_PERIOD_CHECK_INTERVAL)
    {
        ipcClientTickTime = 0x00;
        if((g_commonInfo.armClinetStatus == ARM_CLINET_OFFLINE) && (pFtmInfo->ftmMode == FTM_MODE_EXIT))
        {
            IpcTxWakeupStatus();
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Ipc Check Client Status offline, heartFail Count=%d, armFailCount=%d\r\n", g_commonInfo.heartFailCount, g_commonInfo.armFailCount);
        }
    }
}

/*************************************************
函数名称: IpcPeriodFunctionCallback
函数功能: 接收到具体时间进行分发和处理
输入参数: 输入数据指针和长度和参数
输出参数: 执行结果
函数返回类型值： 
           IPC_NO_ERROR --- 执行正确
           IPC_ID_ERROR---  消息ID错误
编写者: liaoyonggang
编写日期 :2016/07/21
*************************************************/
void IpcPeriodFunction(void)
{
    Msg msg;
    
    msg.event  = EVENT_ID_HEARTBEAT_TIMEOUT;
    msg.len    = 0;
    msg.lparam = 0;

    IpcPowerOnTxArm();
    IpcPeriodCheckClientStatus();

    if(HANDSHAKE_SUCESS == g_commonInfo.handshakeStatus)
    {
        g_ipcInfo.heartbeatTimeout++;
    }
    else
    {
        g_ipcInfo.handshakeTimeout++;
    }

    if (GetCheckDtcEnable(MASK_VOL | MASK_ACC | MASK_BUS_OFF) &&
        (g_ipcInfo.heartbeatTimeout >= IPC_HEARTBEAT_PERIOD_TIMEOUT ||
         g_ipcInfo.handshakeTimeout >= IPC_HANDSHAKE_PERIOD_TIMEOUT))
    {
        DiagModuleFailureCheck(DTC_DEVICE_ERROR_STATUS);
    }

    if(IPC_HANDSHAKE_PERIOD_TIMEOUT == g_ipcInfo.handshakeTimeout)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "ipc handshake timeout count=IPC_HANDSHAKE_PERIOD_TIMEOUT entered with actual count=%d\r\n", g_ipcInfo.handshakeTimeout);

        g_commonInfo.canReportInit = FALSE;
        g_ipcInfo.handshakeTimeout++;

        SystemSendMessage(TASK_ID_PM, msg); //重启模组
        IpcUartReInit(); //重置ipc串口
    }

    //周期性未接收ARM-->MCU心跳消息超时
    if(IPC_HEARTBEAT_PERIOD_TIMEOUT == g_ipcInfo.heartbeatTimeout)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc heartbeat pm timeout\r\n");

        g_ipcInfo.heartbeatFlag = FLAG_NOT_RX_HEARTBEAT;
        g_commonInfo.canReportInit = FALSE;
        g_commonInfo.heartFailCount++;
        g_commonInfo.handshakeStatus = HANDSHAKE_FAIL;
        g_ipcInfo.heartbeatTimeout = 0;

        SystemSendMessage(TASK_ID_PM, msg);  //重启模组
        IpcUartReInit(); //重置ipc串口
    }


    //在等待ARM侧的ACK应答
    if(FLAG_WAIT_ACK == g_ipcInfo.ackFlag)
    {
        g_ipcInfo.retransmissionTimeout++;
        if(g_nvTboxSelfConfigData.logAndIpc.retransmissionCount == g_ipcInfo.retransmissionTimeout)
        {

            SystemApiLogPrintf(LOG_WARING_OUTPUT, "ipc retry tx is %d\r\n", g_ipcInfo.retransmissionCount);
            g_ipcInfo.retransmissionTimeout = 0;
            g_ipcInfo.retransmissionCount++;

            //超时重发业务命令
            if(g_nvTboxSelfConfigData.logAndIpc.retransmissionCount >= g_ipcInfo.retransmissionCount)
            {
                //Uart33SendData(g_ipcInfo.ipcTx.len, g_ipcInfo.ipcTx.buffer);
                IpcSendData(g_ipcInfo.ipcTx.len, g_ipcInfo.ipcTx.buffer);
            }
            else
            {
                g_ipcInfo.txTid = g_ipcInfo.txTid - 1;
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc retry tx 3 time fail\r\n");
                IpcClearAckInfo();
            }
        }
    }
}

/*************************************************
函数名称: IpcMsgTxPowerOnReport
函数功能: MCU上报版本号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: benyulong
编写日期 :2019/12/11
*************************************************/
void IpcMsgTxPowerOnReport(void)
{    
    uint8_t tempBuffer[11];
    Msg msg;
    uint16_t temp16 = 0;
    uint32_t bootVersion = ReadValueOfFlashAddress(BOOT_VERSION_ADDR);
    tempBuffer[0] = (uint8_t)MCU_HR_MAIN_VERSION;
    tempBuffer[1] = (uint8_t)MCU_HR_SUB_VERSION;
    tempBuffer[2] = (uint8_t)MCU_SOFT_MAIN_VERSION;
    tempBuffer[3] = (uint8_t)MCU_SOFT_SUB_VERSION;
    tempBuffer[4] = (uint8_t)MCU_SOFT_PATCH_VERSION;
    if ((bootVersion & 0xFFFF) != 0xFFFF)
    {
        tempBuffer[5] = (bootVersion >> 8) & 0xff;
        tempBuffer[6] = bootVersion & 0xff;
    }
    else
    {
        tempBuffer[5] = 0;
        tempBuffer[6] = 0;
    }

    temp16 = (uint16_t)(g_commonInfo.bPlusAdcValueFilter/10);
    tempBuffer[9] = (uint8_t)(temp16 >> 8);
    tempBuffer[10] = (uint8_t)temp16;

    msg.event  = MESSAGE_TX_POWER_ON_REPORT;
    msg.len    = 11;
    msg.lparam = (uint32_t)&tempBuffer[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: IpcFunctionCallback
函数功能: 对ARM-->MCU接收事件和MCU-->ARM发送事件进行处理
输入参数: 无
输出参数: 执行结果
函数返回类型值： 
           IPC_NO_ERROR --- 转义执行正确
           IPC_WAIT_ACK_RX_EVNET--- 等待ACK命令接收到非ACK命令
           IPC_ID_ERROR --- 未找到对应的发送事件
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
ipcErrorCode IpcFunctionCallback(void)
{
    Msg msg;
    ipcErrorCode errorCode = IPC_NO_ERROR;
    uint8_t tempBuf[QUEUE_IPC_TX_MAX_LEN];

    memset(tempBuf, 0x00, QUEUE_IPC_TX_MAX_LEN);
    //ARM--->MCU有接收数据
    if(QUEUE_NO_ERROR == SystemApiReceiveMessage(&g_taskRxQueue[QUEUE_ID_IPC_RX_TASK], &msg, tempBuf, 0))
    {
        switch(msg.event)
        {
            case EVENT_ID_IPC_PERIOD_NOTIFY:
            {
                IpcPeriodFunction();
                break;
            }
            //接收到ARM其他命令
            default:
            {
                if((MESSAGE_RX_VEHICLE_REMOTE_DIAGNOSIS != msg.event)&&(MESSAGE_RX_BT_REQUESET != msg.event)&&(MESSAGE_RX_HEARTBEAT != msg.event))
                {
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ipc rx is 0x%x\r\n", msg.event);
                }
                errorCode = IpcMsgRxFunction(msg);
                break;
            }
        }
    }

    if(FLAG_WAIT_ACK == g_ipcInfo.ackFlag)
    {
        errorCode = IPC_NO_ERROR;
        return errorCode;
    }
    else
    {
        //MCU--->ARM有发送数据
        if(QUEUE_NO_ERROR == SystemApiReceiveMessage(&g_taskRxQueue[QUEUE_ID_IPC_TX_TASK], &msg, tempBuf, 0))
        {
            errorCode = IpcTxPackageFrameData(msg);
            if(IPC_NO_ERROR != errorCode)
            {
                #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ipc tx package frame data error is %d\r\n", errorCode);
                #endif
            }
        }
    }

    return errorCode;
}

/*********************************************
函数名称: IpcMsgRxECallStatusIndication
函数功能: 接收到ECall状态通知
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/12
*************************************************/
static void IpcMsgRxECallStatusIndication(uint8_t *para, uint16_t len)
{
    Msg msg;
    
    msg.event = EVENT_ID_ECALL_STATUS_NOTIFY;
    msg.len   = len;
    msg.lparam = (uint32_t)para;

    SystemSendMessage(TASK_ID_LED, msg);

}

/*************************************************
函数名称: IpcSendData
函数功能: 向IPC的对方发送数据．可以根据不同的IPC硬件选择不同的发送方式
输入参数: len-uint8_t为单位的数据长度，buf-需要发送的数据buffer
输出参数: 无
函数返回类型值：无 
编写者: RichardChen
编写日期 :2024/04/17
*************************************************/
void IpcSendData(uint16_t len, uint8_t *buf)
{
    UartIPCSendData(len, buf);
    g_ipcInfo.TxCount++;
}

/*************************************************
函数名称: IpcUartReInit
函数功能: MCU被唤醒后的重新初始化
输入参数: 无
输出参数: 无
函数返回类型值：无 
编写者: RichardChen
编写日期 :2024/04/17
*************************************************/
void IpcUartReInit(void)
{
    McuUartIPCReInit(IpcDataCallback, IpcErrorCallback);

    g_commonInfo.handshakeStatus = HANDSHAKE_FAIL;
    g_commonInfo.gnssIsOnline = GNSS_IS_OFFLINE;
    g_commonInfo.tspStatus  = WORK_STATUS_UNINITIALIZED;
    g_commonInfo.armClinetStatus = ARM_CLINET_OFFLINE;
    g_commonInfo.armClientCount = 0;
    g_commonInfo.heartFailCount  = 0;
    g_commonInfo.armFailCount    = 0;
    g_commonInfo.powerOnReportStatus = POWER_ON_REPORT_IDLE;
    g_commonInfo.intoInterrupt = NOT_IN;
    g_commonInfo.udsCtrlNmPduStatus = 0x00;
    g_commonInfo.udsCtrlNormPduStatus = 0x00;
    g_commonInfo.bleKeyAuthStatus = 0x00;
    g_commonInfo.ecallStatus = ECALL_STATUS_INACTIVE;

    g_ipcInfo.ackFlag = FLAG_NOT_WATI_ACK;
    g_ipcInfo.txAck = FLAG_NOT_WATI_ACK;
    g_ipcInfo.retransmissionTimeout = 0;
    g_ipcInfo.retransmissionCount = 0;
    g_ipcInfo.heartbeatFlag = FLAG_NOT_RX_HEARTBEAT;
    g_ipcInfo.heartbeatTimeout = 0;
    g_ipcInfo.handshakeTimeout = 0;
    g_ipcInfo.rxStatus = STATUS_RX_IDLE;
    g_ipcInfo.txTid = 1;
    g_ipcInfo.rxTid = 1;
    g_ipcInfo.ipcTx.len = 0;
    g_ipcInfo.uartLen = 0;
    memset(g_ipcInfo.ipcTx.buffer, 0x00, sizeof(g_ipcInfo.ipcTx.buffer));
    memset(g_ipcInfo.uartBuf, 0x00, sizeof(g_ipcInfo.uartBuf));

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "IPC Uart reinit finished.\r\n");
}

/*
 * @brief: 发送更新响应
 * @param status: 更新状态
 * @param index: 更新索引
 * @return: queueErrorCode
 * @retval: QUEUE_NO_ERROR-发送成功
 * @retval: 其它-发送失败
 * @note: status为UPDATE_FRAME_STATUS_OK，则index为当前帧的索引，否则为请求的帧索引
 */
queueErrorCode IpcSendUpdateResponse(uint8_t status, uint16_t index)
{
    queueErrorCode errorCode = QUEUE_NO_ERROR;
    uint8_t tempBuffer[3];
    Msg msg;

    tempBuffer[0] = status;
    tempBuffer[1] = (uint8_t)(index >> 8);
    tempBuffer[2] = (uint8_t)(index & 0xFF);

    msg.event = MESSAGE_TX_UPDATE_CODE_RESPONSE;
    msg.len = 3;
    msg.lparam = (uint32_t)&tempBuffer[0];
    errorCode = SystemSendMessage(TASK_ID_IPC, msg);
    return errorCode;
}

/*
 * @brief: 检查UART接收状态
 * @param: 无
 * @return: 无
 * @note: 在IPC任务中调用，周期1~2ms
 */
void IpcCheckUartRxStatus(void)
{
    if(g_ipcInfo.uartRxFlag)
    {
        IpcUartRxFunction();
        McuUartIPCRxData();
        g_ipcInfo.uartRxFlag = false;
        g_ipcInfo.RxCount++;
    }
    if(g_ipcInfo.uartErrFlag)
    {
        McuUartIPCRxData();
        g_ipcInfo.uartErrFlag = false;
    }
}
